#!/usr/bin/env python3
"""
测试Streamlit前端功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试后端模块
        from ask import CodeAssistant
        print("✅ 后端模块导入成功")
        
        # 测试前端模块
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import StreamingAPIService, get_api_instance
        print("✅ API服务模块导入成功")
        
        from code_viewer import display_enhanced_search_results
        print("✅ 代码展示组件导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_api_service():
    """测试API服务"""
    print("\n🧪 测试API服务...")
    
    try:
        # 导入API服务
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import get_api_instance
        
        # 获取API实例
        api = get_api_instance()
        print("✅ API实例创建成功")
        
        # 测试初始化
        result = api.initialize()
        if result['success']:
            print("✅ API服务初始化成功")
            print(f"   配置信息: {result.get('config', {})}")
            return True
        else:
            print(f"❌ API服务初始化失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ API服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("\n🧪 测试搜索功能...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import get_api_instance
        
        api = get_api_instance()
        
        # 确保已初始化
        if not api._initialized:
            result = api.initialize()
            if not result['success']:
                print(f"❌ 初始化失败: {result.get('error')}")
                return False
        
        # 测试搜索
        query = "code chunking"
        print(f"   搜索查询: {query}")
        
        results = api.search_code(query)
        print(f"✅ 搜索完成，找到 {len(results)} 个结果")
        
        if results:
            # 显示第一个结果的详细信息
            first_result = results[0]
            print(f"   第一个结果:")
            print(f"     文件: {first_result.file_path}")
            print(f"     相似度: {first_result.similarity:.4f}")
            print(f"     检索方法: {first_result.retrieval_method}")
            print(f"     代码长度: {len(first_result.code)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streaming_response():
    """测试流式响应"""
    print("\n🧪 测试流式响应...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import get_api_instance, QueryRequest
        
        api = get_api_instance()
        
        # 确保已初始化
        if not api._initialized:
            result = api.initialize()
            if not result['success']:
                print(f"❌ 初始化失败: {result.get('error')}")
                return False
        
        # 创建测试请求
        request = QueryRequest(query="什么是代码分块？")
        print(f"   测试查询: {request.query}")
        
        # 测试流式响应
        chunk_count = 0
        search_results_found = False
        llm_content_found = False
        
        for chunk in api.query_with_streaming(request):
            chunk_type = chunk.get('type')
            chunk_count += 1
            
            if chunk_type == 'search_results':
                search_results_found = True
                results = chunk.get('content', [])
                print(f"   ✅ 收到搜索结果: {len(results)} 个")
                
            elif chunk_type == 'content':
                llm_content_found = True
                content = chunk.get('content', '')
                if len(content) > 50:  # 只显示前50个字符
                    print(f"   ✅ 收到LLM内容: {content[:50]}...")
                else:
                    print(f"   ✅ 收到LLM内容: {content}")
                
            elif chunk_type == 'done':
                print("   ✅ 流式响应完成")
                break
                
            elif chunk_type == 'error':
                print(f"   ❌ 流式响应错误: {chunk.get('content')}")
                return False
        
        print(f"   总共收到 {chunk_count} 个数据块")
        print(f"   搜索结果: {'✅' if search_results_found else '❌'}")
        print(f"   LLM内容: {'✅' if llm_content_found else '❌'}")
        
        return search_results_found
        
    except Exception as e:
        print(f"❌ 流式响应测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Streamlit前端功能...")
    print("=" * 50)
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    # 检查必要文件
    required_files = [
        "frontend/api_service.py",
        "frontend/app.py", 
        "frontend/code_viewer.py",
        "ask.py",
        "config.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件存在")
    print()
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("API服务", test_api_service),
        ("搜索功能", test_search_functionality),
        ("流式响应", test_streaming_response)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！前端应用已准备就绪。")
        print("\n启动命令:")
        print("   ./start_streamlit.sh")
        print("   或者: cd frontend && streamlit run app.py")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
