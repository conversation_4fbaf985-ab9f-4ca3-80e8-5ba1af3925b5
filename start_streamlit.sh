#!/bin/bash

# 启动Streamlit前端应用脚本

# 设置离线模式环境变量
export TRANSFORMERS_OFFLINE=1
export HF_HUB_OFFLINE=1
export HF_HUB_DISABLE_TELEMETRY=1

echo "🔧 Set offline mode environment variables:"
echo "   TRANSFORMERS_OFFLINE=$TRANSFORMERS_OFFLINE"
echo "   HF_HUB_OFFLINE=$HF_HUB_OFFLINE"
echo "   HF_HUB_DISABLE_TELEMETRY=$HF_HUB_DISABLE_TELEMETRY"
echo ""

# 检查虚拟环境是否存在
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found. Please create it first:"
    echo "   python -m venv .venv"
    echo "   source .venv/bin/activate"
    echo "   pip install -e ."
    exit 1
fi

# 激活虚拟环境
echo "🐍 Activating virtual environment..."
source .venv/bin/activate

# 检查是否有必要的模型文件
if [ ! -d "models" ]; then
    echo "⚠️  Warning: Local models directory not found."
    echo "   Please run download_models.py first to download models for offline use."
fi

# 检查是否有索引文件
if [ ! -f "faiss_index.bin" ] || [ ! -f "metadata.pkl" ]; then
    echo "⚠️  Warning: Index files not found."
    echo "   Please run 'python build_index.py' first to build the index."
fi

# 检查DEEPSEEK_API_KEY
if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "⚠️  Warning: DEEPSEEK_API_KEY environment variable not set."
    echo "   LLM functionality will be disabled."
    echo "   Please set DEEPSEEK_API_KEY in your .env file or environment."
fi

# 启动Streamlit应用
echo "🚀 Starting Streamlit application..."
echo "   URL: http://localhost:8501"
echo "   Press Ctrl+C to stop the application"
echo ""

# 确保在项目根目录启动，这样前端可以访问索引文件
python -m streamlit run frontend/app.py --server.port 8501 --server.headless true
