#!/usr/bin/env python3
"""
Streamlit前端演示脚本
快速启动并展示主要功能
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'streamlit',
        'plotly', 
        'pandas',
        'torch',
        'transformers'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"  ❌ {package}")
    
    if missing:
        print(f"\n⚠️  缺少依赖包: {missing}")
        print("请运行: pip install -e .")
        return False
    
    return True

def check_files():
    """检查必要文件"""
    print("\n📁 检查文件...")
    
    required_files = [
        "frontend/app.py",
        "frontend/api_service.py", 
        "frontend/code_viewer.py",
        "ask.py",
        "config.json"
    ]
    
    missing = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            missing.append(file_path)
            print(f"  ❌ {file_path}")
    
    if missing:
        print(f"\n⚠️  缺少文件: {missing}")
        return False
    
    return True

def check_models():
    """检查模型文件"""
    print("\n🤖 检查模型...")

    model_paths = [
        "models",
        "faiss_index.bin",
        "metadata.pkl"
    ]

    missing_critical = []
    for path in model_paths:
        if Path(path).exists():
            print(f"  ✅ {path}")
        else:
            if path in ["faiss_index.bin", "metadata.pkl"]:
                missing_critical.append(path)
                print(f"  ❌ {path} (必需)")
            else:
                print(f"  ⚠️  {path} (可选)")

    if missing_critical:
        print(f"\n⚠️  缺少关键文件: {missing_critical}")
        print("这些文件是系统运行必需的。")
        return False

    return True

def run_tests():
    """运行快速测试"""
    print("\n🧪 运行快速测试...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_frontend.py"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("  ✅ 所有测试通过")
            return True
        else:
            print("  ❌ 测试失败")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("  ⚠️  测试超时，但可能仍然可用")
        return True
    except Exception as e:
        print(f"  ❌ 测试异常: {e}")
        return False

def start_streamlit():
    """启动Streamlit应用"""
    print("\n🚀 启动Streamlit应用...")
    
    try:
        # 检查端口是否可用
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8501))
        sock.close()
        
        if result == 0:
            print("  ⚠️  端口8501已被占用")
            print("  请手动停止其他Streamlit应用或使用不同端口")
            return False
        
        # 启动应用
        print("  📡 启动服务器...")
        print("  🌐 URL: http://localhost:8501")
        print("  ⏹️  按 Ctrl+C 停止服务")
        print()
        
        # 等待几秒后自动打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:8501')
                print("  🌐 已自动打开浏览器")
            except:
                print("  ⚠️  无法自动打开浏览器，请手动访问 http://localhost:8501")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动Streamlit（从项目根目录）
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "frontend/app.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ], cwd=Path.cwd())
        
        return True
        
    except KeyboardInterrupt:
        print("\n  ⏹️  应用已停止")
        return True
    except Exception as e:
        print(f"  ❌ 启动失败: {e}")
        return False

def build_index():
    """构建代码索引"""
    print("\n🔨 构建代码索引...")

    try:
        print("  📝 正在分析代码文件...")
        result = subprocess.run([
            sys.executable, "build_index.py"
        ], capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("  ✅ 索引构建成功")
            return True
        else:
            print("  ❌ 索引构建失败")
            print(f"  错误: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("  ⚠️  索引构建超时，请手动运行: python build_index.py")
        return False
    except Exception as e:
        print(f"  ❌ 索引构建异常: {e}")
        return False

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 使用提示:")
    print("  1. 首次使用请点击'🚀 初始化系统'")
    print("  2. 在输入框中输入问题，如：'代码分块算法是如何工作的？'")
    print("  3. 查看搜索结果的相似度分数和代码片段")
    print("  4. 观察AI回答的流式输出过程")
    print("  5. 使用不同标签页查看分析结果")
    print()
    print("📚 示例问题:")
    print("  - 代码分块算法是如何工作的？")
    print("  - 如何实现语义搜索功能？")
    print("  - UniXcoder模型的主要特点是什么？")
    print("  - 混合检索器的实现原理")
    print("  - 如何优化检索性能？")

def main():
    """主函数"""
    print("🎯 UniXcoder RAG Streamlit前端演示")
    print("=" * 50)
    
    # 检查环境
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装依赖")
        return False
    
    if not check_files():
        print("\n❌ 文件检查失败，请确保所有文件存在")
        return False

    # 检查模型和索引文件
    if not check_models():
        print("\n🔨 需要构建代码索引才能使用系统")
        print("索引构建选项:")
        print("  1. 自动构建 (推荐)")
        print("  2. 手动构建")
        print("  3. 跳过 (系统将无法正常工作)")
        print()
        print("❓ 请选择 (1/2/3): ", end="")
        try:
            choice = input().strip()
            if choice == '1' or choice == '':
                if not build_index():
                    print("\n❌ 自动构建失败")
                    print("💡 请尝试手动构建: python build_index.py")
                    print("   或者: python quick_build_index.py")
                    return False
            elif choice == '2':
                print("\n📝 手动构建命令:")
                print("  python build_index.py")
                print("  或者: python quick_build_index.py")
                print("\n构建完成后重新运行此脚本")
                return False
            else:
                print("\n⚠️  跳过索引构建，系统将无法正常工作")
                print("如需正常使用，请稍后运行: python build_index.py")
        except KeyboardInterrupt:
            print("\n⏹️  已取消")
            return False
    
    # 询问是否运行测试
    print("\n❓ 是否运行快速测试？(y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '']:
            if not run_tests():
                print("\n⚠️  测试失败，但仍可尝试启动应用")
    except KeyboardInterrupt:
        print("\n⏹️  已取消")
        return False
    
    # 显示使用提示
    show_usage_tips()
    
    # 询问是否启动
    print("\n❓ 是否启动Streamlit应用？(y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '']:
            return start_streamlit()
        else:
            print("\n📝 手动启动命令:")
            print("   ./start_streamlit.sh")
            print("   或者: cd frontend && streamlit run app.py")
            return True
    except KeyboardInterrupt:
        print("\n⏹️  已取消")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  演示已停止")
        sys.exit(0)
