#!/usr/bin/env python3
"""
测试内容保留功能
"""

import sys
import os
sys.path.append('.')

from datetime import datetime

class MockSessionState:
    """模拟Streamlit session state"""
    def __init__(self):
        self._state = {}

    def __getattr__(self, key):
        return self._state.get(key, None)

    def __setattr__(self, key, value):
        if key.startswith('_'):
            super().__setattr__(key, value)
        else:
            self._state[key] = value

    def get(self, key, default=None):
        return self._state.get(key, default)

    def keys(self):
        return self._state.keys()

    def __delitem__(self, key):
        if key in self._state:
            del self._state[key]

def start_new_conversation(session_state):
    """开始新的对话"""
    import uuid
    conversation_id = str(uuid.uuid4())
    session_state.current_conversation_id = conversation_id
    session_state.llm_context = []
    session_state.conversation_history = []

    # 清除所有相关的状态标记
    session_state.streaming_running = False
    session_state.streaming_completed = False
    session_state.streaming_finalized = False
    session_state.last_query = ""

    # 清除所有对话完成标记
    keys_to_remove = [key for key in session_state.keys() if key.startswith("conversation_completed_")]
    for key in keys_to_remove:
        del session_state._state[key]

def add_to_conversation(session_state, query, response, search_results=None):
    """添加对话到历史"""
    conversation_item = {
        'id': len(session_state.conversation_history),
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'query': query,
        'response': response,
        'search_results': search_results or [],
        'conversation_id': session_state.current_conversation_id
    }
    session_state.conversation_history.append(conversation_item)

    # 添加到LLM上下文
    if not hasattr(session_state, 'llm_context') or session_state.llm_context is None:
        session_state.llm_context = []

    session_state.llm_context.extend([
        {"role": "user", "content": query},
        {"role": "assistant", "content": response}
    ])

def test_content_retention():
    """测试内容保留功能"""
    print("🧪 测试内容保留功能...")

    # 创建模拟session state
    session_state = MockSessionState()

    # 初始化会话状态
    session_state.conversation_history = []
    session_state.current_conversation_id = None
    session_state.llm_context = []
    
    try:
        # 测试1: 开始新对话
        print("\n📝 测试1: 开始新对话")
        start_new_conversation(session_state)

        if session_state.current_conversation_id:
            print("✅ 新对话ID创建成功")
        else:
            print("❌ 新对话ID创建失败")
            return False

        # 测试2: 添加对话内容
        print("\n📝 测试2: 添加对话内容")
        query1 = "什么是Python？"
        response1 = "Python是一种高级编程语言，具有简洁的语法和强大的功能。"
        search_results1 = [{"file": "test.py", "content": "print('Hello Python')"}]

        add_to_conversation(session_state, query1, response1, search_results1)

        if len(session_state.conversation_history) == 1:
            print("✅ 对话内容添加成功")
            print(f"   对话历史长度: {len(session_state.conversation_history)}")
            print(f"   LLM上下文长度: {len(session_state.llm_context)}")
        else:
            print("❌ 对话内容添加失败")
            return False

        # 测试3: 添加第二轮对话
        print("\n📝 测试3: 添加第二轮对话")
        query2 = "Python有什么特点？"
        response2 = "Python的主要特点包括：1. 语法简洁 2. 跨平台 3. 丰富的库生态"

        add_to_conversation(session_state, query2, response2)

        if len(session_state.conversation_history) == 2:
            print("✅ 第二轮对话添加成功")
            print(f"   对话历史长度: {len(session_state.conversation_history)}")
            print(f"   LLM上下文长度: {len(session_state.llm_context)}")
        else:
            print("❌ 第二轮对话添加失败")
            return False
        
        # 测试4: 验证内容保留
        print("\n📝 测试4: 验证内容保留")
        
        # 检查对话历史
        for i, item in enumerate(session_state.conversation_history):
            print(f"   对话 {i+1}:")
            print(f"     查询: {item['query'][:50]}...")
            print(f"     回答: {item['response'][:50]}...")
            print(f"     时间戳: {item['timestamp']}")

            if not item['response'] or not item['response'].strip():
                print("❌ 发现空的回答内容")
                return False

        # 检查LLM上下文
        if len(session_state.llm_context) == 4:  # 2轮对话 = 4条消息
            print("✅ LLM上下文正确保留")
            for i, msg in enumerate(session_state.llm_context):
                print(f"     消息 {i+1}: {msg['role']} - {msg['content'][:30]}...")
        else:
            print(f"❌ LLM上下文长度错误: {len(session_state.llm_context)}")
            return False

        # 测试5: 模拟页面重新运行
        print("\n📝 测试5: 模拟页面重新运行后内容保留")

        # 保存当前状态
        saved_history = session_state.conversation_history.copy()
        saved_context = session_state.llm_context.copy()
        saved_id = session_state.current_conversation_id

        # 模拟页面重新运行（但保留session state）
        # 在实际Streamlit中，session state会被保留

        if (len(session_state.conversation_history) == len(saved_history) and
            len(session_state.llm_context) == len(saved_context) and
            session_state.current_conversation_id == saved_id):
            print("✅ 页面重新运行后内容正确保留")
        else:
            print("❌ 页面重新运行后内容丢失")
            return False

        # 测试6: 新对话功能
        print("\n📝 测试6: 新对话功能")
        old_id = session_state.current_conversation_id
        start_new_conversation(session_state)

        if (session_state.current_conversation_id != old_id and
            len(session_state.conversation_history) == 0 and
            len(session_state.llm_context) == 0):
            print("✅ 新对话功能正常，历史已清除")
        else:
            print("❌ 新对话功能异常")
            return False
        
        print("\n🎉 所有测试通过！内容保留功能正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_conversation_state_management():
    """测试对话状态管理"""
    print("\n🔧 测试对话状态管理...")

    try:
        # 创建模拟session state
        session_state = MockSessionState()

        # 模拟对话完成状态
        conversation_id = "test-conversation-123"
        session_state.current_conversation_id = conversation_id

        # 设置完成标记
        session_state._state[f"conversation_completed_{conversation_id}"] = True

        # 检查状态
        completed = session_state.get(f"conversation_completed_{conversation_id}", False)

        if completed:
            print("✅ 对话完成状态正确设置")
        else:
            print("❌ 对话完成状态设置失败")
            return False

        # 测试状态清除
        start_new_conversation(session_state)

        # 检查旧状态是否被清除
        old_completed = session_state.get(f"conversation_completed_{conversation_id}", False)

        if not old_completed:
            print("✅ 旧对话状态正确清除")
        else:
            print("❌ 旧对话状态清除失败")
            return False

        return True

    except Exception as e:
        print(f"❌ 状态管理测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_content_retention()
    success2 = test_conversation_state_management()
    
    overall_success = success1 and success2
    print(f"\n🎯 总体测试结果: {'成功' if overall_success else '失败'}")
    sys.exit(0 if overall_success else 1)
