#!/usr/bin/env python3
"""
基础前端功能测试
只测试核心功能，不包括LLM调用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试后端模块
        from ask import CodeAssistant
        print("✅ 后端模块导入成功")
        
        # 测试前端模块
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import StreamingAPIService, get_api_instance
        print("✅ API服务模块导入成功")
        
        from code_viewer import display_enhanced_search_results
        print("✅ 代码展示组件导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_api_initialization():
    """测试API初始化"""
    print("\n🧪 测试API初始化...")
    
    try:
        # 导入API服务
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import get_api_instance
        
        # 获取API实例
        api = get_api_instance()
        print("✅ API实例创建成功")
        
        # 测试初始化
        result = api.initialize()
        if result['success']:
            print("✅ API服务初始化成功")
            print(f"   配置信息: {result.get('config', {})}")
            return True
        else:
            print(f"❌ API服务初始化失败: {result.get('error')}")
            # 检查是否是索引文件问题
            if 'Index files not found' in result.get('error', ''):
                print("💡 这是预期的错误，因为需要先构建索引")
                return True  # 这种情况下认为测试通过
            return False
            
    except Exception as e:
        print(f"❌ API初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_functionality():
    """测试搜索功能（如果索引存在）"""
    print("\n🧪 测试搜索功能...")
    
    # 检查索引文件是否存在
    if not Path("faiss_index.bin").exists() or not Path("metadata.pkl").exists():
        print("⚠️  索引文件不存在，跳过搜索测试")
        return True
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import get_api_instance
        
        api = get_api_instance()
        
        # 初始化
        result = api.initialize()
        if not result['success']:
            print(f"❌ 初始化失败: {result.get('error')}")
            return False
        
        # 测试搜索
        query = "code chunking"
        print(f"   搜索查询: {query}")
        
        results = api.search_code(query)
        print(f"✅ 搜索完成，找到 {len(results)} 个结果")
        
        if results:
            # 显示第一个结果的详细信息
            first_result = results[0]
            print(f"   第一个结果:")
            print(f"     文件: {first_result.file_path}")
            print(f"     相似度: {first_result.similarity:.4f}")
            print(f"     检索方法: {first_result.retrieval_method}")
            print(f"     代码长度: {len(first_result.code)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_components():
    """测试Streamlit组件"""
    print("\n🧪 测试Streamlit组件...")
    
    try:
        import streamlit as st
        print("✅ Streamlit导入成功")
        
        import plotly.graph_objects as go
        import plotly.express as px
        print("✅ Plotly导入成功")
        
        import pandas as pd
        print("✅ Pandas导入成功")
        
        # 测试前端组件导入
        sys.path.insert(0, str(project_root / "frontend"))
        from code_viewer import display_enhanced_search_results
        from api_service import SearchResult
        print("✅ 前端组件导入成功")
        
        # 创建测试数据
        test_result = SearchResult(
            similarity=0.85,
            file_path="test/example.py",
            start_line=10,
            end_line=20,
            code="def test_function():\n    return True",
            signature="test_function()",
            parent_class="TestClass",
            retrieval_method="semantic"
        )
        print("✅ 测试数据创建成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 组件导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        return False

def check_files():
    """检查必要文件"""
    print("\n📁 检查文件...")
    
    required_files = [
        "frontend/app.py",
        "frontend/api_service.py", 
        "frontend/code_viewer.py",
        "ask.py",
        "config.json"
    ]
    
    missing = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            missing.append(file_path)
            print(f"  ❌ {file_path}")
    
    # 检查索引文件
    index_files = ["faiss_index.bin", "metadata.pkl"]
    for file_path in index_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ⚠️  {file_path} (需要构建)")
    
    return len(missing) == 0

def main():
    """主测试函数"""
    print("🚀 基础前端功能测试")
    print("=" * 40)
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    # 检查文件
    if not check_files():
        print("\n❌ 文件检查失败")
        return False
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("API初始化", test_api_initialization),
        ("搜索功能", test_search_functionality),
        ("Streamlit组件", test_streamlit_components)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 40)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 基础功能测试通过！")
        
        # 检查索引文件
        if Path("faiss_index.bin").exists() and Path("metadata.pkl").exists():
            print("\n✅ 索引文件存在，可以直接启动应用:")
            print("   python demo_streamlit.py")
            print("   或者: ./start_streamlit.sh")
        else:
            print("\n⚠️  索引文件不存在，需要先构建:")
            print("   python quick_build_index.py")
            print("   或者: python build_index.py")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
