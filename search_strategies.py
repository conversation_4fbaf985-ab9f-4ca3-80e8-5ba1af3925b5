"""
搜索策略模式实现

将复杂的搜索逻辑拆分为可组合的策略模块，提高可维护性和可测试性。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np

from logger import get_logger
from exceptions import RetrievalError, RankingError, FusionError, error_handler, handle_errors
from index_manager import get_index_manager
from model_manager import get_model_manager

logger = get_logger(__name__)


class SearchMethod(Enum):
    """搜索方法枚举"""
    SEMANTIC = "semantic"
    BM25 = "bm25"
    STRUCTURED = "structured"
    HYBRID = "hybrid"


@dataclass
class SearchResult:
    """搜索结果"""
    chunk_id: str
    chunk: Dict[str, Any]
    score: float
    method: SearchMethod
    explanation: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SearchContext:
    """搜索上下文"""
    original_query: str
    expanded_query: Optional[str] = None
    query_embedding: Optional[np.ndarray] = None
    filters: Optional[Dict[str, Any]] = None
    top_k: int = 10


class BaseSearchStrategy(ABC):
    """搜索策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(f"{__name__}.{name}")
    
    @abstractmethod
    def search(self, context: SearchContext) -> List[SearchResult]:
        """执行搜索"""
        pass
    
    @abstractmethod
    def build_index(self, chunks: List[Dict[str, Any]]) -> None:
        """构建索引"""
        pass
    
    def validate_context(self, context: SearchContext) -> bool:
        """验证搜索上下文"""
        return context.original_query is not None and len(context.original_query.strip()) > 0


class SemanticSearchStrategy(BaseSearchStrategy):
    """语义搜索策略"""
    
    def __init__(self, semantic_retriever):
        super().__init__("semantic")
        self.semantic_retriever = semantic_retriever
    
    @handle_errors(RetrievalError, logger=logger)
    def search(self, context: SearchContext) -> List[SearchResult]:
        """执行语义搜索"""
        if not self.validate_context(context):
            raise RetrievalError("Invalid search context for semantic search")
        
        try:
            # 获取查询嵌入
            if context.query_embedding is None:
                query_embedding = self.semantic_retriever.get_query_embedding(
                    context.original_query
                )
                context.query_embedding = query_embedding
            else:
                query_embedding = context.query_embedding
            
            # 执行搜索
            raw_results = self.semantic_retriever.search_once(query_embedding)
            
            # 转换结果格式
            results = []
            for i, res in enumerate(raw_results):
                result = SearchResult(
                    chunk_id=f"semantic_{i}",
                    chunk=res['chunk'],
                    score=res['similarity'],
                    method=SearchMethod.SEMANTIC,
                    explanation=f"Semantic similarity: {res['similarity']:.4f}"
                )
                results.append(result)
            
            self.logger.debug(f"Semantic search returned {len(results)} results")
            return results
            
        except Exception as e:
            raise RetrievalError(
                f"Semantic search failed for query: {context.original_query}",
                query=context.original_query,
                cause=e
            )
    
    def build_index(self, chunks: List[Dict[str, Any]]) -> None:
        """构建语义索引"""
        # 语义索引通常已经在外部构建
        pass


class BM25SearchStrategy(BaseSearchStrategy):
    """BM25搜索策略"""
    
    def __init__(self, bm25_retriever):
        super().__init__("bm25")
        self.bm25_retriever = bm25_retriever
    
    @handle_errors(RetrievalError, logger=logger)
    def search(self, context: SearchContext) -> List[SearchResult]:
        """执行BM25搜索"""
        if not self.validate_context(context):
            raise RetrievalError("Invalid search context for BM25 search")
        
        try:
            # 使用扩展查询（如果有）
            query = context.expanded_query or context.original_query
            raw_results = self.bm25_retriever.search(query)
            
            # 转换结果格式
            results = []
            for i, res in enumerate(raw_results):
                result = SearchResult(
                    chunk_id=f"bm25_{i}",
                    chunk=res,
                    score=res.get('score', 0.0),
                    method=SearchMethod.BM25,
                    explanation=f"BM25 score: {res.get('score', 0.0):.4f}"
                )
                results.append(result)
            
            self.logger.debug(f"BM25 search returned {len(results)} results")
            return results
            
        except Exception as e:
            raise RetrievalError(
                f"BM25 search failed for query: {context.original_query}",
                query=context.original_query,
                cause=e
            )
    
    def build_index(self, chunks: List[Dict[str, Any]]) -> None:
        """构建BM25索引"""
        self.bm25_retriever.build_index(chunks)


class StructuredSearchStrategy(BaseSearchStrategy):
    """结构化搜索策略"""
    
    def __init__(self, structured_retriever):
        super().__init__("structured")
        self.structured_retriever = structured_retriever
    
    @handle_errors(RetrievalError, logger=logger)
    def search(self, context: SearchContext) -> List[SearchResult]:
        """执行结构化搜索"""
        if not self.validate_context(context):
            raise RetrievalError("Invalid search context for structured search")
        
        try:
            raw_results = self.structured_retriever.search(context.original_query)
            
            # 转换结果格式
            results = []
            for i, res in enumerate(raw_results):
                result = SearchResult(
                    chunk_id=f"structured_{i}",
                    chunk=res,
                    score=res.get('score', 0.0),
                    method=SearchMethod.STRUCTURED,
                    explanation=f"Structured match: {res.get('score', 0.0):.4f}"
                )
                results.append(result)
            
            self.logger.debug(f"Structured search returned {len(results)} results")
            return results
            
        except Exception as e:
            raise RetrievalError(
                f"Structured search failed for query: {context.original_query}",
                query=context.original_query,
                cause=e
            )
    
    def build_index(self, chunks: List[Dict[str, Any]]) -> None:
        """构建结构化索引"""
        self.structured_retriever.build_index(chunks)


class SearchOrchestrator:
    """搜索编排器"""
    
    def __init__(self, config):
        self.config = config
        self.strategies: Dict[SearchMethod, BaseSearchStrategy] = {}
        self.fusion_strategy = None
        self.reranking_strategy = None
        self.logger = get_logger(__name__)
    
    def register_strategy(self, method: SearchMethod, strategy: BaseSearchStrategy):
        """注册搜索策略"""
        self.strategies[method] = strategy
        self.logger.info(f"Registered {method.value} search strategy")
    
    def set_fusion_strategy(self, fusion_strategy):
        """设置结果融合策略"""
        self.fusion_strategy = fusion_strategy
    
    def set_reranking_strategy(self, reranking_strategy):
        """设置重排序策略"""
        self.reranking_strategy = reranking_strategy
    
    @handle_errors(RetrievalError, logger=logger)
    def search(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """执行混合搜索"""
        if top_k is None:
            top_k = self.config.retrieval.max_results
        
        # 创建搜索上下文
        context = SearchContext(
            original_query=query,
            top_k=top_k
        )
        
        # 查询扩展
        context = self._expand_query(context)
        
        # 多路召回
        all_results = self._multi_recall(context)
        
        # 结果融合
        fused_results = self._fuse_results(all_results)
        
        # 重排序
        if self.config.retrieval.use_reranking and self.reranking_strategy:
            final_results = self._rerank_results(context, fused_results)
        else:
            final_results = fused_results
        
        # 返回Top-K结果
        return self._format_final_results(final_results[:top_k])
    
    def _expand_query(self, context: SearchContext) -> SearchContext:
        """查询扩展"""
        # 简化的查询扩展逻辑
        if hasattr(self, 'query_expander') and self.query_expander:
            try:
                expanded = self.query_expander.expand_query(context.original_query)
                context.expanded_query = expanded.get('expanded_query', context.original_query)
            except Exception as e:
                self.logger.warning(f"Query expansion failed: {e}")
                context.expanded_query = context.original_query
        else:
            context.expanded_query = context.original_query
        
        return context
    
    def _multi_recall(self, context: SearchContext) -> Dict[SearchMethod, List[SearchResult]]:
        """多路召回"""
        all_results = {}
        
        # 并行执行各种搜索策略
        for method, strategy in self.strategies.items():
            try:
                results = strategy.search(context)
                all_results[method] = results
                self.logger.debug(f"{method.value} search returned {len(results)} results")
            except Exception as e:
                self.logger.error(f"{method.value} search failed: {e}")
                all_results[method] = []
        
        return all_results
    
    def _fuse_results(self, all_results: Dict[SearchMethod, List[SearchResult]]) -> List[SearchResult]:
        """结果融合"""
        if self.fusion_strategy:
            try:
                return self.fusion_strategy.fuse(all_results)
            except Exception as e:
                self.logger.error(f"Custom fusion strategy failed: {e}")
        
        # 默认融合策略：简单合并和去重
        return self._default_fusion(all_results)
    
    def _default_fusion(self, all_results: Dict[SearchMethod, List[SearchResult]]) -> List[SearchResult]:
        """默认融合策略"""
        try:
            fused_results = []
            seen_chunks = set()
            
            # 按方法权重合并结果
            method_weights = {
                SearchMethod.SEMANTIC: 0.4,
                SearchMethod.BM25: 0.35,
                SearchMethod.STRUCTURED: 0.25
            }
            
            for method, results in all_results.items():
                weight = method_weights.get(method, 0.33)
                
                for result in results:
                    # 简单的去重策略
                    chunk_hash = hash(str(result.chunk))
                    if chunk_hash not in seen_chunks:
                        seen_chunks.add(chunk_hash)
                        # 调整分数
                        result.score *= weight
                        fused_results.append(result)
            
            # 按分数排序
            fused_results.sort(key=lambda x: x.score, reverse=True)
            
            return fused_results
            
        except Exception as e:
            raise FusionError(f"Default fusion failed: {str(e)}", cause=e)
    
    def _rerank_results(self, context: SearchContext, results: List[SearchResult]) -> List[SearchResult]:
        """重排序"""
        try:
            return self.reranking_strategy.rerank(context, results)
        except Exception as e:
            self.logger.error(f"Reranking failed: {e}")
            return results
    
    def _format_final_results(self, results: List[SearchResult]) -> List[Dict[str, Any]]:
        """格式化最终结果"""
        formatted_results = []
        
        for result in results:
            formatted_result = {
                'chunk': result.chunk,
                'score': result.score,
                'retrieval_method': result.method.value,
                'explanation': result.explanation
            }
            
            if result.metadata:
                formatted_result['metadata'] = result.metadata
            
            formatted_results.append(formatted_result)
        
        return formatted_results
    
    def build_index(self, chunks: List[Dict[str, Any]]) -> None:
        """构建所有索引"""
        for method, strategy in self.strategies.items():
            try:
                strategy.build_index(chunks)
                self.logger.info(f"Built {method.value} index successfully")
            except Exception as e:
                self.logger.error(f"Failed to build {method.value} index: {e}")


class BaseFusionStrategy(ABC):
    """结果融合策略基类"""
    
    @abstractmethod
    def fuse(self, all_results: Dict[SearchMethod, List[SearchResult]]) -> List[SearchResult]:
        """融合多路搜索结果"""
        pass


class WeightedFusionStrategy(BaseFusionStrategy):
    """加权融合策略"""
    
    def __init__(self, weights: Dict[SearchMethod, float]):
        self.weights = weights
        self.logger = get_logger(__name__)
    
    def fuse(self, all_results: Dict[SearchMethod, List[SearchResult]]) -> List[SearchResult]:
        """加权融合"""
        try:
            chunk_scores = {}
            chunk_info = {}
            
            for method, results in all_results.items():
                weight = self.weights.get(method, 0.33)
                
                for result in results:
                    chunk_key = str(result.chunk)  # 简化的chunk标识
                    
                    if chunk_key not in chunk_scores:
                        chunk_scores[chunk_key] = 0.0
                        chunk_info[chunk_key] = result
                    
                    chunk_scores[chunk_key] += result.score * weight
            
            # 创建融合结果
            fused_results = []
            for chunk_key, score in chunk_scores.items():
                result = chunk_info[chunk_key]
                result.score = score
                result.method = SearchMethod.HYBRID
                result.explanation = f"Hybrid score: {score:.4f}"
                fused_results.append(result)
            
            # 按分数排序
            fused_results.sort(key=lambda x: x.score, reverse=True)
            
            return fused_results
            
        except Exception as e:
            raise FusionError(f"Weighted fusion failed: {str(e)}", cause=e)


class BaseRerankingStrategy(ABC):
    """重排序策略基类"""
    
    @abstractmethod
    def rerank(self, context: SearchContext, results: List[SearchResult]) -> List[SearchResult]:
        """重排序结果"""
        pass


class CrossEncoderRerankingStrategy(BaseRerankingStrategy):
    """交叉编码器重排序策略"""
    
    def __init__(self, reranker):
        self.reranker = reranker
        self.logger = get_logger(__name__)
    
    def rerank(self, context: SearchContext, results: List[SearchResult]) -> List[SearchResult]:
        """使用交叉编码器重排序"""
        try:
            if len(results) <= 1:
                return results
            
            # 准备重排序输入
            pairs = []
            for result in results:
                chunk_text = self._extract_text(result.chunk)
                pairs.append([context.original_query, chunk_text])
            
            # 计算重排序分数
            rerank_scores = self.reranker.predict(pairs)
            
            # 更新结果分数
            for i, result in enumerate(results):
                result.score = rerank_scores[i]
                result.explanation = f"Reranked score: {rerank_scores[i]:.4f}"
            
            # 按新分数排序
            results.sort(key=lambda x: x.score, reverse=True)
            
            return results
            
        except Exception as e:
            raise RankingError(f"Cross-encoder reranking failed: {str(e)}", cause=e)
    
    def _extract_text(self, chunk: Dict[str, Any]) -> str:
        """从chunk中提取文本"""
        if isinstance(chunk, dict):
            return chunk.get('code', '') + ' ' + chunk.get('docstring', '')
        return str(chunk)