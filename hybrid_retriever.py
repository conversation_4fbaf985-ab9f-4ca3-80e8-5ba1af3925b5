"""
混合召回系统
集成语义向量、BM25关键词、结构化搜索的多路召回策略
"""

import re
import math
import jieba
import string
from typing import List, Dict, Any, Tuple, Set
from collections import defaultdict, Counter
from dataclasses import dataclass
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from config import get_config
from logger import get_logger, performance_monitor
from search_strategies import (
    SearchOrchestrator, SemanticSearchStrategy, BM25SearchStrategy, 
    StructuredSearchStrategy, SearchMethod, WeightedFusionStrategy,
    CrossEncoderRerankingStrategy
)
from exceptions import RetrievalError, error_handler

logger = get_logger(__name__)

@dataclass
class RetrievalResult:
    """检索结果"""
    chunk_id: int
    chunk: Dict[str, Any]
    score: float
    retrieval_method: str
    explanation: str

class QueryExpander:
    """查询扩展器"""
    
    def __init__(self):
        self.config = get_config()
        
        # 技术术语词典
        self.tech_synonyms = {
            'function': ['函数', 'method', '方法', 'def', 'func'],
            'class': ['类', 'object', '对象', 'cls'],
            'variable': ['变量', 'var', '变量名', 'param', 'parameter'],
            'loop': ['循环', 'for', 'while', '遍历', 'iterate'],
            'condition': ['条件', 'if', 'elif', '判断', 'check'],
            'import': ['导入', 'include', '引入', 'from'],
            'return': ['返回', 'output', '输出', 'result'],
            'error': ['错误', 'exception', '异常', 'bug', 'issue'],
            'test': ['测试', 'unit test', '单元测试', 'assert'],
            'database': ['数据库', 'db', 'sql', 'query', '查询'],
            'api': ['接口', 'interface', 'endpoint', '端点'],
            'file': ['文件', 'document', '文档', 'io'],
            'string': ['字符串', 'str', 'text', '文本'],
            'list': ['列表', 'array', '数组', 'sequence'],
            'dict': ['字典', 'dictionary', 'map', 'hash'],
        }
        
        # 编程概念扩展
        self.concept_expansions = {
            'search': ['搜索', '查找', 'find', 'lookup', 'retrieve', 'get'],
            'sort': ['排序', 'order', 'arrange', 'organize'],
            'filter': ['过滤', 'select', 'where', '筛选'],
            'parse': ['解析', 'analyze', 'decode', '分析'],
            'validate': ['验证', 'check', 'verify', '校验'],
            'convert': ['转换', 'transform', 'change', '变换'],
            'calculate': ['计算', 'compute', 'process', '处理'],
            'generate': ['生成', 'create', 'build', '构建'],
        }
    
    @performance_monitor("query_expansion")
    def expand_query(self, query: str) -> Dict[str, Any]:
        """扩展查询"""
        expanded = {
            'original': query,
            'cleaned': self._clean_query(query),
            'keywords': self._extract_keywords(query),
            'synonyms': self._get_synonyms(query),
            'concepts': self._expand_concepts(query),
            'code_patterns': self._extract_code_patterns(query)
        }
        
        # 生成扩展查询字符串
        all_terms = set([expanded['cleaned']])
        all_terms.update(expanded['keywords'])
        all_terms.update(expanded['synonyms'])
        all_terms.update(expanded['concepts'])
        
        expanded['expanded_query'] = ' '.join(all_terms)
        
        logger.debug(f"Query expanded: {query} -> {len(all_terms)} terms")
        return expanded
    
    def _clean_query(self, query: str) -> str:
        """清理查询字符串"""
        # 移除特殊字符，保留中英文和数字
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', query)
        return ' '.join(cleaned.split())
    
    def _extract_keywords(self, query: str) -> List[str]:
        """提取关键词"""
        # 英文分词
        english_words = re.findall(r'\b[a-zA-Z]+\b', query)
        
        # 中文分词
        chinese_words = []
        chinese_text = re.findall(r'[\u4e00-\u9fff]+', query)
        for text in chinese_text:
            chinese_words.extend(jieba.lcut(text))
        
        # 过滤停用词
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 
                     '是', '的', '了', '在', '有', '和', '或', '但是', '如何', '怎么', '什么'}
        
        keywords = []
        for word in english_words + chinese_words:
            if len(word) > 1 and word.lower() not in stop_words:
                keywords.append(word.lower())
        
        return list(set(keywords))
    
    def _get_synonyms(self, query: str) -> List[str]:
        """获取同义词"""
        synonyms = []
        query_lower = query.lower()
        
        for term, synonym_list in self.tech_synonyms.items():
            if term in query_lower or any(syn in query_lower for syn in synonym_list):
                synonyms.extend(synonym_list)
        
        return list(set(synonyms))
    
    def _expand_concepts(self, query: str) -> List[str]:
        """扩展概念"""
        concepts = []
        query_lower = query.lower()
        
        for concept, expansion_list in self.concept_expansions.items():
            if concept in query_lower or any(exp in query_lower for exp in expansion_list):
                concepts.extend(expansion_list)
        
        return list(set(concepts))
    
    def _extract_code_patterns(self, query: str) -> List[str]:
        """提取代码模式"""
        patterns = []
        
        # 检测函数调用模式
        if re.search(r'\w+\(\)', query):
            patterns.append('function_call')
        
        # 检测类实例化模式
        if re.search(r'\w+\(\w*\)', query):
            patterns.append('instantiation')
        
        # 检测属性访问模式
        if '.' in query:
            patterns.append('attribute_access')
        
        # 检测导入模式
        if any(word in query.lower() for word in ['import', 'from', '导入']):
            patterns.append('import_statement')
        
        return patterns

class BM25Retriever:
    """BM25关键词检索器"""
    
    def __init__(self, k1: float = 1.5, b: float = 0.75):
        self.k1 = k1
        self.b = b
        self.corpus = []
        self.doc_frequencies = {}
        self.idf_cache = {}
        self.doc_lengths = []
        self.avg_doc_length = 0
        self.metadata = []
    
    @performance_monitor("bm25_build_index")
    def build_index(self, chunks: List[Dict[str, Any]]):
        """构建BM25索引"""
        logger.info(f"Building BM25 index for {len(chunks)} chunks...")
        
        self.corpus = []
        self.metadata = chunks
        
        for chunk in chunks:
            # 构建文档：结合代码、注释、函数名等
            doc = self._build_document(chunk)
            self.corpus.append(doc)
        
        # 计算文档频率和IDF
        self._calculate_statistics()
        
        logger.info("BM25 index built successfully")
    
    def _build_document(self, chunk: Dict[str, Any]) -> str:
        """构建BM25文档"""
        doc_parts = []
        
        # 代码内容
        code = chunk.get('code', '')
        doc_parts.append(code)
        
        # 元数据
        metadata = chunk.get('metadata', {})
        
        # 函数/类名（重要性高，重复添加）
        if metadata.get('name'):
            name = metadata['name']
            doc_parts.extend([name] * 3)  # 提高权重
        
        # 文件路径中的信息
        if metadata.get('file_path'):
            file_name = metadata['file_path'].split('/')[-1].replace('.py', '')
            doc_parts.append(file_name)
        
        # 文档字符串
        if metadata.get('docstring'):
            doc_parts.append(metadata['docstring'])
        
        # 签名
        if metadata.get('signature'):
            doc_parts.append(metadata['signature'])
        
        # 父类信息
        if metadata.get('parent_class'):
            doc_parts.append(metadata['parent_class'])
        
        # 合并并清理
        doc = ' '.join(doc_parts)
        doc = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', doc)
        doc = ' '.join(doc.split())  # 标准化空格
        
        return doc.lower()
    
    def _calculate_statistics(self):
        """计算BM25统计信息"""
        # 分词并计算文档长度
        tokenized_corpus = []
        self.doc_lengths = []
        word_doc_count = defaultdict(set)
        
        for i, doc in enumerate(self.corpus):
            tokens = self._tokenize(doc)
            tokenized_corpus.append(tokens)
            self.doc_lengths.append(len(tokens))
            
            # 计算每个词出现的文档数
            for token in set(tokens):
                word_doc_count[token].add(i)
        
        self.tokenized_corpus = tokenized_corpus
        self.avg_doc_length = sum(self.doc_lengths) / len(self.doc_lengths)
        
        # 计算IDF
        num_docs = len(self.corpus)
        for word, doc_set in word_doc_count.items():
            df = len(doc_set)
            idf = math.log((num_docs - df + 0.5) / (df + 0.5))
            self.idf_cache[word] = max(idf, 0.01)  # 避免负数
    
    def _tokenize(self, text: str) -> List[str]:
        """分词"""
        # 英文分词
        english_tokens = re.findall(r'\b[a-zA-Z_]\w*\b', text)
        
        # 中文分词
        chinese_tokens = []
        chinese_parts = re.findall(r'[\u4e00-\u9fff]+', text)
        for part in chinese_parts:
            chinese_tokens.extend(jieba.lcut(part))
        
        # 数字和标识符
        identifier_tokens = re.findall(r'\b\w+\b', text)
        
        all_tokens = english_tokens + chinese_tokens + identifier_tokens
        return [token.lower() for token in all_tokens if len(token) > 1]
    
    @performance_monitor("bm25_search")
    def search(self, query: str, top_k: int = 50) -> List[Dict]:
        """BM25搜索"""
        if not self.corpus:
            return []
        
        query_tokens = self._tokenize(query)
        if not query_tokens:
            return []
        
        scores = []
        for i, doc_tokens in enumerate(self.tokenized_corpus):
            score = self._calculate_bm25_score(query_tokens, doc_tokens, i)
            scores.append((score, i))
        
        # 按分数排序
        scores.sort(reverse=True)
        
        results = []
        for score, idx in scores[:top_k]:
            if score > 0:
                result = RetrievalResult(
                    chunk_id=idx,
                    chunk=self.metadata[idx],
                    score=score,
                    retrieval_method="bm25",
                    explanation=f"BM25 keyword match score: {score:.4f}"
                )
                results.append(result.__dict__)
        
        return results
    
    def _calculate_bm25_score(self, query_tokens: List[str], doc_tokens: List[str], doc_idx: int) -> float:
        """计算BM25分数"""
        doc_len = self.doc_lengths[doc_idx]
        score = 0.0
        
        # 计算词频
        doc_token_counts = Counter(doc_tokens)
        
        for token in query_tokens:
            if token in doc_token_counts:
                tf = doc_token_counts[token]
                idf = self.idf_cache.get(token, 0.01)
                
                # BM25公式
                numerator = tf * (self.k1 + 1)
                denominator = tf + self.k1 * (1 - self.b + self.b * (doc_len / self.avg_doc_length))
                
                score += idf * (numerator / denominator)
        
        return score

class StructuredRetriever:
    """结构化检索器（基于代码结构）"""
    
    def __init__(self):
        self.function_index = {}
        self.class_index = {}
        self.file_index = {}
        self.signature_index = {}
        self.metadata = []
    
    @performance_monitor("structured_build_index")
    def build_index(self, chunks: List[Dict[str, Any]]):
        """构建结构化索引"""
        logger.info(f"Building structured index for {len(chunks)} chunks...")
        
        self.metadata = chunks
        self.function_index = defaultdict(list)
        self.class_index = defaultdict(list)
        self.file_index = defaultdict(list)
        self.signature_index = defaultdict(list)
        
        for i, chunk in enumerate(chunks):
            metadata = chunk.get('metadata', {})
            
            # 函数名索引
            if metadata.get('name'):
                name = metadata['name'].lower()
                self.function_index[name].append(i)
                # 部分匹配索引
                for j in range(1, len(name)):
                    self.function_index[name[:j]].append(i)
            
            # 类名索引
            if metadata.get('parent_class'):
                class_name = metadata['parent_class'].lower()
                self.class_index[class_name].append(i)
            
            # 文件名索引
            if metadata.get('file_path'):
                file_name = metadata['file_path'].split('/')[-1].replace('.py', '').lower()
                self.file_index[file_name].append(i)
            
            # 签名索引
            if metadata.get('signature'):
                signature = metadata['signature'].lower()
                self.signature_index[signature].append(i)
                # 提取签名中的关键词
                sig_words = re.findall(r'\w+', signature)
                for word in sig_words:
                    if len(word) > 2:
                        self.signature_index[word].append(i)
        
        logger.info("Structured index built successfully")
    
    @performance_monitor("structured_search")
    def search(self, query: str, top_k: int = 30) -> List[Dict]:
        """结构化搜索"""
        query_lower = query.lower()
        query_words = re.findall(r'\w+', query_lower)
        
        candidates = defaultdict(float)
        
        # 1. 精确函数名匹配
        for word in query_words:
            if word in self.function_index:
                for idx in self.function_index[word]:
                    candidates[idx] += 3.0  # 高权重
        
        # 2. 类名匹配
        for word in query_words:
            if word in self.class_index:
                for idx in self.class_index[word]:
                    candidates[idx] += 2.0
        
        # 3. 文件名匹配
        for word in query_words:
            if word in self.file_index:
                for idx in self.file_index[word]:
                    candidates[idx] += 1.5
        
        # 4. 签名匹配
        for word in query_words:
            if word in self.signature_index:
                for idx in self.signature_index[word]:
                    candidates[idx] += 1.0
        
        # 5. 特殊模式匹配
        pattern_score = self._match_patterns(query, candidates)
        
        # 排序并返回结果
        sorted_candidates = sorted(candidates.items(), key=lambda x: x[1], reverse=True)
        
        results = []
        for idx, score in sorted_candidates[:top_k]:
            if score > 0:
                result = RetrievalResult(
                    chunk_id=idx,
                    chunk=self.metadata[idx],
                    score=score,
                    retrieval_method="structured",
                    explanation=f"Structured match score: {score:.1f}"
                )
                results.append(result.__dict__)
        
        return results
    
    def _match_patterns(self, query: str, candidates: Dict[int, float]) -> Dict[int, float]:
        """匹配特殊模式"""
        # 函数调用模式
        if re.search(r'\w+\s*\(', query):
            func_pattern = re.findall(r'(\w+)\s*\(', query)
            for func_name in func_pattern:
                if func_name.lower() in self.function_index:
                    for idx in self.function_index[func_name.lower()]:
                        candidates[idx] += 2.0
        
        # 类实例化模式
        if re.search(r'(\w+)\(\w*\)', query):
            class_pattern = re.findall(r'(\w+)\(\w*\)', query)
            for class_name in class_pattern:
                if class_name.lower() in self.class_index:
                    for idx in self.class_index[class_name.lower()]:
                        candidates[idx] += 2.0
        
        return candidates

class HybridRetriever:
    """混合检索器 - 使用策略模式重构"""
    
    def __init__(self, semantic_retriever, reranker):
        self.config = get_config()
        self.semantic_retriever = semantic_retriever
        self.reranker = reranker
        
        # 混合权重（提前初始化）
        self.weights = {
            SearchMethod.SEMANTIC: 0.5,
            SearchMethod.BM25: 0.3,
            SearchMethod.STRUCTURED: 0.2
        }
        
        # 初始化子检索器
        self.query_expander = QueryExpander()
        self.bm25_retriever = BM25Retriever()
        self.structured_retriever = StructuredRetriever()
        
        # 初始化搜索编排器
        self.orchestrator = SearchOrchestrator(self.config)
        self._setup_strategies()
    
    def _setup_strategies(self):
        """设置搜索策略"""
        # 注册搜索策略
        self.orchestrator.register_strategy(
            SearchMethod.SEMANTIC, 
            SemanticSearchStrategy(self.semantic_retriever)
        )
        self.orchestrator.register_strategy(
            SearchMethod.BM25, 
            BM25SearchStrategy(self.bm25_retriever)
        )
        self.orchestrator.register_strategy(
            SearchMethod.STRUCTURED, 
            StructuredSearchStrategy(self.structured_retriever)
        )
        
        # 设置融合策略
        fusion_strategy = WeightedFusionStrategy(self.weights)
        self.orchestrator.set_fusion_strategy(fusion_strategy)
        
        # 设置重排序策略
        if self.reranker:
            reranking_strategy = CrossEncoderRerankingStrategy(self.reranker)
            self.orchestrator.set_reranking_strategy(reranking_strategy)
        
        # 设置查询扩展器
        self.orchestrator.query_expander = self.query_expander
    
    @performance_monitor("hybrid_build_index")
    def build_index(self, chunks: List[Dict[str, Any]], embeddings: np.ndarray):
        """构建混合索引"""
        logger.info("Building hybrid retrieval index...")
        
        # 使用编排器构建索引
        self.orchestrator.build_index(chunks)
        
        logger.info("Hybrid index built successfully")
    
    @performance_monitor("hybrid_search")
    @error_handler(logger, "hybrid_search")
    def search(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """混合搜索 - 简化版本"""
        if top_k is None:
            top_k = self.config.retrieval.max_results
        
        logger.debug(f"Starting hybrid search for: {query}")
        
        try:
            # 使用编排器执行搜索
            return self.orchestrator.search(query, top_k)
        except Exception as e:
            raise RetrievalError(
                f"Hybrid search failed for query: {query}",
                query=query,
                cause=e
            )
    
    def _semantic_search(self, query: str) -> List[RetrievalResult]:
        """语义搜索"""
        # 调用原有的语义搜索
        try:
            query_embedding = self.semantic_retriever.get_query_embedding(query)
            raw_results = self.semantic_retriever.search_once(query_embedding)
            
            results = []
            for i, res in enumerate(raw_results):
                result = RetrievalResult(
                    chunk_id=i,
                    chunk=res['chunk'],
                    score=res['similarity'],
                    retrieval_method="semantic",
                    explanation=f"Semantic similarity: {res['similarity']:.4f}"
                )
                results.append(result)
            
            return results
        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []
    
    def _fuse_results(self, semantic_results: List[RetrievalResult], 
                     bm25_results: List[RetrievalResult],
                     structured_results: List[RetrievalResult]) -> List[Dict[str, Any]]:
        """融合多路召回结果"""
        # 使用加权融合
        chunk_scores = defaultdict(lambda: {
            'semantic': 0.0,
            'bm25': 0.0, 
            'structured': 0.0,
            'chunk': None,
            'explanations': []
        })
        
        # 归一化分数并融合
        for results, method, weight in [
            (semantic_results, 'semantic', self.weights['semantic']),
            (bm25_results, 'bm25', self.weights['bm25']),
            (structured_results, 'structured', self.weights['structured'])
        ]:
            if not results:
                continue
                
            # 归一化分数到[0,1]
            max_score = max(r.score for r in results) if results else 1.0
            if max_score == 0:
                max_score = 1.0
            
            for result in results:
                chunk_id = result.chunk_id
                normalized_score = result.score / max_score
                
                chunk_scores[chunk_id][method] = normalized_score
                chunk_scores[chunk_id]['chunk'] = result.chunk
                chunk_scores[chunk_id]['explanations'].append(result.explanation)
        
        # 计算最终分数
        final_results = []
        for chunk_id, score_info in chunk_scores.items():
            final_score = (
                score_info['semantic'] * self.weights['semantic'] +
                score_info['bm25'] * self.weights['bm25'] +
                score_info['structured'] * self.weights['structured']
            )
            
            if final_score > 0 and score_info['chunk']:
                final_results.append({
                    'chunk': score_info['chunk'],
                    'similarity': final_score,
                    'retrieval_methods': {
                        'semantic': score_info['semantic'],
                        'bm25': score_info['bm25'],
                        'structured': score_info['structured']
                    },
                    'explanations': score_info['explanations']
                })
        
        # 按分数排序
        final_results.sort(key=lambda x: x['similarity'], reverse=True)
        
        logger.debug(f"Fused {len(final_results)} results from multiple retrievers")
        return final_results
    
    def _rerank_results(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """重排序结果"""
        if not results or not self.reranker:
            return results
        
        try:
            # 准备重排序输入
            rerank_pairs = []
            for res in results:
                chunk = res['chunk']
                input_text = self._build_rerank_text(chunk)
                rerank_pairs.append([query, input_text])
            
            # 执行重排序
            rerank_scores = self.reranker.predict(rerank_pairs, show_progress_bar=False)
            
            # 更新分数
            for i, score in enumerate(rerank_scores):
                results[i]['similarity'] = score
                results[i]['rerank_score'] = score
            
            # 重新排序
            results.sort(key=lambda x: x['similarity'], reverse=True)
            
            logger.debug(f"Reranked {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Reranking failed: {e}")
            return results
    
    def _build_rerank_text(self, chunk: Dict[str, Any]) -> str:
        """构建重排序文本"""
        metadata = chunk.get('metadata', {})
        input_text = f"File: {metadata.get('file_path', '')}\n"
        
        if metadata.get('parent_class'):
            input_text += f"Class: {metadata['parent_class']}\n"
        if metadata.get('name'):
            input_text += f"Name: {metadata['name']}\n"
        if metadata.get('signature'):
            input_text += f"Signature: {metadata['signature']}\n"
        if metadata.get('docstring'):
            input_text += f"Docstring: {metadata['docstring']}\n"
        
        input_text += f"Code:\n{chunk.get('code', '')}"
        return input_text

# 全局实例
_hybrid_retriever = None

def get_hybrid_retriever(semantic_retriever=None, reranker=None) -> HybridRetriever:
    """获取混合检索器"""
    global _hybrid_retriever
    if _hybrid_retriever is None:
        _hybrid_retriever = HybridRetriever(semantic_retriever, reranker)
    return _hybrid_retriever