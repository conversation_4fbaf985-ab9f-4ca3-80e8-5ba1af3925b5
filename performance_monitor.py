"""
性能监控系统

提供全面的性能监控、分析和报告功能。
"""

import time
import functools
import threading
import psutil
import torch
from typing import Dict, Any, Callable, Optional, List, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
from contextlib import contextmanager
import json
from pathlib import Path

from logger import get_logger
from config_manager import get_config
from exceptions import BaseServiceError, error_handler

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    cpu_percent_before: float
    cpu_percent_after: float
    memory_mb_before: float
    memory_mb_after: float
    gpu_memory_mb_before: float = 0.0
    gpu_memory_mb_after: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def memory_delta_mb(self) -> float:
        """内存变化量"""
        return self.memory_mb_after - self.memory_mb_before
    
    @property
    def gpu_memory_delta_mb(self) -> float:
        """GPU内存变化量"""
        return self.gpu_memory_mb_after - self.gpu_memory_mb_before
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'operation_name': self.operation_name,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'cpu_percent_before': self.cpu_percent_before,
            'cpu_percent_after': self.cpu_percent_after,
            'memory_mb_before': self.memory_mb_before,
            'memory_mb_after': self.memory_mb_after,
            'memory_delta_mb': self.memory_delta_mb,
            'gpu_memory_mb_before': self.gpu_memory_mb_before,
            'gpu_memory_mb_after': self.gpu_memory_mb_after,
            'gpu_memory_delta_mb': self.gpu_memory_delta_mb,
            'success': self.success,
            'error_message': self.error_message,
            'custom_metrics': self.custom_metrics
        }


@dataclass
class OperationStats:
    """操作统计"""
    operation_name: str
    call_count: int = 0
    total_duration: float = 0.0
    min_duration: float = float('inf')
    max_duration: float = 0.0
    success_count: int = 0
    error_count: int = 0
    total_memory_delta: float = 0.0
    total_gpu_memory_delta: float = 0.0
    
    @property
    def average_duration(self) -> float:
        """平均耗时"""
        return self.total_duration / self.call_count if self.call_count > 0 else 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.success_count / self.call_count if self.call_count > 0 else 0.0
    
    @property
    def average_memory_delta(self) -> float:
        """平均内存变化"""
        return self.total_memory_delta / self.call_count if self.call_count > 0 else 0.0
    
    def update(self, metrics: PerformanceMetrics):
        """更新统计"""
        self.call_count += 1
        self.total_duration += metrics.duration
        self.min_duration = min(self.min_duration, metrics.duration)
        self.max_duration = max(self.max_duration, metrics.duration)
        
        if metrics.success:
            self.success_count += 1
        else:
            self.error_count += 1
        
        self.total_memory_delta += metrics.memory_delta_mb
        self.total_gpu_memory_delta += metrics.gpu_memory_delta_mb
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'operation_name': self.operation_name,
            'call_count': self.call_count,
            'total_duration': self.total_duration,
            'average_duration': self.average_duration,
            'min_duration': self.min_duration,
            'max_duration': self.max_duration,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': self.success_rate,
            'total_memory_delta': self.total_memory_delta,
            'average_memory_delta': self.average_memory_delta,
            'total_gpu_memory_delta': self.total_gpu_memory_delta
        }


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.operation_stats: Dict[str, OperationStats] = {}
        self.recent_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self._lock = threading.RLock()
        
        # 系统信息
        self.process = psutil.Process()
    
    def add_metrics(self, metrics: PerformanceMetrics):
        """添加性能指标"""
        with self._lock:
            # 添加到历史记录
            self.metrics_history.append(metrics)
            
            # 更新操作统计
            if metrics.operation_name not in self.operation_stats:
                self.operation_stats[metrics.operation_name] = OperationStats(metrics.operation_name)
            
            self.operation_stats[metrics.operation_name].update(metrics)
            
            # 添加到最近指标
            self.recent_metrics[metrics.operation_name].append(metrics)
    
    def get_operation_stats(self, operation_name: Optional[str] = None) -> Union[OperationStats, Dict[str, OperationStats]]:
        """获取操作统计"""
        with self._lock:
            if operation_name:
                return self.operation_stats.get(operation_name)
            return dict(self.operation_stats)
    
    def get_recent_metrics(self, operation_name: str, count: int = 10) -> List[PerformanceMetrics]:
        """获取最近的性能指标"""
        with self._lock:
            metrics = list(self.recent_metrics.get(operation_name, []))
            return metrics[-count:] if metrics else []
    
    def get_system_snapshot(self) -> Dict[str, Any]:
        """获取系统快照"""
        try:
            memory_info = self.process.memory_info()
            cpu_percent = self.process.cpu_percent()
            
            snapshot = {
                'timestamp': time.time(),
                'cpu_percent': cpu_percent,
                'memory_mb': memory_info.rss / 1024 / 1024,
                'memory_percent': self.process.memory_percent(),
                'num_threads': self.process.num_threads(),
            }
            
            # GPU信息
            if torch.cuda.is_available():
                snapshot.update({
                    'gpu_memory_allocated_mb': torch.cuda.memory_allocated() / 1024 / 1024,
                    'gpu_memory_reserved_mb': torch.cuda.memory_reserved() / 1024 / 1024,
                    'gpu_count': torch.cuda.device_count()
                })
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Failed to get system snapshot: {e}")
            return {'timestamp': time.time(), 'error': str(e)}
    
    def generate_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        with self._lock:
            report = {
                'timestamp': time.time(),
                'total_operations': len(self.operation_stats),
                'total_metrics': len(self.metrics_history),
                'system_snapshot': self.get_system_snapshot(),
                'operation_stats': {
                    name: stats.to_dict() 
                    for name, stats in self.operation_stats.items()
                }
            }
            
            # 添加汇总统计
            if self.operation_stats:
                total_calls = sum(stats.call_count for stats in self.operation_stats.values())
                total_errors = sum(stats.error_count for stats in self.operation_stats.values())
                overall_success_rate = (total_calls - total_errors) / total_calls if total_calls > 0 else 0.0
                
                report['summary'] = {
                    'total_calls': total_calls,
                    'total_errors': total_errors,
                    'overall_success_rate': overall_success_rate,
                    'operations_count': len(self.operation_stats)
                }
            
            return report
    
    def clear_stats(self):
        """清空统计数据"""
        with self._lock:
            self.metrics_history.clear()
            self.operation_stats.clear()
            self.recent_metrics.clear()
    
    def export_metrics(self, file_path: str, format: str = "json"):
        """导出性能指标"""
        try:
            report = self.generate_report()
            
            path = Path(file_path)
            if format.lower() == "json":
                with open(path, 'w') as f:
                    json.dump(report, f, indent=2, default=str)
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            logger.info(f"Performance metrics exported to {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.collector = PerformanceCollector()
        self.enabled = True
        self._thresholds = {
            'slow_operation_seconds': 1.0,
            'high_memory_mb': 1024.0,
            'high_cpu_percent': 80.0
        }
        self._alerts_enabled = True
    
    def set_thresholds(self, **thresholds):
        """设置性能阈值"""
        self._thresholds.update(thresholds)
    
    def enable_alerts(self, enabled: bool = True):
        """启用/禁用性能警告"""
        self._alerts_enabled = enabled
    
    @contextmanager
    def monitor_operation(self, operation_name: str, custom_metrics: Optional[Dict[str, Any]] = None):
        """监控操作的上下文管理器"""
        if not self.enabled:
            yield
            return
        
        # 收集开始时的指标
        start_time = time.time()
        cpu_before = psutil.cpu_percent()
        memory_before = psutil.Process().memory_info().rss / 1024 / 1024
        gpu_memory_before = self._get_gpu_memory()
        
        success = True
        error_message = None
        
        try:
            yield
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            # 收集结束时的指标
            end_time = time.time()
            duration = end_time - start_time
            cpu_after = psutil.cpu_percent()
            memory_after = psutil.Process().memory_info().rss / 1024 / 1024
            gpu_memory_after = self._get_gpu_memory()
            
            # 创建性能指标
            metrics = PerformanceMetrics(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                cpu_percent_before=cpu_before,
                cpu_percent_after=cpu_after,
                memory_mb_before=memory_before,
                memory_mb_after=memory_after,
                gpu_memory_mb_before=gpu_memory_before,
                gpu_memory_mb_after=gpu_memory_after,
                success=success,
                error_message=error_message,
                custom_metrics=custom_metrics or {}
            )
            
            # 添加到收集器
            self.collector.add_metrics(metrics)
            
            # 检查警告
            if self._alerts_enabled:
                self._check_alerts(metrics)
    
    def _get_gpu_memory(self) -> float:
        """获取GPU内存使用量"""
        try:
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024 / 1024
            return 0.0
        except Exception:
            return 0.0
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """检查性能警告"""
        # 慢操作警告
        if metrics.duration > self._thresholds['slow_operation_seconds']:
            logger.warning(
                f"Slow operation detected: {metrics.operation_name} "
                f"took {metrics.duration:.2f}s"
            )
        
        # 高内存使用警告
        if abs(metrics.memory_delta_mb) > self._thresholds['high_memory_mb']:
            logger.warning(
                f"High memory usage in {metrics.operation_name}: "
                f"{metrics.memory_delta_mb:+.1f}MB"
            )
        
        # 高CPU使用警告
        if metrics.cpu_percent_after > self._thresholds['high_cpu_percent']:
            logger.warning(
                f"High CPU usage after {metrics.operation_name}: "
                f"{metrics.cpu_percent_after:.1f}%"
            )
    
    def get_stats(self, operation_name: Optional[str] = None) -> Union[OperationStats, Dict[str, OperationStats]]:
        """获取统计信息"""
        return self.collector.get_operation_stats(operation_name)
    
    def generate_report(self) -> Dict[str, Any]:
        """生成报告"""
        return self.collector.generate_report()
    
    def export_metrics(self, file_path: str, format: str = "json"):
        """导出指标"""
        self.collector.export_metrics(file_path, format)
    
    def clear_stats(self):
        """清空统计"""
        self.collector.clear_stats()


# 全局性能监控器
_performance_monitor = None
_monitor_lock = threading.Lock()


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器"""
    global _performance_monitor
    
    if _performance_monitor is None:
        with _monitor_lock:
            if _performance_monitor is None:
                _performance_monitor = PerformanceMonitor()
    
    return _performance_monitor


def performance_monitor(operation_name: Optional[str] = None, custom_metrics: Optional[Dict[str, Any]] = None):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            name = operation_name or f"{func.__module__}.{func.__name__}"
            
            with monitor.monitor_operation(name, custom_metrics):
                return func(*args, **kwargs)
        
        return wrapper
    
    # 支持不带参数的使用
    if callable(operation_name):
        func = operation_name
        operation_name = None
        return decorator(func)
    
    return decorator


@contextmanager
def monitor_performance(operation_name: str, custom_metrics: Optional[Dict[str, Any]] = None):
    """性能监控上下文管理器"""
    monitor = get_performance_monitor()
    with monitor.monitor_operation(operation_name, custom_metrics):
        yield


def get_performance_stats(operation_name: Optional[str] = None) -> Union[OperationStats, Dict[str, OperationStats]]:
    """获取性能统计信息"""
    monitor = get_performance_monitor()
    return monitor.get_stats(operation_name)


def generate_performance_report() -> Dict[str, Any]:
    """生成性能报告"""
    monitor = get_performance_monitor()
    return monitor.generate_report()


def export_performance_metrics(file_path: str, format: str = "json"):
    """导出性能指标"""
    monitor = get_performance_monitor()
    monitor.export_metrics(file_path, format)


def clear_performance_stats():
    """清空性能统计"""
    monitor = get_performance_monitor()
    monitor.clear_stats()


def set_performance_thresholds(**thresholds):
    """设置性能阈值"""
    monitor = get_performance_monitor()
    monitor.set_thresholds(**thresholds)


# 为了兼容性，保留原有的performance_monitor函数
def performance_monitor_legacy(operation_name: str):
    """传统的性能监控装饰器（保持兼容性）"""
    return performance_monitor(operation_name)