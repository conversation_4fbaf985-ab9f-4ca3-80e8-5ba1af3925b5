#!/usr/bin/env python3
"""
模型下载脚本
用于预先下载所需的模型到本地缓存
"""

import os
import sys
from pathlib import Path
from transformers import <PERSON><PERSON>oken<PERSON>, RobertaModel, RobertaConfig
from sentence_transformers import CrossEncoder

def download_unixcoder_model():
    """下载UniXcoder模型"""
    model_name = "microsoft/unixcoder-base"
    cache_dir = "./models"
    
    print(f"Downloading UniXcoder model: {model_name}")
    print(f"Cache directory: {cache_dir}")
    
    try:
        # 创建缓存目录
        Path(cache_dir).mkdir(parents=True, exist_ok=True)
        
        # 下载tokenizer
        print("Downloading tokenizer...")
        tokenizer = RobertaTokenizer.from_pretrained(model_name, cache_dir=cache_dir)
        
        # 下载配置
        print("Downloading config...")
        config = RobertaConfig.from_pretrained(model_name, cache_dir=cache_dir)
        
        # 下载模型
        print("Downloading model...")
        model = RobertaModel.from_pretrained(model_name, cache_dir=cache_dir)
        
        print(f"✅ UniXcoder model downloaded successfully to {cache_dir}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download UniXcoder model: {e}")
        return False

def download_reranker_model():
    """下载重排序模型"""
    model_name = "cross-encoder/ms-marco-MiniLM-L-6-v2"
    cache_dir = "./models"
    
    print(f"Downloading reranker model: {model_name}")
    
    try:
        # 创建缓存目录
        Path(cache_dir).mkdir(parents=True, exist_ok=True)
        
        # 下载重排序模型
        print("Downloading reranker...")
        reranker = CrossEncoder(model_name, cache_folder=cache_dir)
        
        print(f"✅ Reranker model downloaded successfully to {cache_dir}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download reranker model: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Starting model download...")
    print("=" * 50)
    
    success_count = 0
    total_count = 2
    
    # 下载UniXcoder模型
    if download_unixcoder_model():
        success_count += 1
    
    print("-" * 50)
    
    # 下载重排序模型
    if download_reranker_model():
        success_count += 1
    
    print("=" * 50)
    print(f"Download completed: {success_count}/{total_count} models downloaded successfully")
    
    if success_count == total_count:
        print("✅ All models downloaded successfully!")
        print("You can now run the indexing with offline mode.")
        return True
    else:
        print("⚠️  Some models failed to download.")
        print("You may need to check your network connection or try again.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
