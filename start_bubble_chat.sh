#!/bin/bash

# UniXcoder RAG 气泡对话界面启动脚本

echo "💬 UniXcoder RAG 气泡对话界面"
echo "================================"

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python未安装或不在PATH中"
    exit 1
fi

# 检查必要文件
echo "🔍 检查系统文件..."

required_files=(
    "frontend/app.py"
    "frontend/api_service.py"
    "faiss_index.bin"
    "metadata.pkl"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file"
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo ""
    echo "⚠️  缺少必要文件:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    
    if [[ " ${missing_files[@]} " =~ " faiss_index.bin " ]] || [[ " ${missing_files[@]} " =~ " metadata.pkl " ]]; then
        echo ""
        echo "请先构建索引:"
        echo "   python build_index.py"
    fi
    exit 1
fi

echo ""
echo "✅ 系统文件检查通过"

# 检查端口
echo "🔍 检查端口8501..."
if lsof -Pi :8501 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口8501已被占用"
    echo "请停止其他应用或使用不同端口"
    exit 1
fi

echo "✅ 端口8501可用"

# 显示功能特性
echo ""
echo "🎨 界面特色:"
echo "  💬 气泡样式对话界面"
echo "  📝 Markdown格式AI回答"
echo "  📚 侧边栏代码引用"
echo "  🔄 流式输出动画效果"
echo "  🎯 多轮对话支持"

echo ""
echo "🚀 启动气泡对话界面..."
echo "🌐 URL: http://localhost:8501"
echo "⏹️  按 Ctrl+C 停止服务"
echo ""

# 启动应用
python -m streamlit run frontend/app.py --server.port 8501 --server.headless true

echo ""
echo "⏹️  气泡对话界面已停止"
