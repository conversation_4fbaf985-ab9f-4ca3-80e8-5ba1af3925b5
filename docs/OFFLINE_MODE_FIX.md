# Offline Mode Fix for UniXcoder RAG System

## Problem
The UniXcoder RAG system was failing to start due to network connectivity issues when trying to access Hugging Face Hub for model templates, even though the models were downloaded locally.

## Root Cause
The transformers library was attempting to fetch additional chat templates from Hugging Face Hub during model initialization, despite having `local_files_only=True` set in the configuration. This was happening because:

1. Environment variables for offline mode were not being set early enough in the import process
2. The transformers library needs specific environment variables to be set before any transformers-related modules are imported

## Solution

### 1. Updated Model Manager
- Added environment variable handling in the `get_unixcoder()` method to set offline mode variables before loading UniXcoder models
- Added similar handling for the reranker model loading
- Added a helper function `_set_offline_mode()` to centralize environment variable setting

### 2. Updated Frontend Application
- Added environment variable setting at the very beginning of `frontend/app.py` before any transformers imports
- Ensured proper import order to set offline mode before loading any models

### 3. Created Startup Script
- Created `start_offline.sh` script that sets environment variables at the shell level before starting the application
- This ensures the environment variables are set before <PERSON> starts and imports any modules

## Key Environment Variables
The following environment variables must be set for offline mode:
```bash
export TRANSFORMERS_OFFLINE=1
export HF_HUB_OFFLINE=1
export HF_HUB_DISABLE_TELEMETRY=1
```

## Usage

### Method 1: Using the Startup Script (Recommended)
```bash
./start_offline.sh
```

### Method 2: Manual Environment Setup
```bash
export TRANSFORMERS_OFFLINE=1
export HF_HUB_OFFLINE=1
export HF_HUB_DISABLE_TELEMETRY=1
source .venv/bin/activate
cd frontend
python -m streamlit run app.py --server.port 8501
```

## Configuration
Ensure your `config.json` has the following settings for offline mode:
```json
{
  "model": {
    "cache_dir": "./models",
    "local_files_only": true,
    "offline_mode": true
  }
}
```

## Verification
When the system starts successfully in offline mode, you should see:
- No network connection errors
- Models loading from local cache
- "CodeAssistant initialized successfully" message
- Streamlit app accessible at http://localhost:8501

## Files Modified
1. `model_manager.py` - Added offline mode environment variable handling
2. `frontend/app.py` - Added early environment variable setting
3. `start_offline.sh` - New startup script for offline mode
4. `config.json` - Updated with offline mode settings

## Testing
The fix was verified by:
1. Running the test script `test_model_fix.py` which confirmed both UniXcoder and reranker models load successfully
2. Starting the full Streamlit application which now initializes without network errors
3. Confirming the web interface is accessible and functional

## Notes
- The local models must be downloaded first using `download_models.py`
- The environment variables must be set before any Python imports occur
- The startup script provides the most reliable way to ensure proper offline mode operation
