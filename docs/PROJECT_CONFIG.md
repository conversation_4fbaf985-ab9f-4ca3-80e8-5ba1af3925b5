# 项目配置说明

## 概述

本项目支持通过 `.env` 文件配置项目目录和文件处理参数，用于自定义代码索引的范围和行为。

## 配置参数

### 1. PROJECT_ROOT_DIR
- **描述**: 项目根目录
- **默认值**: `.` (当前目录)
- **示例**: `PROJECT_ROOT_DIR=/path/to/your/project`

### 2. TARGET_DIRECTORIES
- **描述**: 要扫描的目标目录列表，用逗号分隔
- **默认值**: `./,./tests,./docs`
- **示例**: `TARGET_DIRECTORIES=./src,./lib,./tests,./examples`

### 3. EXCLUDE_PATTERNS
- **描述**: 要排除的文件/目录模式，用逗号分隔
- **默认值**: `__pycache__,*.pyc,*.pyo,*.egg-info,.git,.venv,node_modules,build,dist`
- **示例**: `EXCLUDE_PATTERNS=__pycache__,.git,.venv,build,dist,*.log`

### 4. FILE_EXTENSIONS
- **描述**: 要处理的文件扩展名，用逗号分隔
- **默认值**: `.py,.md,.txt,.json,.toml,.yaml,.yml`
- **示例**: `FILE_EXTENSIONS=.py,.js,.ts,.java,.cpp,.h,.md`

## 配置示例

### Python项目
```env
PROJECT_ROOT_DIR=.
TARGET_DIRECTORIES=./src,./tests,./docs
EXCLUDE_PATTERNS=__pycache__,*.pyc,.git,.venv,build,dist
FILE_EXTENSIONS=.py,.md,.txt,.json,.toml,.yaml
```

### JavaScript/Node.js项目
```env
PROJECT_ROOT_DIR=.
TARGET_DIRECTORIES=./src,./lib,./test,./docs
EXCLUDE_PATTERNS=node_modules,.git,build,dist,*.log
FILE_EXTENSIONS=.js,.ts,.jsx,.tsx,.json,.md
```

### 多语言项目
```env
PROJECT_ROOT_DIR=.
TARGET_DIRECTORIES=./src,./backend,./frontend,./docs
EXCLUDE_PATTERNS=__pycache__,node_modules,.git,.venv,build,dist,target
FILE_EXTENSIONS=.py,.js,.ts,.java,.cpp,.h,.md,.json,.yaml
```

### 大型项目（选择性扫描）
```env
PROJECT_ROOT_DIR=.
TARGET_DIRECTORIES=./core,./api,./utils
EXCLUDE_PATTERNS=__pycache__,node_modules,.git,.venv,build,dist,logs,tmp
FILE_EXTENSIONS=.py,.js,.md
```

## 使用方法

1. **编辑 .env 文件**:
   ```bash
   # 编辑项目根目录下的 .env 文件
   nano .env
   ```

2. **运行索引构建**:
   ```bash
   # 使用配置构建索引
   uv run run_build_index_offline.py
   ```

3. **验证配置**:
   运行时会显示当前配置：
   ```
   📁 Project root: .
   📂 Target directories: ./src,./tests,./docs
   📄 File extensions: .py,.md,.txt,.json
   🚫 Exclude patterns: __pycache__,.git,.venv
   ```

## 注意事项

1. **路径格式**: 使用相对路径（相对于项目根目录）或绝对路径
2. **分隔符**: 多个值用逗号分隔，不要有空格
3. **扩展名**: 文件扩展名必须包含点号（如 `.py`）
4. **排除模式**: 支持简单的通配符模式（如 `*.pyc`）
5. **大小写敏感**: 文件扩展名和路径都是大小写敏感的

## 性能建议

1. **限制目录范围**: 只扫描必要的目录，避免包含大量无关文件
2. **排除大文件**: 通过排除模式跳过日志文件、编译产物等
3. **选择合适的扩展名**: 只包含需要索引的文件类型
4. **避免深层嵌套**: 过深的目录结构可能影响扫描性能

## 故障排除

### 问题：找不到文件
- 检查 `TARGET_DIRECTORIES` 路径是否正确
- 确认 `FILE_EXTENSIONS` 包含目标文件类型
- 验证文件没有被 `EXCLUDE_PATTERNS` 排除

### 问题：扫描时间过长
- 减少 `TARGET_DIRECTORIES` 的范围
- 增加更多 `EXCLUDE_PATTERNS` 来跳过无关目录
- 限制 `FILE_EXTENSIONS` 到必要的类型

### 问题：内存使用过高
- 排除大型文件（如数据文件、媒体文件）
- 分批处理大型项目
- 检查是否包含了编译产物或缓存文件
