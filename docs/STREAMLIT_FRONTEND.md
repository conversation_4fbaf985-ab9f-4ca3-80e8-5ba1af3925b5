# Streamlit前端应用说明

## 概述

UniXcoder RAG系统的Streamlit前端提供了一个直观、功能丰富的Web界面，支持智能代码检索、流式AI对话和可视化分析。

## 主要功能

### 🔍 智能代码检索
- **语义搜索**：基于UniXcoder模型的深度语义理解
- **混合检索**：结合多种检索方法提高准确性
- **实时搜索**：即时返回相关代码片段

### 💬 多轮对话系统
- **上下文感知**：支持多轮连续对话，自动维护对话上下文
- **对话历史**：完整记录用户问题和AI回答
- **智能上下文**：可配置上下文长度，优化对话连贯性
- **对话管理**：支持新建对话、查看历史、对话统计

### 🔄 流式AI回答
- **实时显示**：AI回答过程实时显示，无需等待
- **中断控制**：支持停止正在生成的回答
- **状态指示**：清晰的生成状态和进度提示
- **错误处理**：优雅处理网络错误和API异常

### 📚 智能引用面板
- **紧凑展示**：优化的代码引用卡片设计
- **过滤排序**：支持按相似度、文件、方法过滤和排序
- **展开收起**：可控制引用显示数量和详细程度
- **统计分析**：引用摘要和分布统计

### 📊 可视化分析
- **相似度图表**：直观显示搜索结果的相似度分布
- **检索方法分析**：展示不同检索方法的贡献度
- **性能统计**：监控检索效果和响应时间

### 📋 代码片段展示
- **语法高亮**：Python代码语法高亮显示
- **行号标注**：精确定位代码位置
- **上下文信息**：显示文件路径、类名、函数签名等
- **相似度评分**：颜色编码的相似度分数

### 🎨 响应式布局
- **双栏设计**：对话区域和引用面板分离显示
- **自适应布局**：根据屏幕尺寸自动调整
- **可控显示**：支持隐藏/显示引用面板
- **优化交互**：流畅的用户交互体验

## 技术架构

### 前端组件
```
frontend/
├── app.py              # 主Streamlit应用
├── api_service.py      # API服务层
└── code_viewer.py      # 代码展示组件
```

### 核心模块

#### 1. API服务层 (api_service.py)
- **StreamingAPIService**：核心API服务类
- **SearchResult**：搜索结果数据结构
- **QueryRequest/QueryResponse**：请求响应数据结构
- 支持流式输出和异步处理

#### 2. 主应用 (app.py)
- **用户界面**：基于Streamlit的响应式UI
- **会话管理**：对话历史和状态管理
- **流式渲染**：实时更新AI回答内容
- **错误处理**：用户友好的错误提示

#### 3. 代码展示组件 (code_viewer.py)
- **增强展示**：多标签页展示搜索结果
- **可视化图表**：Plotly图表展示分析结果
- **交互功能**：排序、筛选、展开/折叠

## 使用指南

### 启动应用
```bash
# 方式一：使用启动脚本
./start_streamlit.sh

# 方式二：直接启动
cd frontend
streamlit run app.py
```

### 界面操作

#### 1. 系统初始化
- 点击侧边栏的"🚀 初始化系统"按钮
- 等待系统加载模型和索引
- 查看初始化状态和配置信息

#### 2. 提出问题
- 在主界面输入框中输入问题
- 可选择预设的示例问题
- 点击"🔍 搜索"按钮开始查询

#### 3. 查看结果
- **详细结果**：查看所有匹配的代码片段
- **相似度分析**：查看相似度分布图表
- **检索方法分析**：了解不同方法的效果

#### 4. 对话管理
- 查看对话历史
- 开始新对话
- 利用上下文进行多轮对话

## 配置说明

### 环境变量
```bash
# LLM API配置
DEEPSEEK_API_KEY=your_api_key

# 离线模式配置
TRANSFORMERS_OFFLINE=1
HF_HUB_OFFLINE=1
HF_HUB_DISABLE_TELEMETRY=1
```

### 依赖包
```toml
streamlit>=1.47.1      # Web框架
plotly>=5.0.0          # 图表库
pandas>=1.5.0          # 数据处理
pydantic>=2.5.0        # 数据验证
```

## 性能优化

### 1. 缓存策略
- **模型缓存**：避免重复加载模型
- **搜索缓存**：缓存常见查询结果
- **会话状态**：优化状态管理

### 2. 流式处理
- **分块传输**：减少首次响应时间
- **异步处理**：提高并发性能
- **错误恢复**：优雅处理中断

### 3. 用户体验
- **响应式设计**：适配不同屏幕尺寸
- **加载指示**：清晰的进度提示
- **错误提示**：友好的错误信息

## 故障排除

### 常见问题

#### 1. 初始化失败
```
❌ 初始化失败: No module named 'xxx'
```
**解决方案**：
- 检查虚拟环境是否激活
- 安装缺失的依赖包：`pip install -e .`

#### 2. 模型加载错误
```
❌ 模型加载失败: Model not found
```
**解决方案**：
- 运行模型下载脚本：`python download_models.py`
- 检查模型文件是否存在

#### 3. 索引文件缺失
```
⚠️ Warning: Index files not found
```
**解决方案**：
- 构建索引：`python build_index.py`
- 检查索引文件权限

#### 4. LLM API错误
```
❌ Error generating response: API key not found
```
**解决方案**：
- 设置DEEPSEEK_API_KEY环境变量
- 检查API密钥有效性

### 调试模式
```bash
# 启用详细日志
export STREAMLIT_LOGGER_LEVEL=debug

# 运行测试脚本
python test_frontend.py
```

## 扩展开发

### 添加新功能
1. 在`api_service.py`中添加后端接口
2. 在`app.py`中添加UI组件
3. 在`code_viewer.py`中添加展示组件

### 自定义样式
- 修改`app.py`中的CSS样式
- 使用Streamlit的主题系统
- 添加自定义组件

### 性能监控
- 集成性能监控工具
- 添加用户行为分析
- 优化响应时间

## 更新日志

### v1.0.0 (2025-01-28)
- ✅ 实现基础Streamlit前端
- ✅ 支持流式LLM输出
- ✅ 代码片段可视化展示
- ✅ 相似度分析图表
- ✅ 多轮对话支持
- ✅ 错误处理和用户提示
