"""
增强的配置管理系统

支持配置热更新、环境变量覆盖、配置验证和默认值管理。
"""

import os
import json
import yaml
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional, Union, Callable, List
from dataclasses import dataclass, field, asdict
from enum import Enum
import weakref

from logger import get_logger
from exceptions import ConfigurationError, error_handler, handle_errors

logger = get_logger(__name__)


class ConfigFormat(Enum):
    """配置文件格式"""
    JSON = "json"
    YAML = "yaml"
    ENV = "env"


@dataclass
class CacheConfig:
    """缓存配置"""
    # 嵌入缓存
    embedding_max_size: int = 10000
    embedding_max_memory_mb: int = 1024
    embedding_ttl: Optional[float] = 3600
    
    # 模型缓存
    model_max_memory_mb: int = 8192
    model_ttl: Optional[float] = None
    
    # 通用缓存设置
    cleanup_interval: int = 300
    enable_cache: bool = True


@dataclass
class SystemConfig:
    """系统配置"""
    # 性能设置
    batch_size: int = 32
    max_workers: int = 4
    cache_embeddings: bool = True
    
    # 内存管理
    memory_warning_threshold_mb: int = 8192
    memory_critical_threshold_mb: int = 12288
    auto_cleanup: bool = True
    
    # 日志设置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    enable_performance_monitoring: bool = True


@dataclass
class ModelConfig:
    """模型配置"""
    name: str = "microsoft/unixcoder-base"
    max_length: int = 512
    device: str = "auto"
    cache_dir: Optional[str] = None
    load_in_8bit: bool = False
    
    # 推理设置
    batch_size: int = 32
    fp16: bool = True
    trust_remote_code: bool = False


@dataclass
class IndexConfig:
    """索引配置"""
    index_file: str = "faiss_index.bin"
    metadata_file: str = "metadata.pkl"
    index_type: str = "ivf"
    nlist: int = 100
    nprobe: int = 10
    use_gpu: bool = True
    dimension: int = 768
    
    # 构建设置
    build_batch_size: int = 1000
    parallel_build: bool = True
    temp_dir: Optional[str] = None


@dataclass
class ChunkingConfig:
    """分块配置"""
    max_tokens: int = 900
    overlap_ratio: float = 0.1
    min_chunk_size: int = 50
    semantic_splitting: bool = True
    preserve_structure: bool = True
    
    # 高级设置
    use_ast_parsing: bool = True
    extract_docstrings: bool = True
    include_imports: bool = True


@dataclass
class RetrievalConfig:
    """检索配置"""
    initial_k: int = 25
    similarity_threshold: float = 0.34
    score_drop_off_threshold: float = 0.05
    max_results: int = 7
    reranker_model: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"
    reranker_max_length: int = 512
    use_reranking: bool = True
    
    # 混合检索权重
    semantic_weight: float = 0.5
    bm25_weight: float = 0.3
    structured_weight: float = 0.2
    
    # 查询扩展
    enable_query_expansion: bool = True
    expansion_terms: int = 3


@dataclass
class Config:
    """主配置类"""
    model: ModelConfig = field(default_factory=ModelConfig)
    index: IndexConfig = field(default_factory=IndexConfig)
    chunking: ChunkingConfig = field(default_factory=ChunkingConfig)
    retrieval: RetrievalConfig = field(default_factory=RetrievalConfig)
    system: SystemConfig = field(default_factory=SystemConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    
    # 元数据
    version: str = "2.0.0"
    last_updated: Optional[float] = None


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate(config: Config) -> List[str]:
        """验证配置，返回错误列表"""
        errors = []
        
        # 验证模型配置
        if config.model.max_length <= 0:
            errors.append("model.max_length must be positive")
        
        if config.model.device not in ["auto", "cuda", "cpu"]:
            errors.append("model.device must be 'auto', 'cuda', or 'cpu'")
        
        # 验证索引配置
        if config.index.dimension <= 0:
            errors.append("index.dimension must be positive")
        
        if config.index.nlist <= 0:
            errors.append("index.nlist must be positive")
        
        # 验证分块配置
        if not 0 < config.chunking.overlap_ratio < 1:
            errors.append("chunking.overlap_ratio must be between 0 and 1")
        
        if config.chunking.max_tokens <= config.chunking.min_chunk_size:
            errors.append("chunking.max_tokens must be greater than min_chunk_size")
        
        # 验证检索配置
        if not 0 < config.retrieval.similarity_threshold < 1:
            errors.append("retrieval.similarity_threshold must be between 0 and 1")
        
        weights_sum = (
            config.retrieval.semantic_weight + 
            config.retrieval.bm25_weight + 
            config.retrieval.structured_weight
        )
        if abs(weights_sum - 1.0) > 0.01:
            errors.append("retrieval weights must sum to approximately 1.0")
        
        # 验证系统配置
        if config.system.batch_size <= 0:
            errors.append("system.batch_size must be positive")
        
        if config.system.max_workers <= 0:
            errors.append("system.max_workers must be positive")
        
        # 验证缓存配置
        if config.cache.embedding_max_size <= 0:
            errors.append("cache.embedding_max_size must be positive")
        
        return errors


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._find_config_file()
        self.config = Config()
        self._callbacks: List[weakref.ReferenceType] = []
        self._last_modified = 0.0
        self._watching = False
        self._watch_thread = None
        self._lock = threading.RLock()
        
        # 加载配置
        self.load_config()
    
    def _find_config_file(self) -> Optional[str]:
        """查找配置文件"""
        possible_files = [
            "config.json",
            "config.yaml", 
            "config.yml",
            ".unixcoder.json",
            ".unixcoder.yaml"
        ]
        
        for filename in possible_files:
            if Path(filename).exists():
                logger.info(f"Found config file: {filename}")
                return filename
        
        return None
    
    @handle_errors(ConfigurationError, logger=logger)
    def load_config(self):
        """加载配置"""
        # 先加载默认配置
        self.config = Config()
        
        # 从文件加载配置
        if self.config_file and Path(self.config_file).exists():
            self._load_from_file()
        
        # 从环境变量覆盖
        self._load_from_env()
        
        # 验证配置
        self._validate_config()
        
        # 更新时间戳
        self.config.last_updated = time.time()
        
        # 通知监听器
        self._notify_callbacks()
        
        logger.info("Configuration loaded successfully")
    
    def _load_from_file(self):
        """从文件加载配置"""
        try:
            config_path = Path(self.config_file)
            content = config_path.read_text()
            
            if config_path.suffix.lower() in ['.yml', '.yaml']:
                data = yaml.safe_load(content)
            else:
                data = json.loads(content)
            
            # 递归更新配置
            self._update_config_recursive(self.config, data)
            
            # 更新文件修改时间
            self._last_modified = config_path.stat().st_mtime
            
        except Exception as e:
            raise ConfigurationError(
                f"Failed to load config from {self.config_file}",
                config_key=self.config_file,
                cause=e
            )
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        env_mappings = {
            'UNIXCODER_MODEL_NAME': ('model', 'name'),
            'UNIXCODER_MODEL_DEVICE': ('model', 'device'),
            'UNIXCODER_BATCH_SIZE': ('system', 'batch_size'),
            'UNIXCODER_LOG_LEVEL': ('system', 'log_level'),
            'UNIXCODER_CACHE_SIZE': ('cache', 'embedding_max_size'),
            'UNIXCODER_MAX_LENGTH': ('model', 'max_length'),
        }
        
        for env_var, (section, key) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    # 尝试转换类型
                    section_obj = getattr(self.config, section)
                    current_value = getattr(section_obj, key)
                    
                    if isinstance(current_value, bool):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif isinstance(current_value, int):
                        value = int(value)
                    elif isinstance(current_value, float):
                        value = float(value)
                    
                    setattr(section_obj, key, value)
                    logger.debug(f"Updated {section}.{key} from environment: {value}")
                    
                except Exception as e:
                    logger.warning(f"Failed to parse environment variable {env_var}: {e}")
    
    def _update_config_recursive(self, config_obj: Any, data: Dict[str, Any]):
        """递归更新配置对象"""
        for key, value in data.items():
            if hasattr(config_obj, key):
                attr = getattr(config_obj, key)
                
                if hasattr(attr, '__dict__') and isinstance(value, dict):
                    # 递归更新嵌套配置
                    self._update_config_recursive(attr, value)
                else:
                    # 直接设置值
                    setattr(config_obj, key, value)
    
    def _validate_config(self):
        """验证配置"""
        errors = ConfigValidator.validate(self.config)
        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(f"- {error}" for error in errors)
            raise ConfigurationError(error_msg)
    
    def save_config(self, file_path: Optional[str] = None):
        """保存配置到文件"""
        target_file = file_path or self.config_file
        if not target_file:
            raise ConfigurationError("No config file specified for saving")
        
        try:
            config_dict = asdict(self.config)
            config_path = Path(target_file)
            
            if config_path.suffix.lower() in ['.yml', '.yaml']:
                content = yaml.dump(config_dict, default_flow_style=False, indent=2)
            else:
                content = json.dumps(config_dict, indent=2)
            
            config_path.write_text(content)
            logger.info(f"Configuration saved to {target_file}")
            
        except Exception as e:
            raise ConfigurationError(
                f"Failed to save config to {target_file}",
                config_key=target_file,
                cause=e
            )
    
    def get_config(self) -> Config:
        """获取配置副本"""
        with self._lock:
            # 返回深拷贝以防止意外修改
            import copy
            return copy.deepcopy(self.config)
    
    def update_config(self, **kwargs):
        """更新配置，支持使用'__'进行嵌套更新。"""
        with self._lock:
            for key, value in kwargs.items():
                try:
                    keys = key.split('__')
                    config_obj = self.config
                    # 遍历到父级对象
                    for k in keys[:-1]:
                        config_obj = getattr(config_obj, k)
                    
                    final_key = keys[-1]
                    if hasattr(config_obj, final_key):
                        setattr(config_obj, final_key, value)
                    else:
                        logger.warning(f"Unknown config key: {key}")
                except AttributeError:
                    logger.warning(f"Invalid config path in key: {key}")
            
            # 重新验证
            self._validate_config()
            
            # 更新时间戳
            self.config.last_updated = time.time()
            
            # 通知监听器
            self._notify_callbacks()
    
    def watch_config(self, callback: Callable[[Config], None]):
        """监听配置变化"""
        # 使用弱引用避免内存泄漏
        self._callbacks.append(weakref.ref(callback))
        
        # 启动文件监听
        if not self._watching and self.config_file:
            self._start_watching()
    
    def _start_watching(self):
        """开始监听配置文件变化"""
        self._watching = True
        self._watch_thread = threading.Thread(
            target=self._watch_loop,
            daemon=True
        )
        self._watch_thread.start()
    
    def _watch_loop(self):
        """文件监听循环"""
        while self._watching:
            try:
                if self.config_file and Path(self.config_file).exists():
                    current_mtime = Path(self.config_file).stat().st_mtime
                    
                    if current_mtime > self._last_modified:
                        logger.info("Config file changed, reloading...")
                        self.load_config()
                
                time.sleep(1.0)  # 检查间隔
                
            except Exception as e:
                logger.error(f"Config watch error: {e}")
                time.sleep(5.0)
    
    def _notify_callbacks(self):
        """通知配置变化回调"""
        # 清理失效的弱引用
        self._callbacks = [ref for ref in self._callbacks if ref() is not None]
        
        # 调用有效的回调
        for ref in self._callbacks:
            callback = ref()
            if callback:
                try:
                    callback(self.config)
                except Exception as e:
                    logger.error(f"Config callback error: {e}")
    
    def stop_watching(self):
        """停止监听"""
        self._watching = False
        if self._watch_thread:
            self._watch_thread.join(timeout=1)


# 全局配置管理器
_config_manager = None
_config_manager_lock = threading.Lock()


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    
    if _config_manager is None:
        with _config_manager_lock:
            if _config_manager is None:
                _config_manager = ConfigManager()
    
    return _config_manager


def get_config() -> Config:
    """获取当前配置"""
    return get_config_manager().get_config()


def update_config(**kwargs):
    """更新配置"""
    get_config_manager().update_config(**kwargs)


def watch_config_changes(callback: Callable[[Config], None]):
    """监听配置变化"""
    get_config_manager().watch_config(callback)