#!/usr/bin/env python3
"""
快速构建索引脚本
用于首次使用时快速构建代码索引
"""

import sys
import os
from pathlib import Path
import subprocess
import time

def check_build_index_exists():
    """检查build_index.py是否存在"""
    build_script = Path("build_index.py")
    if build_script.exists():
        return True
    else:
        print("❌ build_index.py 文件不存在")
        return False

def run_build_index():
    """运行构建索引脚本"""
    print("🔨 开始构建代码索引...")
    print("这可能需要几分钟时间，请耐心等待...")
    print()
    
    try:
        # 运行构建脚本
        start_time = time.time()
        
        result = subprocess.run([
            sys.executable, "build_index.py"
        ], text=True, timeout=600)  # 10分钟超时
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 索引构建成功！耗时: {duration:.1f}秒")
            
            # 检查生成的文件
            index_files = ["faiss_index.bin", "metadata.pkl"]
            for file in index_files:
                if Path(file).exists():
                    size = Path(file).stat().st_size / (1024 * 1024)  # MB
                    print(f"  📄 {file}: {size:.1f} MB")
                else:
                    print(f"  ❌ {file}: 未生成")
            
            return True
        else:
            print(f"❌ 索引构建失败，退出码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 构建超时，请检查是否有大量代码文件需要处理")
        return False
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def check_existing_index():
    """检查是否已存在索引文件"""
    index_files = ["faiss_index.bin", "metadata.pkl"]
    existing = []
    
    for file in index_files:
        if Path(file).exists():
            existing.append(file)
    
    if existing:
        print(f"📁 发现已存在的索引文件: {existing}")
        print("❓ 是否重新构建索引？(y/n): ", end="")
        try:
            choice = input().lower().strip()
            return choice in ['y', 'yes']
        except KeyboardInterrupt:
            print("\n⏹️  已取消")
            return False
    
    return True

def main():
    """主函数"""
    print("🚀 UniXcoder 代码索引构建工具")
    print("=" * 40)
    
    # 检查当前目录
    if not Path("config.json").exists():
        print("❌ 当前目录不是UniXcoder项目根目录")
        print("请在项目根目录下运行此脚本")
        return False
    
    # 检查构建脚本
    if not check_build_index_exists():
        return False
    
    # 检查现有索引
    if not check_existing_index():
        print("⏹️  已取消构建")
        return True
    
    # 运行构建
    success = run_build_index()
    
    if success:
        print()
        print("🎉 索引构建完成！")
        print("现在可以启动Streamlit应用:")
        print("  python demo_streamlit.py")
        print("  或者: ./start_streamlit.sh")
    else:
        print()
        print("❌ 索引构建失败")
        print("请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  构建已取消")
        sys.exit(0)
