"""
API服务层 - 为Streamlit前端提供后端功能接口
支持流式输出和代码片段展示
"""

import sys
import os
import json
import logging
import traceback
from pathlib import Path
from typing import Dict, List, Optional, Any, Iterator, Generator
from dataclasses import dataclass
from datetime import datetime
import threading
import queue
from contextlib import contextmanager

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入后端模块
try:
    from ask import CodeAssistant
    from logger import get_logger
    from config import get_config
except ImportError as e:
    print(f"无法导入后端模块: {e}")
    raise

@dataclass
class SearchResult:
    """搜索结果数据类"""
    similarity: float
    file_path: str
    start_line: int
    end_line: int
    code: str
    signature: Optional[str] = None
    parent_class: Optional[str] = None
    retrieval_method: str = "semantic"
    retrieval_methods: Optional[Dict[str, float]] = None

@dataclass
class QueryRequest:
    """查询请求数据类"""
    query: str
    context: Optional[List[Dict[str, str]]] = None

@dataclass
class QueryResponse:
    """查询响应数据类"""
    success: bool
    results: List[SearchResult]
    llm_response: str = ""
    error: Optional[str] = None
    execution_time: float = 0.0

class StreamingAPIService:
    """流式API服务类"""
    
    def __init__(self):
        """初始化API服务"""
        self.logger = get_logger(__name__)
        self.config = get_config()
        self.assistant = None
        self._initialized = False
        
    def initialize(self) -> Dict[str, Any]:
        """初始化后端服务"""
        try:
            if self._initialized:
                return {"success": True, "message": "Already initialized"}

            # 检查索引文件是否存在（在项目根目录）
            index_file = project_root / "faiss_index.bin"
            metadata_file = project_root / "metadata.pkl"

            if not index_file.exists() or not metadata_file.exists():
                missing_files = []
                if not index_file.exists():
                    missing_files.append("faiss_index.bin")
                if not metadata_file.exists():
                    missing_files.append("metadata.pkl")

                return {
                    "success": False,
                    "error": f"Index files not found: {', '.join(missing_files)}",
                    "suggestion": "Please run 'python build_index.py' to build the index first.",
                    "missing_files": missing_files
                }

            self.logger.info("Initializing CodeAssistant...")
            self.assistant = CodeAssistant()
            self._initialized = True

            return {
                "success": True,
                "message": "API service initialized successfully",
                "config": {
                    "model_name": self.config.model.name,
                    "llm_model": self.config.llm.model_name,
                    "stream_enabled": self.config.llm.stream
                }
            }

        except Exception as e:
            self.logger.error(f"Failed to initialize API service: {e}")
            return {
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def search_code(self, query: str) -> List[SearchResult]:
        """搜索代码片段"""
        if not self._initialized:
            raise RuntimeError("API service not initialized")
            
        try:
            # 执行搜索
            results = self.assistant.dynamic_search(query)
            
            # 转换为标准格式
            search_results = []
            for res in results:
                search_result = self._convert_to_search_result(res)
                if search_result:
                    search_results.append(search_result)
                    
            return search_results
            
        except Exception as e:
            self.logger.error(f"Search failed: {e}")
            raise
    
    def _convert_to_search_result(self, res: Dict[str, Any]) -> Optional[SearchResult]:
        """转换搜索结果为标准格式"""
        try:
            # 处理混合检索器的双重嵌套结构
            if 'chunk' in res and isinstance(res['chunk'], dict):
                outer_chunk = res['chunk']
                if 'chunk' in outer_chunk and isinstance(outer_chunk['chunk'], dict):
                    chunk_info = outer_chunk['chunk']
                else:
                    chunk_info = outer_chunk
                similarity = res.get('score', res.get('similarity', 0.0))
                retrieval_method = res.get('retrieval_method', 'unknown')
                retrieval_methods = res.get('retrieval_methods', {})
            else:
                chunk_info = res
                similarity = res.get('similarity', 0.0)
                retrieval_method = 'semantic'
                retrieval_methods = {}

            # 获取元数据
            meta = chunk_info.get('metadata', {}) if isinstance(chunk_info, dict) else {}
            code = chunk_info.get('code', '') if isinstance(chunk_info, dict) else ''
            
            return SearchResult(
                similarity=similarity,
                file_path=meta.get('file_path', 'N/A'),
                start_line=meta.get('start_line', 0),
                end_line=meta.get('end_line', 0),
                code=code,
                signature=meta.get('signature'),
                parent_class=meta.get('parent_class'),
                retrieval_method=retrieval_method,
                retrieval_methods=retrieval_methods
            )
            
        except Exception as e:
            self.logger.error(f"Failed to convert search result: {e}")
            return None
    
    def generate_streaming_response(self, query: str, results: List[SearchResult], 
                                  context: Optional[List[Dict[str, str]]] = None) -> Generator[Dict[str, Any], None, None]:
        """生成流式LLM响应"""
        if not self._initialized:
            raise RuntimeError("API service not initialized")
            
        if not self.assistant.llm_client:
            yield {
                "type": "error",
                "content": "LLM client not configured. Please set DEEPSEEK_API_KEY environment variable."
            }
            return
            
        try:
            # 转换结果格式
            converted_results = []
            for result in results:
                converted_results.append({
                    'chunk': {
                        'code': result.code,
                        'metadata': {
                            'file_path': result.file_path,
                            'start_line': result.start_line,
                            'end_line': result.end_line,
                            'signature': result.signature,
                            'parent_class': result.parent_class
                        }
                    },
                    'similarity': result.similarity
                })
            
            # 生成prompt
            if context:
                prompt = self.assistant.generate_llm_prompt_with_context(query, converted_results, context)
            else:
                prompt = self.assistant.generate_llm_prompt(query, converted_results)
            
            # 流式生成
            response = self.assistant.llm_client.chat.completions.create(
                model=self.config.llm.model_name,
                messages=[{"role": "user", "content": prompt}],
                stream=True,
                temperature=self.config.llm.temperature,
                max_tokens=self.config.llm.max_tokens
            )
            
            for chunk in response:
                content = chunk.choices[0].delta.content
                if content:
                    yield {
                        "type": "content",
                        "content": content
                    }
                    
            yield {
                "type": "done",
                "content": ""
            }
            
        except Exception as e:
            self.logger.error(f"Streaming generation failed: {e}")
            yield {
                "type": "error",
                "content": f"Error generating response: {str(e)}"
            }
    
    def query_with_streaming(self, request: QueryRequest) -> Generator[Dict[str, Any], None, None]:
        """完整的查询流程，包含搜索和流式响应"""
        start_time = datetime.now()
        
        try:
            # 1. 搜索阶段
            yield {
                "type": "status",
                "content": "🔍 Searching for relevant code..."
            }
            
            results = self.search_code(request.query)
            
            if not results:
                yield {
                    "type": "status",
                    "content": "❌ No relevant code found"
                }
                yield {
                    "type": "done",
                    "content": ""
                }
                return
            
            # 2. 返回搜索结果
            yield {
                "type": "search_results",
                "content": results
            }
            
            yield {
                "type": "status", 
                "content": f"✅ Found {len(results)} relevant code snippets. Generating response..."
            }
            
            # 3. 流式生成LLM响应
            yield {
                "type": "llm_start",
                "content": ""
            }
            
            for chunk in self.generate_streaming_response(request.query, results, request.context):
                yield chunk
                
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"Query failed: {e}")
            yield {
                "type": "error",
                "content": str(e),
                "execution_time": execution_time,
                "traceback": traceback.format_exc()
            }

# 全局API实例
_api_instance = None

def get_api_instance() -> StreamingAPIService:
    """获取API实例（单例模式）"""
    global _api_instance
    if _api_instance is None:
        _api_instance = StreamingAPIService()
    return _api_instance
