"""
代码引用查看器组件
提供紧凑的代码引用展示功能
"""

import streamlit as st
from typing import List, Dict, Any
from api_service import SearchResult

def get_similarity_class(similarity: float) -> str:
    """根据相似度获取CSS类名"""
    if similarity > 0.8:
        return "similarity-high"
    elif similarity > 0.6:
        return "similarity-medium"
    else:
        return "similarity-low"

def format_code_preview(code: str, max_lines: int = 5) -> str:
    """格式化代码预览"""
    if not code:
        return "无代码内容"
    
    lines = code.split('\n')
    if len(lines) <= max_lines:
        return code
    
    preview_lines = lines[:max_lines]
    return '\n'.join(preview_lines) + f'\n... (还有 {len(lines) - max_lines} 行)'

def display_compact_reference(result: SearchResult, index: int) -> None:
    """显示紧凑的代码引用"""
    # 文件名和相似度
    filename = result.file_path.split('/')[-1]
    similarity_class = get_similarity_class(result.similarity)
    
    # 引用卡片HTML
    reference_html = f'''
    <div class="reference-card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <strong>📄 {filename}</strong>
            <span class="similarity-badge {similarity_class}">{result.similarity:.3f}</span>
        </div>
        
        <div style="font-size: 0.8rem; color: #666; margin-bottom: 8px;">
            📍 行 {result.start_line}-{result.end_line}
            {f" · 🏗️ {result.parent_class}" if result.parent_class else ""}
            {f" · ✍️ {result.signature}" if result.signature else ""}
        </div>
    </div>
    '''
    
    st.markdown(reference_html, unsafe_allow_html=True)
    
    # 代码预览（可展开）
    with st.expander("查看代码", expanded=False):
        if result.code:
            # 显示完整代码
            st.code(result.code, language="python")
        else:
            st.warning("无代码内容")
        
        # 详细信息
        st.markdown("**详细信息:**")
        info_data = {
            "文件路径": result.file_path,
            "检索方法": result.retrieval_method,
            "相似度分数": f"{result.similarity:.6f}"
        }
        
        if result.retrieval_methods:
            methods_info = []
            for method, score in result.retrieval_methods.items():
                if score > 0:
                    methods_info.append(f"{method}: {score:.3f}")
            if methods_info:
                info_data["方法分数"] = ", ".join(methods_info)
        
        for key, value in info_data.items():
            st.markdown(f"- **{key}:** {value}")

def display_reference_panel(results: List[SearchResult]) -> None:
    """显示引用面板"""
    if not results:
        st.markdown('''
        <div style="text-align: center; padding: 20px; color: #666;">
            <p>🔍 暂无代码引用</p>
            <p style="font-size: 0.9rem;">发送问题后，相关的代码片段将在这里显示</p>
        </div>
        ''', unsafe_allow_html=True)
        return
    
    # 引用统计
    total_refs = len(results)
    high_quality = len([r for r in results if r.similarity > 0.7])
    
    st.markdown(f'''
    <div class="conversation-stats">
        <strong>📚 代码引用统计</strong><br>
        总计: {total_refs} 个 · 高质量: {high_quality} 个
    </div>
    ''', unsafe_allow_html=True)
    
    # 显示控制
    col1, col2 = st.columns(2)
    with col1:
        show_count = st.selectbox(
            "显示数量",
            options=[3, 5, 10, "全部"],
            index=0,
            key="ref_show_count"
        )
    
    with col2:
        sort_by = st.selectbox(
            "排序方式",
            options=["相似度", "文件名", "行号"],
            index=0,
            key="ref_sort_by"
        )
    
    # 排序结果
    if sort_by == "相似度":
        sorted_results = sorted(results, key=lambda x: x.similarity, reverse=True)
    elif sort_by == "文件名":
        sorted_results = sorted(results, key=lambda x: x.file_path)
    else:  # 行号
        sorted_results = sorted(results, key=lambda x: x.start_line)
    
    # 确定显示数量
    if show_count == "全部":
        display_results = sorted_results
    else:
        display_results = sorted_results[:show_count]
    
    # 显示引用
    for i, result in enumerate(display_results):
        display_compact_reference(result, i)
    
    # 显示剩余数量提示
    if show_count != "全部" and len(results) > show_count:
        remaining = len(results) - show_count
        st.info(f"还有 {remaining} 个引用未显示，可调整上方设置查看更多")

def display_reference_summary(results: List[SearchResult]) -> None:
    """显示引用摘要"""
    if not results:
        return
    
    # 统计信息
    total_count = len(results)
    avg_similarity = sum(r.similarity for r in results) / total_count
    max_similarity = max(r.similarity for r in results)
    min_similarity = min(r.similarity for r in results)
    
    # 文件分布
    files = {}
    for result in results:
        filename = result.file_path.split('/')[-1]
        files[filename] = files.get(filename, 0) + 1
    
    # 检索方法分布
    methods = {}
    for result in results:
        method = result.retrieval_method
        methods[method] = methods.get(method, 0) + 1
    
    # 显示摘要
    with st.expander("📊 引用摘要", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**📈 相似度统计**")
            st.markdown(f"- 平均: {avg_similarity:.3f}")
            st.markdown(f"- 最高: {max_similarity:.3f}")
            st.markdown(f"- 最低: {min_similarity:.3f}")
            
            st.markdown("**🔍 检索方法**")
            for method, count in methods.items():
                percentage = (count / total_count) * 100
                st.markdown(f"- {method}: {count} ({percentage:.1f}%)")
        
        with col2:
            st.markdown("**📁 文件分布**")
            sorted_files = sorted(files.items(), key=lambda x: x[1], reverse=True)
            for filename, count in sorted_files[:5]:  # 显示前5个文件
                percentage = (count / total_count) * 100
                st.markdown(f"- {filename}: {count} ({percentage:.1f}%)")
            
            if len(files) > 5:
                st.markdown(f"- ... 还有 {len(files) - 5} 个文件")

def display_reference_filters(results: List[SearchResult]) -> List[SearchResult]:
    """显示引用过滤器并返回过滤后的结果"""
    if not results:
        return results
    
    with st.expander("🔧 过滤选项", expanded=False):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 相似度过滤
            min_similarity = st.slider(
                "最低相似度",
                min_value=0.0,
                max_value=1.0,
                value=0.0,
                step=0.1,
                key="ref_min_similarity"
            )
        
        with col2:
            # 文件类型过滤
            all_files = list(set(r.file_path.split('/')[-1] for r in results))
            selected_files = st.multiselect(
                "文件过滤",
                options=all_files,
                default=all_files,
                key="ref_file_filter"
            )
        
        with col3:
            # 检索方法过滤
            all_methods = list(set(r.retrieval_method for r in results))
            selected_methods = st.multiselect(
                "检索方法",
                options=all_methods,
                default=all_methods,
                key="ref_method_filter"
            )
    
    # 应用过滤器
    filtered_results = []
    for result in results:
        # 相似度过滤
        if result.similarity < min_similarity:
            continue
        
        # 文件过滤
        filename = result.file_path.split('/')[-1]
        if filename not in selected_files:
            continue
        
        # 方法过滤
        if result.retrieval_method not in selected_methods:
            continue
        
        filtered_results.append(result)
    
    if len(filtered_results) != len(results):
        st.info(f"过滤后显示 {len(filtered_results)}/{len(results)} 个引用")
    
    return filtered_results
