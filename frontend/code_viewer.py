"""
代码片段展示组件
提供丰富的代码展示和相似度可视化功能
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from typing import List, Dict, Any
import pandas as pd
from api_service import SearchResult

def display_similarity_chart(results: List[SearchResult]):
    """显示相似度分数图表"""
    if not results:
        return
    
    # 准备数据
    data = []
    for i, result in enumerate(results):
        data.append({
            'Index': f"结果 {i+1}",
            'Similarity': result.similarity,
            'File': result.file_path.split('/')[-1],  # 只显示文件名
            'Method': result.retrieval_method
        })
    
    df = pd.DataFrame(data)
    
    # 创建条形图
    fig = px.bar(
        df, 
        x='Index', 
        y='Similarity',
        color='Similarity',
        color_continuous_scale='RdYlGn',
        hover_data=['File', 'Method'],
        title="代码片段相似度分数"
    )
    
    fig.update_layout(
        height=300,
        showlegend=False,
        xaxis_title="搜索结果",
        yaxis_title="相似度分数"
    )
    
    st.plotly_chart(fig, use_container_width=True)

def display_retrieval_methods_breakdown(results: List[SearchResult]):
    """显示检索方法分解图"""
    if not results:
        return
    
    # 收集所有检索方法的分数
    method_scores = {}
    for result in results:
        if result.retrieval_methods:
            for method, score in result.retrieval_methods.items():
                if score > 0:
                    if method not in method_scores:
                        method_scores[method] = []
                    method_scores[method].append(score)
    
    if not method_scores:
        return
    
    # 计算平均分数
    avg_scores = {method: sum(scores)/len(scores) for method, scores in method_scores.items()}
    
    # 创建饼图
    fig = px.pie(
        values=list(avg_scores.values()),
        names=list(avg_scores.keys()),
        title="检索方法贡献度分布"
    )
    
    fig.update_layout(height=300)
    st.plotly_chart(fig, use_container_width=True)

def display_code_snippet_card(result: SearchResult, index: int):
    """显示单个代码片段卡片"""
    # 相似度颜色映射
    if result.similarity > 0.8:
        similarity_color = "#28a745"  # 绿色
        similarity_emoji = "🟢"
    elif result.similarity > 0.6:
        similarity_color = "#ffc107"  # 黄色
        similarity_emoji = "🟡"
    elif result.similarity > 0.4:
        similarity_color = "#fd7e14"  # 橙色
        similarity_emoji = "🟠"
    else:
        similarity_color = "#dc3545"  # 红色
        similarity_emoji = "🔴"
    
    # 卡片容器
    with st.container():
        # 头部信息
        col1, col2, col3 = st.columns([3, 1, 1])
        
        with col1:
            st.markdown(f"### 📄 结果 {index + 1}: {result.file_path.split('/')[-1]}")
        
        with col2:
            st.markdown(f"**行数:** {result.start_line}-{result.end_line}")
        
        with col3:
            st.markdown(
                f'<div style="text-align: center;">'
                f'{similarity_emoji} <span style="color: {similarity_color}; font-weight: bold; font-size: 1.2em;">'
                f'{result.similarity:.4f}</span></div>',
                unsafe_allow_html=True
            )
        
        # 文件路径和上下文信息
        st.markdown(f"**📁 文件路径:** `{result.file_path}`")
        
        if result.parent_class:
            st.markdown(f"**🏗️ 所属类:** `{result.parent_class}`")
        
        if result.signature:
            st.markdown(f"**✍️ 函数签名:** `{result.signature}`")
        
        # 检索方法信息
        col1, col2 = st.columns([1, 1])
        with col1:
            st.markdown(f"**🔍 主要检索方法:** {result.retrieval_method}")
        
        with col2:
            if result.retrieval_methods:
                methods_info = []
                for method, score in result.retrieval_methods.items():
                    if score > 0:
                        methods_info.append(f"{method}: {score:.3f}")
                if methods_info:
                    st.markdown(f"**📊 方法分数:** {', '.join(methods_info)}")
        
        # 代码内容
        st.markdown("**💻 代码内容:**")
        
        if result.code:
            # 处理代码显示
            code_lines = result.code.split('\n')
            
            # 如果代码太长，提供展开/折叠选项
            if len(code_lines) > 20:
                show_full = st.checkbox(f"显示完整代码 ({len(code_lines)} 行)", key=f"show_full_{index}")
                
                if show_full:
                    display_lines = code_lines
                else:
                    display_lines = code_lines[:15]
                    if len(code_lines) > 15:
                        display_lines.append("... (更多代码，请勾选上方复选框查看)")
            else:
                display_lines = code_lines
            
            # 添加行号并显示
            numbered_code = []
            for line_num, line in enumerate(display_lines, result.start_line):
                if line == "... (更多代码，请勾选上方复选框查看)":
                    numbered_code.append(line)
                else:
                    numbered_code.append(f"{line_num:4d} | {line}")
            
            st.code('\n'.join(numbered_code), language='python')
        else:
            st.warning("⚠️ 无代码内容")
        
        st.divider()

def display_enhanced_search_results(results: List[SearchResult]):
    """增强的搜索结果展示"""
    if not results:
        st.warning("🔍 没有找到相关的代码片段")
        return
    
    # 标题和统计信息
    st.markdown(f"## 🎯 搜索结果 ({len(results)} 个匹配)")
    
    # 创建标签页
    tab1, tab2, tab3 = st.tabs(["📋 详细结果", "📊 相似度分析", "🔍 检索方法分析"])
    
    with tab1:
        # 排序选项
        col1, col2 = st.columns([1, 3])
        with col1:
            sort_by = st.selectbox(
                "排序方式:",
                ["相似度 (降序)", "相似度 (升序)", "文件名", "行号"],
                key="sort_results"
            )
        
        # 根据选择排序
        if sort_by == "相似度 (降序)":
            sorted_results = sorted(results, key=lambda x: x.similarity, reverse=True)
        elif sort_by == "相似度 (升序)":
            sorted_results = sorted(results, key=lambda x: x.similarity)
        elif sort_by == "文件名":
            sorted_results = sorted(results, key=lambda x: x.file_path)
        else:  # 行号
            sorted_results = sorted(results, key=lambda x: x.start_line)
        
        # 显示每个结果
        for i, result in enumerate(sorted_results):
            display_code_snippet_card(result, i)
    
    with tab2:
        st.markdown("### 📈 相似度分数分布")
        display_similarity_chart(results)
        
        # 统计信息
        similarities = [r.similarity for r in results]
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("平均相似度", f"{sum(similarities)/len(similarities):.4f}")
        with col2:
            st.metric("最高相似度", f"{max(similarities):.4f}")
        with col3:
            st.metric("最低相似度", f"{min(similarities):.4f}")
        with col4:
            high_quality = len([s for s in similarities if s > 0.7])
            st.metric("高质量结果", f"{high_quality}/{len(results)}")
    
    with tab3:
        st.markdown("### 🔍 检索方法效果分析")
        display_retrieval_methods_breakdown(results)
        
        # 方法统计
        method_counts = {}
        for result in results:
            method = result.retrieval_method
            method_counts[method] = method_counts.get(method, 0) + 1
        
        if method_counts:
            st.markdown("**检索方法使用统计:**")
            for method, count in method_counts.items():
                percentage = (count / len(results)) * 100
                st.markdown(f"- **{method}**: {count} 个结果 ({percentage:.1f}%)")

def display_code_context_info(result: SearchResult):
    """显示代码上下文信息"""
    st.markdown("#### 🔍 代码上下文")
    
    info_data = {
        "属性": ["文件路径", "起始行", "结束行", "检索方法", "相似度分数"],
        "值": [
            result.file_path,
            str(result.start_line),
            str(result.end_line), 
            result.retrieval_method,
            f"{result.similarity:.4f}"
        ]
    }
    
    if result.parent_class:
        info_data["属性"].append("所属类")
        info_data["值"].append(result.parent_class)
    
    if result.signature:
        info_data["属性"].append("函数签名")
        info_data["值"].append(result.signature)
    
    df = pd.DataFrame(info_data)
    st.table(df)
