"""
UniXcoder RAG 智能代码检索与问答系统 - Streamlit前端
支持流式输出和代码片段展示
"""

import streamlit as st
import time
import json
from datetime import datetime
from typing import List, Dict, Any
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置离线模式环境变量（在导入任何transformers相关模块之前）
try:
    from config import get_config
    config = get_config()
    if getattr(config.model, 'local_files_only', False):
        os.environ['TRANSFORMERS_OFFLINE'] = '1'
        os.environ['HF_HUB_OFFLINE'] = '1'
        os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'
except Exception as e:
    st.warning(f"Warning: Failed to set offline mode: {e}")

# 导入API服务和组件
try:
    from api_service import get_api_instance, QueryRequest, SearchResult
    from code_viewer import display_enhanced_search_results
except ImportError as e:
    st.error(f"无法导入API服务: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="UniXcoder RAG 智能代码检索",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    font-weight: bold;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}

.chat-container {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    margin-bottom: 20px;
}

.message-wrapper {
    display: flex;
    margin: 15px 0;
    align-items: flex-end;
}

.user-message-wrapper {
    justify-content: flex-end;
}

.ai-message-wrapper {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 12px 18px;
    border-radius: 20px;
    position: relative;
    word-wrap: break-word;
}

.user-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
    margin-left: 20px;
}

.ai-bubble {
    background: white;
    color: #333;
    border-bottom-left-radius: 5px;
    margin-right: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e8ed;
}

.generating-bubble {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffeaa7;
    animation: pulse 2s infinite;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin: 0 10px;
    flex-shrink: 0;
}

.user-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai-avatar {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.message-time {
    font-size: 0.75rem;
    color: #666;
    margin-top: 5px;
    text-align: center;
}

.message-info {
    font-size: 0.8rem;
    color: #888;
    margin-top: 5px;
    font-style: italic;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.typing-indicator {
    display: flex;
    align-items: center;
    padding: 10px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #999;
    margin: 0 2px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

.input-container {
    background: white;
    border-radius: 25px;
    padding: 10px 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e8ed;
}

.reference-panel {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e1e8ed;
}

.reference-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.reference-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    margin: 8px 0;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.reference-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.similarity-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    color: white;
}

.similarity-high { background-color: #28a745; }
.similarity-medium { background-color: #ffc107; color: #000; }
.similarity-low { background-color: #dc3545; }

.collapsible-section {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin: 10px 0;
    overflow: hidden;
}

.collapsible-header {
    background: #f8f9fa;
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
}

.collapsible-header:hover {
    background: #e9ecef;
}

.collapsible-content {
    padding: 15px;
    background: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin: 10px 0;
}

.stat-item {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: white;
    border-radius: 15px;
    margin: 20px 0;
}

.welcome-message {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin: 20px 0;
}

/* 平滑滚动和固定定位 */
html {
    scroll-behavior: smooth;
}

.main-content {
    scroll-behavior: smooth;
}

/* 避免页面跳转 */
.stApp {
    scroll-behavior: smooth;
}

/* 流式输出优化 */
.streaming-content {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 减少布局偏移 */
.message-wrapper {
    min-height: 60px;
    transition: all 0.2s ease;
}

/* 固定输入区域 */
.input-container {
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 100;
    border-top: 1px solid #e0e0e0;
    padding: 20px;
    margin-top: 20px;
}
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """初始化会话状态"""
    if 'api_service' not in st.session_state:
        st.session_state.api_service = get_api_instance()

    if 'initialized' not in st.session_state:
        st.session_state.initialized = False

    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []

    if 'current_results' not in st.session_state:
        st.session_state.current_results = []

    if 'current_query' not in st.session_state:
        st.session_state.current_query = ""

    if 'is_generating' not in st.session_state:
        st.session_state.is_generating = False

    if 'conversation_id' not in st.session_state:
        st.session_state.conversation_id = 1

    if 'show_references' not in st.session_state:
        st.session_state.show_references = True

    if 'current_response' not in st.session_state:
        st.session_state.current_response = ""

def initialize_system():
    """初始化系统"""
    if st.session_state.initialized:
        return True

    with st.spinner("正在初始化系统..."):
        result = st.session_state.api_service.initialize()

        if result['success']:
            st.session_state.initialized = True
            st.success("✅ 系统初始化成功！")

            # 显示配置信息
            config = result.get('config', {})
            st.info(f"模型: {config.get('model_name', 'N/A')} | LLM: {config.get('llm_model', 'N/A')}")
            return True
        else:
            error_msg = result.get('error', '未知错误')
            st.error(f"❌ 初始化失败: {error_msg}")

            # 检查是否是索引文件缺失
            if 'Index files not found' in error_msg:
                st.warning("🔨 需要先构建代码索引")

                missing_files = result.get('missing_files', [])
                if missing_files:
                    st.write("缺少的文件:")
                    for file in missing_files:
                        st.write(f"  - {file}")

                st.info("💡 解决方案:")
                st.code("python build_index.py", language="bash")

                suggestion = result.get('suggestion')
                if suggestion:
                    st.write(suggestion)

                # 提供构建索引的按钮
                if st.button("🔨 构建索引", type="primary"):
                    build_index_in_streamlit()
            else:
                with st.expander("错误详情"):
                    st.code(result.get('traceback', ''))
            return False

def build_index_in_streamlit():
    """在Streamlit中构建索引"""
    import subprocess
    import sys

    with st.spinner("正在构建代码索引，请稍候..."):
        try:
            # 创建进度条
            progress_bar = st.progress(0)
            status_text = st.empty()

            status_text.text("📝 正在分析代码文件...")
            progress_bar.progress(25)

            # 运行构建索引脚本（在项目根目录）
            result = subprocess.run([
                sys.executable, "build_index.py"
            ], capture_output=True, text=True, cwd=project_root)

            progress_bar.progress(75)
            status_text.text("🔍 正在生成索引...")

            if result.returncode == 0:
                progress_bar.progress(100)
                status_text.text("✅ 索引构建完成")
                st.success("🎉 索引构建成功！现在可以重新初始化系统。")

                # 清除初始化状态，允许重新初始化
                st.session_state.initialized = False
                st.session_state.api_service._initialized = False

                # 减少等待时间，避免不必要的刷新
                time.sleep(0.5)
            else:
                st.error("❌ 索引构建失败")
                st.code(result.stderr, language="text")

        except Exception as e:
            st.error(f"❌ 构建过程中发生错误: {str(e)}")
        finally:
            # 清理进度显示
            progress_bar.empty()
            status_text.empty()

def display_search_results(results: List[SearchResult]):
    """显示搜索结果 - 使用增强的展示组件"""
    display_enhanced_search_results(results)

def display_message_bubble(message: Dict[str, Any]):
    """显示气泡样式的对话消息"""
    role = message["role"]
    content = message["content"]
    timestamp = message.get("timestamp", "")
    results_count = message.get("results_count", 0)

    if role == "user":
        # 用户消息气泡
        st.markdown(f'''
        <div class="message-wrapper user-message-wrapper">
            <div class="message-bubble user-bubble">
                {content}
                <div class="message-time">{timestamp}</div>
            </div>
            <div class="message-avatar user-avatar">👤</div>
        </div>
        ''', unsafe_allow_html=True)

    else:
        # AI消息气泡 - 显示引用和回答
        reference_info = f"基于 {results_count} 个代码片段" if results_count > 0 else ""

        # 获取搜索结果（优先从消息中获取，否则从当前状态获取）
        search_results = message.get("search_results", [])
        if not search_results and hasattr(st.session_state, 'current_results'):
            search_results = st.session_state.current_results

        # 显示代码引用（如果有）
        if results_count > 0 and search_results:
            display_references_before_answer(search_results)

        # 创建AI消息容器
        st.markdown(f'''
        <div class="message-wrapper ai-message-wrapper">
            <div class="message-avatar ai-avatar">🤖</div>
            <div class="message-bubble ai-bubble">
        ''', unsafe_allow_html=True)

        # 使用streamlit的markdown渲染AI回答
        st.markdown(content)

        # 添加时间和引用信息
        info_html = f'<div class="message-time">{timestamp}'
        if reference_info:
            info_html += f' · {reference_info}'
        info_html += '</div></div></div>'

        st.markdown(info_html, unsafe_allow_html=True)

def display_references_before_answer(results: List[SearchResult]):
    """在AI回答前显示代码引用"""
    if not results:
        return

    # 引用统计
    total_refs = len(results)
    high_quality = len([r for r in results if r.similarity > 0.7])

    # 默认收起的引用展示
    with st.expander(f"📚 代码引用 ({total_refs} 个片段，{high_quality} 个高质量)", expanded=False):
        # 引用统计
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("总引用", total_refs)
        with col2:
            st.metric("高质量", high_quality)
        with col3:
            avg_similarity = sum(r.similarity for r in results) / len(results)
            st.metric("平均相似度", f"{avg_similarity:.3f}")

        # 显示引用列表
        for i, result in enumerate(results):
            filename = result.file_path.split('/')[-1]
            similarity_class = "🟢" if result.similarity > 0.8 else "🟡" if result.similarity > 0.6 else "🔴"

            with st.expander(f"{similarity_class} {filename} (相似度: {result.similarity:.3f})", expanded=False):
                # 基本信息
                col1, col2 = st.columns(2)
                with col1:
                    st.markdown(f"**📍 位置:** {result.start_line}-{result.end_line}")
                    if result.parent_class:
                        st.markdown(f"**🏗️ 类:** {result.parent_class}")
                with col2:
                    if result.signature:
                        st.markdown(f"**✍️ 函数:** {result.signature}")
                    st.markdown(f"**🔍 检索方法:** {result.retrieval_method}")

                # 代码预览
                if result.code:
                    st.markdown("**代码片段:**")
                    st.code(result.code, language="python")

                # 文件路径
                st.markdown(f"**📁 文件路径:** `{result.file_path}`")

def display_conversation_history():
    """显示对话历史 - 气泡样式"""
    if not st.session_state.conversation_history and not st.session_state.is_generating:
        st.markdown('''
        <div class="welcome-message">
            <h3>💬 欢迎使用 UniXcoder RAG 智能助手</h3>
            <p>我可以帮您分析代码、回答技术问题、提供实现建议</p>
            <p>在下方输入您的问题开始对话吧！</p>
        </div>
        ''', unsafe_allow_html=True)
        return

    # 对话容器
    st.markdown('<div class="chat-container">', unsafe_allow_html=True)

    # 显示所有对话消息
    for message in st.session_state.conversation_history:
        display_message_bubble(message)

    # 如果正在生成回答，显示打字指示器和当前响应
    if st.session_state.is_generating:
        current_time = datetime.now().strftime("%H:%M:%S")
        current_content = st.session_state.current_response

        if current_content:
            # 显示正在生成的内容
            st.markdown(f'''
            <div class="message-wrapper ai-message-wrapper">
                <div class="message-avatar ai-avatar">🤖</div>
                <div class="message-bubble ai-bubble generating-bubble">
            ''', unsafe_allow_html=True)

            # 使用markdown渲染正在生成的内容
            st.markdown(current_content)

            st.markdown(f'''
                <div class="message-time">{current_time} · 正在生成...</div>
                </div>
            </div>
            ''', unsafe_allow_html=True)
        else:
            # 显示打字指示器
            st.markdown(f'''
            <div class="message-wrapper ai-message-wrapper">
                <div class="message-avatar ai-avatar">🤖</div>
                <div class="message-bubble ai-bubble generating-bubble">
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <div class="message-time">{current_time} · 正在思考...</div>
                </div>
            </div>
            ''', unsafe_allow_html=True)

    st.markdown('</div>', unsafe_allow_html=True)

    # 自动滚动到底部
    st.markdown('''
    <script>
    setTimeout(function() {
        var container = document.querySelector('.chat-container');
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    }, 100);
    </script>
    ''', unsafe_allow_html=True)



def manage_conversation():
    """对话管理功能"""
    st.sidebar.markdown("### ⚙️ 系统控制")

    # 对话统计
    total_messages = len(st.session_state.conversation_history)
    user_messages = len([m for m in st.session_state.conversation_history if m["role"] == "user"])

    col1, col2 = st.sidebar.columns(2)
    with col1:
        st.metric("对话轮数", user_messages)
    with col2:
        st.metric("总消息", total_messages)

    # 对话控制按钮
    col1, col2 = st.sidebar.columns(2)

    with col1:
        if st.button("🆕 新对话", use_container_width=True):
            start_new_conversation()

    with col2:
        if st.button("⏹️ 停止生成", use_container_width=True, disabled=not st.session_state.is_generating):
            stop_generation()

    # 对话设置
    st.sidebar.markdown("#### ⚙️ 对话设置")

    # 上下文长度设置
    context_length = st.sidebar.slider(
        "上下文长度",
        min_value=0,
        max_value=20,
        value=10,
        help="包含在上下文中的历史消息数量"
    )
    st.session_state.context_length = context_length

    # 对话历史预览
    if st.session_state.conversation_history:
        st.sidebar.markdown("#### 📜 最近对话")
        recent_messages = st.session_state.conversation_history[-4:]  # 显示最近4条

        for msg in recent_messages:
            if msg["role"] == "user":
                preview = msg["content"][:25] + "..." if len(msg["content"]) > 25 else msg["content"]
                st.sidebar.markdown(f"👤 _{preview}_")
            else:
                st.sidebar.markdown(f"🤖 _AI回答_")

def start_new_conversation():
    """开始新对话"""
    st.session_state.conversation_history = []
    st.session_state.current_results = []
    st.session_state.current_query = ""
    st.session_state.current_response = ""
    st.session_state.is_generating = False
    st.session_state.conversation_id += 1
    st.success("🆕 已开始新对话")
    # 移除立即刷新，让用户看到成功消息

def stop_generation():
    """停止生成"""
    st.session_state.is_generating = False
    st.warning("⏹️ 已停止生成")
    # 移除立即刷新，让用户看到停止消息

def stream_response_generator(api_service, request):
    """生成器函数，用于Streamlit的write_stream"""
    try:
        for chunk in api_service.query_with_streaming(request):
            chunk_type = chunk.get('type')
            content = chunk.get('content')

            if chunk_type == 'content' and content:
                yield content
    except Exception as e:
        yield f"\n\n❌ 生成过程中出现错误: {str(e)}"

def perform_streaming_query(query: str, context: List[Dict[str, str]] = None):
    """执行流式查询 - 使用Streamlit原生流式功能"""
    # 设置生成状态
    st.session_state.is_generating = True
    st.session_state.current_response = ""

    # 添加用户消息到历史
    st.session_state.conversation_history.append({
        "role": "user",
        "content": query,
        "timestamp": datetime.now().strftime("%H:%M:%S")
    })

    # 显示用户消息
    display_message_bubble(st.session_state.conversation_history[-1])

    # 创建请求
    request = QueryRequest(query=query, context=context)

    # 执行搜索阶段
    search_results = []
    status_placeholder = st.empty()

    try:
        # 先执行搜索获取结果
        status_placeholder.info("🔍 正在搜索相关代码...")

        # 模拟搜索过程（实际应该从API获取）
        for chunk in st.session_state.api_service.query_with_streaming(request):
            chunk_type = chunk.get('type')
            content = chunk.get('content')

            if chunk_type == 'status':
                status_placeholder.info(content)
            elif chunk_type == 'search_results':
                search_results = content
                st.session_state.current_results = search_results
                status_placeholder.success(f"✅ 找到 {len(search_results)} 个相关代码片段")
                break

        # 显示引用（如果有）
        if search_results:
            display_references_before_answer(search_results)

        # AI回答部分
        st.markdown('''
        <div class="message-wrapper ai-message-wrapper">
            <div class="message-avatar ai-avatar">🤖</div>
            <div class="message-bubble ai-bubble">
        ''', unsafe_allow_html=True)

        # 使用Streamlit的write_stream进行流式输出
        status_placeholder.info("🤖 AI正在生成回答...")

        # 创建流式响应生成器
        def response_generator():
            llm_content = ""
            try:
                for chunk in st.session_state.api_service.query_with_streaming(request):
                    chunk_type = chunk.get('type')
                    content = chunk.get('content')

                    if chunk_type == 'llm_start':
                        continue
                    elif chunk_type == 'content' and content:
                        llm_content += content
                        yield content
                    elif chunk_type == 'done':
                        break
                    elif chunk_type == 'error':
                        yield f"\n\n❌ 错误: {content}"
                        break

                # 保存完整回答
                st.session_state.current_response = llm_content

            except Exception as e:
                yield f"\n\n❌ 生成过程中出现错误: {str(e)}"

        # 使用write_stream进行流式输出
        response_content = st.write_stream(response_generator())

        # 添加时间信息
        current_time = datetime.now().strftime("%H:%M:%S")
        results_info = f" · 基于 {len(search_results)} 个代码片段" if search_results else ""
        st.markdown(f'''
            <div class="message-time">{current_time}{results_info}</div>
        </div>
        </div>
        ''', unsafe_allow_html=True)

        status_placeholder.success("✅ 回答生成完成")

        # 保存AI回答到对话历史
        if response_content:
            st.session_state.conversation_history.append({
                "role": "assistant",
                "content": response_content,
                "timestamp": current_time,
                "results_count": len(search_results),
                "search_results": search_results
            })

    except Exception as e:
        st.error(f"查询过程中发生错误: {str(e)}")
    finally:
        # 完成生成
        st.session_state.is_generating = False
        st.session_state.current_response = ""

        # 清除状态显示
        status_placeholder.empty()

        # 短暂等待后刷新（减少刷新频率）
        time.sleep(0.3)
        st.rerun()

def main():
    """主应用函数"""
    # 初始化会话状态
    initialize_session_state()

    # 主标题
    st.markdown('<h1 class="main-header">🔍 UniXcoder RAG 智能代码检索</h1>', unsafe_allow_html=True)

    # 侧边栏
    with st.sidebar:
        st.header("⚙️ 系统控制")

        # 初始化按钮
        if st.button("🚀 初始化系统", type="primary"):
            initialize_system()

        # 系统状态
        if st.session_state.initialized:
            st.success("✅ 系统已就绪")
        else:
            st.warning("⚠️ 系统未初始化")

        # 对话管理
        manage_conversation()

    # 主内容区域
    if not st.session_state.initialized:
        st.info("� 请先在侧边栏初始化系统")
        return

    # 对话区域（只在非生成状态显示）
    if not st.session_state.is_generating:
        display_conversation_history()

    # 输入区域
    st.markdown('<div class="input-container">', unsafe_allow_html=True)

    # 示例问题
    example_queries = [
        "代码分块算法是如何工作的？",
        "如何实现语义搜索功能？",
        "UniXcoder模型的主要特点是什么？",
        "混合检索器的实现原理",
        "如何优化检索性能？"
    ]

    with st.expander("💡 示例问题", expanded=False):
        cols = st.columns(2)
        for i, example in enumerate(example_queries):
            with cols[i % 2]:
                if st.button(f"📝 {example}", key=f"example_{i}", use_container_width=True):
                    st.session_state.current_query = example
                    # 移除立即刷新，让用户在输入框中看到问题

    # 查询输入框
    query = st.text_area(
        "💭 输入您的问题:",
        value=st.session_state.current_query,
        height=80,
        placeholder="例如：代码分块算法是如何工作的？",
        key="query_input",
        help="支持多轮对话，我会记住之前的对话内容"
    )

    # 按钮区域
    col1, col2 = st.columns([1, 1])

    with col1:
        send_button = st.button(
            "🚀 发送消息",
            type="primary",
            disabled=not query.strip() or st.session_state.is_generating,
            use_container_width=True
        )

    with col2:
        if st.button(
            "⏹️ 停止生成",
            disabled=not st.session_state.is_generating,
            use_container_width=True
        ):
            stop_generation()

    st.markdown('</div>', unsafe_allow_html=True)

    # 处理发送
    if send_button and query.strip():
        # 清空输入框
        st.session_state.current_query = ""

        # 准备上下文（最近的对话历史）
        context_length = getattr(st.session_state, 'context_length', 10)
        context = []
        recent_history = st.session_state.conversation_history[-context_length*2:] if context_length > 0 else []

        for msg in recent_history:
            context.append({
                "role": msg["role"],
                "content": msg["content"]
            })

        # 执行流式查询
        perform_streaming_query(query.strip(), context if context else None)

if __name__ == "__main__":
    main()
