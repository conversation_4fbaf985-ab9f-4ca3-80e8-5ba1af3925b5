"""
资源管理器

提供统一的资源管理、内存监控和清理机制。
"""

import gc
import psutil
import torch
import threading
import time
import weakref
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass
from enum import Enum

from logger import get_logger
from exceptions import BaseServiceError, ErrorCode, resource_manager

logger = get_logger(__name__)


class ResourceType(Enum):
    """资源类型"""
    MODEL = "model"
    CACHE = "cache"
    INDEX = "index"
    TEMP_FILE = "temp_file"
    NETWORK = "network"


@dataclass
class ResourceInfo:
    """资源信息"""
    resource_id: str
    resource_type: ResourceType
    resource: Any
    created_at: float
    memory_usage_mb: float = 0.0
    last_accessed: float = 0.0
    cleanup_func: Optional[Callable] = None


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, warning_threshold_mb: int = 8192, critical_threshold_mb: int = 12288):
        self.warning_threshold_mb = warning_threshold_mb
        self.critical_threshold_mb = critical_threshold_mb
        self.process = psutil.Process()
        self.callbacks = {
            'warning': [],
            'critical': [],
            'normal': []
        }
        self._monitoring = False
        self._monitor_thread = None
        self._lock = threading.Lock()
    
    def start_monitoring(self, interval: float = 30.0):
        """开始内存监控"""
        with self._lock:
            if not self._monitoring:
                self._monitoring = True
                self._monitor_thread = threading.Thread(
                    target=self._monitor_loop,
                    args=(interval,),
                    daemon=True
                )
                self._monitor_thread.start()
                logger.info("Memory monitoring started")
    
    def stop_monitoring(self):
        """停止内存监控"""
        with self._lock:
            self._monitoring = False
            if self._monitor_thread:
                self._monitor_thread.join(timeout=1)
    
    def add_callback(self, level: str, callback: Callable[[float], None]):
        """添加内存警告回调"""
        if level in self.callbacks:
            self.callbacks[level].append(callback)
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        memory_info = self.process.memory_info()
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': self.process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self._monitoring:
            try:
                memory_usage = self.get_memory_usage()
                rss_mb = memory_usage['rss_mb']
                
                # 判断内存使用级别
                if rss_mb >= self.critical_threshold_mb:
                    self._trigger_callbacks('critical', rss_mb)
                elif rss_mb >= self.warning_threshold_mb:
                    self._trigger_callbacks('warning', rss_mb)
                else:
                    self._trigger_callbacks('normal', rss_mb)
                
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                time.sleep(interval)
    
    def _trigger_callbacks(self, level: str, memory_mb: float):
        """触发回调"""
        for callback in self.callbacks[level]:
            try:
                callback(memory_mb)
            except Exception as e:
                logger.error(f"Memory callback error: {e}")


class ResourceRegistry:
    """资源注册表"""
    
    def __init__(self):
        self.resources: Dict[str, ResourceInfo] = {}
        self._lock = threading.RLock()
        self._weak_refs: Dict[str, weakref.ReferenceType] = {}
    
    def register(
        self,
        resource_id: str,
        resource: Any,
        resource_type: ResourceType,
        cleanup_func: Optional[Callable] = None,
        memory_usage_mb: float = 0.0
    ) -> str:
        """注册资源"""
        with self._lock:
            resource_info = ResourceInfo(
                resource_id=resource_id,
                resource_type=resource_type,
                resource=resource,
                created_at=time.time(),
                memory_usage_mb=memory_usage_mb,
                last_accessed=time.time(),
                cleanup_func=cleanup_func
            )
            
            self.resources[resource_id] = resource_info
            
            # 创建弱引用以便自动清理
            def cleanup_callback(ref):
                self._auto_cleanup(resource_id)
            
            self._weak_refs[resource_id] = weakref.ref(resource, cleanup_callback)
            
            logger.debug(f"Registered resource {resource_id} of type {resource_type.value}")
            return resource_id
    
    def unregister(self, resource_id: str) -> bool:
        """注销资源"""
        with self._lock:
            resource_info = self.resources.pop(resource_id, None)
            self._weak_refs.pop(resource_id, None)
            
            if resource_info:
                self._cleanup_resource(resource_info)
                logger.debug(f"Unregistered resource {resource_id}")
                return True
            
            return False
    
    def get_resource(self, resource_id: str) -> Optional[Any]:
        """获取资源"""
        with self._lock:
            resource_info = self.resources.get(resource_id)
            if resource_info:
                resource_info.last_accessed = time.time()
                return resource_info.resource
            return None
    
    def get_resources_by_type(self, resource_type: ResourceType) -> List[ResourceInfo]:
        """按类型获取资源"""
        with self._lock:
            return [
                info for info in self.resources.values()
                if info.resource_type == resource_type
            ]
    
    def cleanup_by_type(self, resource_type: ResourceType) -> int:
        """按类型清理资源"""
        resources_to_cleanup = []
        
        with self._lock:
            for resource_id, info in list(self.resources.items()):
                if info.resource_type == resource_type:
                    resources_to_cleanup.append(resource_id)
        
        cleaned_count = 0
        for resource_id in resources_to_cleanup:
            if self.unregister(resource_id):
                cleaned_count += 1
        
        return cleaned_count
    
    def cleanup_old_resources(self, max_age_seconds: float = 3600) -> int:
        """清理过期资源"""
        current_time = time.time()
        resources_to_cleanup = []
        
        with self._lock:
            for resource_id, info in list(self.resources.items()):
                if current_time - info.created_at > max_age_seconds:
                    resources_to_cleanup.append(resource_id)
        
        cleaned_count = 0
        for resource_id in resources_to_cleanup:
            if self.unregister(resource_id):
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old resources")
        
        return cleaned_count
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取资源内存使用统计"""
        with self._lock:
            total_memory = 0.0
            type_memory = {}
            
            for info in self.resources.values():
                total_memory += info.memory_usage_mb
                type_memory[info.resource_type.value] = (
                    type_memory.get(info.resource_type.value, 0.0) + info.memory_usage_mb
                )
            
            return {
                'total_mb': total_memory,
                'by_type': type_memory,
                'resource_count': len(self.resources)
            }
    
    def clear_all(self):
        """清理所有资源"""
        with self._lock:
            resource_ids = list(self.resources.keys())
            
        cleaned_count = 0
        for resource_id in resource_ids:
            if self.unregister(resource_id):
                cleaned_count += 1
        
        logger.info(f"Cleared all resources ({cleaned_count} items)")
    
    def _auto_cleanup(self, resource_id: str):
        """自动清理回调"""
        self.unregister(resource_id)
    
    def _cleanup_resource(self, resource_info: ResourceInfo):
        """清理单个资源"""
        try:
            if resource_info.cleanup_func:
                resource_info.cleanup_func(resource_info.resource)
            else:
                # 默认清理逻辑
                self._default_cleanup(resource_info.resource, resource_info.resource_type)
        except Exception as e:
            logger.error(f"Resource cleanup failed for {resource_info.resource_id}: {e}")
    
    def _default_cleanup(self, resource: Any, resource_type: ResourceType):
        """默认资源清理"""
        if resource_type == ResourceType.MODEL:
            if hasattr(resource, 'cpu'):
                resource.cpu()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        
        # 通用清理
        if hasattr(resource, 'close'):
            resource.close()
        elif hasattr(resource, 'cleanup'):
            resource.cleanup()
        
        # 强制垃圾回收
        del resource
        gc.collect()


class ResourceManager:
    """统一资源管理器"""
    
    def __init__(self):
        self.registry = ResourceRegistry()
        self.memory_monitor = MemoryMonitor()
        self._cleanup_thread = None
        self._running = False
        
        # 设置内存警告回调
        self.memory_monitor.add_callback('warning', self._on_memory_warning)
        self.memory_monitor.add_callback('critical', self._on_memory_critical)
    
    def start(self):
        """启动资源管理器"""
        self.memory_monitor.start_monitoring()
        self._start_cleanup_thread()
        logger.info("Resource manager started")
    
    def stop(self):
        """停止资源管理器"""
        self._running = False
        self.memory_monitor.stop_monitoring()
        
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=1)
        
        self.registry.clear_all()
        logger.info("Resource manager stopped")
    
    def register_resource(
        self,
        resource: Any,
        resource_type: ResourceType,
        resource_id: Optional[str] = None,
        cleanup_func: Optional[Callable] = None,
        memory_usage_mb: float = 0.0
    ) -> str:
        """注册资源"""
        if resource_id is None:
            resource_id = f"{resource_type.value}_{id(resource)}"
        
        return self.registry.register(
            resource_id=resource_id,
            resource=resource,
            resource_type=resource_type,
            cleanup_func=cleanup_func,
            memory_usage_mb=memory_usage_mb
        )
    
    def get_resource(self, resource_id: str) -> Optional[Any]:
        """获取资源"""
        return self.registry.get_resource(resource_id)
    
    def unregister_resource(self, resource_id: str) -> bool:
        """注销资源"""
        return self.registry.unregister(resource_id)
    
    def force_cleanup(self, memory_target_mb: Optional[float] = None):
        """强制清理资源"""
        if memory_target_mb is None:
            # 清理所有临时资源
            self.registry.cleanup_by_type(ResourceType.TEMP_FILE)
            self.registry.cleanup_old_resources(max_age_seconds=1800)  # 30分钟
        else:
            # 基于内存目标清理
            current_memory = self.memory_monitor.get_memory_usage()['rss_mb']
            if current_memory > memory_target_mb:
                # 清理策略：先清理临时文件，再清理旧缓存
                self.registry.cleanup_by_type(ResourceType.TEMP_FILE)
                self.registry.cleanup_old_resources(max_age_seconds=300)  # 5分钟
        
        # 强制垃圾回收
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    def get_status(self) -> Dict[str, Any]:
        """获取资源管理器状态"""
        memory_usage = self.memory_monitor.get_memory_usage()
        resource_usage = self.registry.get_memory_usage()
        
        return {
            'system_memory': memory_usage,
            'resource_memory': resource_usage,
            'resource_count': resource_usage['resource_count']
        }
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        self._running = True
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            daemon=True
        )
        self._cleanup_thread.start()
    
    def _cleanup_loop(self):
        """清理循环"""
        while self._running:
            try:
                # 每10分钟清理一次过期资源
                self.registry.cleanup_old_resources(max_age_seconds=3600)
                time.sleep(600)
            except Exception as e:
                logger.error(f"Resource cleanup loop error: {e}")
                time.sleep(60)
    
    def _on_memory_warning(self, memory_mb: float):
        """内存警告回调"""
        logger.warning(f"Memory usage warning: {memory_mb:.1f}MB")
        self.force_cleanup(memory_target_mb=memory_mb * 0.8)
    
    def _on_memory_critical(self, memory_mb: float):
        """内存临界回调"""
        logger.error(f"Critical memory usage: {memory_mb:.1f}MB")
        self.force_cleanup(memory_target_mb=memory_mb * 0.6)


# 全局资源管理器实例
_resource_manager = None
_resource_manager_lock = threading.Lock()


def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器"""
    global _resource_manager
    
    if _resource_manager is None:
        with _resource_manager_lock:
            if _resource_manager is None:
                _resource_manager = ResourceManager()
                _resource_manager.start()
    
    return _resource_manager


@contextmanager
def managed_resource(
    resource: Any,
    resource_type: ResourceType,
    cleanup_func: Optional[Callable] = None,
    memory_usage_mb: float = 0.0
):
    """资源管理上下文管理器"""
    manager = get_resource_manager()
    resource_id = None
    
    try:
        resource_id = manager.register_resource(
            resource=resource,
            resource_type=resource_type,
            cleanup_func=cleanup_func,
            memory_usage_mb=memory_usage_mb
        )
        yield resource
    finally:
        if resource_id:
            manager.unregister_resource(resource_id)


@contextmanager
def gpu_memory_context():
    """GPU内存管理上下文"""
    initial_memory = 0
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
    
    try:
        yield
    finally:
        if torch.cuda.is_available():
            current_memory = torch.cuda.memory_allocated()
            memory_used = (current_memory - initial_memory) / 1024 / 1024  # MB
            
            if memory_used > 0:
                logger.debug(f"GPU memory used: {memory_used:.1f}MB")
            
            torch.cuda.empty_cache()
            torch.cuda.synchronize()