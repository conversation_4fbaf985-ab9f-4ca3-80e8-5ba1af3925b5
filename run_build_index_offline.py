#!/usr/bin/env python3
"""
离线模式运行build_index.py
设置所有必要的环境变量来强制离线模式
"""

import os
import sys
import subprocess
from dotenv import load_dotenv

def set_offline_environment():
    """设置离线环境变量"""
    offline_env = {
        # Transformers离线模式
        'TRANSFORMERS_OFFLINE': '1',
        'HF_HUB_OFFLINE': '1',
        'HF_HUB_DISABLE_TELEMETRY': '1',
        
        # 禁用网络请求
        'CURL_CA_BUNDLE': '',
        'REQUESTS_CA_BUNDLE': '',
        
        # 强制使用本地文件
        'HF_DATASETS_OFFLINE': '1',
        'TOKENIZERS_PARALLELISM': 'false',
        
        # 禁用自动更新
        'HF_HUB_DISABLE_PROGRESS_BARS': '1',
        'HF_HUB_DISABLE_SYMLINKS_WARNING': '1',
        'HF_HUB_DISABLE_EXPERIMENTAL_WARNING': '1',
    }
    
    # 更新环境变量
    for key, value in offline_env.items():
        os.environ[key] = value
        print(f"Set {key}={value}")

def main():
    """主函数"""
    print("🚀 Setting up offline environment for build_index.py...")
    print("=" * 60)

    # 加载.env文件
    load_dotenv()

    # 显示项目配置
    print(f"📁 Project root: {os.getenv('PROJECT_ROOT_DIR', '.')}")
    print(f"📂 Target directories: {os.getenv('TARGET_DIRECTORIES', './,./tests,./docs')}")
    print(f"📄 File extensions: {os.getenv('FILE_EXTENSIONS', '.py,.md,.txt,.json,.toml,.yaml,.yml')}")
    print(f"🚫 Exclude patterns: {os.getenv('EXCLUDE_PATTERNS', '__pycache__,*.pyc,*.pyo,*.egg-info,.git,.venv,node_modules,build,dist')}")
    print("-" * 40)

    # 设置离线环境
    set_offline_environment()

    print("\n📋 Environment variables set for offline mode")
    print("-" * 40)
    
    # 运行build_index.py
    print("\n🔧 Running build_index.py in offline mode...")
    try:
        # 直接导入并运行，而不是subprocess
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # 导入并运行build_index
        import build_index
        build_index.main()
        
        print("\n✅ Index building completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Index building failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
