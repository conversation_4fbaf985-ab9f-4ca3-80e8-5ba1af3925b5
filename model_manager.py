"""
模型管理器
提供智能的模型加载、缓存和内存管理功能
"""

import os
import gc
import time
import threading
from typing import Dict, Optional, Any, Tuple
from contextlib import contextmanager
import torch
from transformers import AutoTokenizer
from sentence_transformers import CrossEncoder

from unixcoder import UniXcoder
from config import get_config
import logging

logger = logging.getLogger(__name__)

def _set_offline_mode():
    """设置离线模式环境变量"""
    try:
        config = get_config()
        if getattr(config.model, 'local_files_only', False):
            os.environ['TRANSFORMERS_OFFLINE'] = '1'
            os.environ['HF_HUB_OFFLINE'] = '1'
            os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'
            logger.info("Set offline mode environment variables")
    except Exception as e:
        logger.warning(f"Failed to set offline mode: {e}")

class ModelCache:
    """模型缓存管理"""
    
    def __init__(self, max_memory_mb: int = 4096):
        self.cache: Dict[str, Any] = {}
        self.access_times: Dict[str, float] = {}
        self.memory_usage: Dict[str, int] = {}
        self.max_memory_mb = max_memory_mb
        self.current_memory_mb = 0
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存的模型"""
        with self._lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key]
            return None
    
    def put(self, key: str, model: Any, memory_mb: int):
        """添加模型到缓存"""
        with self._lock:
            # 检查是否需要清理内存
            self._ensure_memory_available(memory_mb)
            
            self.cache[key] = model
            self.access_times[key] = time.time()
            self.memory_usage[key] = memory_mb
            self.current_memory_mb += memory_mb
            
            logger.info(f"Cached model {key}, memory usage: {self.current_memory_mb}/{self.max_memory_mb} MB")
    
    def remove(self, key: str):
        """从缓存中移除模型"""
        with self._lock:
            if key in self.cache:
                model = self.cache.pop(key)
                self.access_times.pop(key, None)
                memory_freed = self.memory_usage.pop(key, 0)
                self.current_memory_mb -= memory_freed
                
                # 清理模型内存
                try:
                    if hasattr(model, 'cpu'):
                        model.cpu()
                    del model
                    gc.collect()
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()
                except Exception as e:
                    logger.warning(f"Error during model cleanup: {e}")
                
                logger.info(f"Removed model {key} from cache, freed {memory_freed} MB")
    
    def _ensure_memory_available(self, required_mb: int):
        """确保有足够的内存空间"""
        while self.current_memory_mb + required_mb > self.max_memory_mb and self.cache:
            # 找到最久未使用的模型
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            logger.info(f"Memory pressure, evicting model {oldest_key}")
            self.remove(oldest_key)
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            keys = list(self.cache.keys())
            for key in keys:
                self.remove(key)

class ModelManager:
    """模型管理器"""
    
    def __init__(self):
        # 首先设置离线模式环境变量
        _set_offline_mode()

        self.config = get_config()
        self.cache = ModelCache(max_memory_mb=self.config.system.cache_embeddings * 4096)
        self._device = None
        self._models_loaded = set()

        # 预热标志
        self._preloaded = False
        self._preload_lock = threading.Lock()
    
    @property
    def device(self) -> torch.device:
        """获取计算设备"""
        if self._device is None:
            device_config = self.config.model.device
            if device_config == "auto":
                self._device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            else:
                self._device = torch.device(device_config)
        return self._device
    
    def get_unixcoder(self) -> UniXcoder:
        """获取UniXcoder模型"""
        model_name = self.config.model.name
        cache_key = f"unixcoder_{model_name}"

        model = self.cache.get(cache_key)
        if model is not None:
            return model

        logger.info(f"Loading UniXcoder model: {model_name}")
        start_time = time.time()

        # 获取模型配置参数
        cache_dir = getattr(self.config.model, 'cache_dir', None)
        local_files_only = getattr(self.config.model, 'local_files_only', False)

        # 如果是本地模式，设置环境变量强制离线模式
        if local_files_only:
            import os
            old_offline = os.environ.get('TRANSFORMERS_OFFLINE', None)
            old_local_files = os.environ.get('HF_HUB_OFFLINE', None)
            old_disable_telemetry = os.environ.get('HF_HUB_DISABLE_TELEMETRY', None)
            try:
                # 设置离线环境变量
                os.environ['TRANSFORMERS_OFFLINE'] = '1'
                os.environ['HF_HUB_OFFLINE'] = '1'
                os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'

                model = UniXcoder(model_name, cache_dir=cache_dir, local_files_only=local_files_only)
                model.to(self.device)
            finally:
                # 恢复环境变量
                if old_offline is not None:
                    os.environ['TRANSFORMERS_OFFLINE'] = old_offline
                else:
                    os.environ.pop('TRANSFORMERS_OFFLINE', None)
                if old_local_files is not None:
                    os.environ['HF_HUB_OFFLINE'] = old_local_files
                else:
                    os.environ.pop('HF_HUB_OFFLINE', None)
                if old_disable_telemetry is not None:
                    os.environ['HF_HUB_DISABLE_TELEMETRY'] = old_disable_telemetry
                else:
                    os.environ.pop('HF_HUB_DISABLE_TELEMETRY', None)
        else:
            model = UniXcoder(model_name, cache_dir=cache_dir, local_files_only=local_files_only)
            model.to(self.device)
        
        # 估算模型内存使用量
        memory_mb = self._estimate_model_memory(model)
        self.cache.put(cache_key, model, memory_mb)
        
        load_time = time.time() - start_time
        logger.info(f"UniXcoder model loaded in {load_time:.2f}s, estimated memory: {memory_mb} MB")
        
        return model
    
    def get_reranker(self) -> CrossEncoder:
        """获取重排序模型"""
        model_name = self.config.retrieval.reranker_model
        cache_key = f"reranker_{model_name}"
        
        model = self.cache.get(cache_key)
        if model is not None:
            return model
        
        logger.info(f"Loading reranker model: {model_name}")
        start_time = time.time()

        # 获取模型配置参数
        cache_dir = getattr(self.config.model, 'cache_dir', None)
        local_files_only = getattr(self.config.model, 'local_files_only', False)

        # 构建CrossEncoder参数
        cross_encoder_kwargs = {
            'max_length': self.config.retrieval.reranker_max_length,
            'device': self.device
        }

        # 如果使用本地文件，设置cache_folder和local_files_only
        if cache_dir:
            cross_encoder_kwargs['cache_folder'] = cache_dir
        if local_files_only:
            cross_encoder_kwargs['local_files_only'] = local_files_only

        # 如果是本地模式，设置环境变量强制离线模式
        if local_files_only:
            import os
            old_offline = os.environ.get('TRANSFORMERS_OFFLINE', None)
            old_local_files = os.environ.get('HF_HUB_OFFLINE', None)
            old_disable_telemetry = os.environ.get('HF_HUB_DISABLE_TELEMETRY', None)
            try:
                # 设置离线环境变量
                os.environ['TRANSFORMERS_OFFLINE'] = '1'
                os.environ['HF_HUB_OFFLINE'] = '1'
                os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'

                model = CrossEncoder(model_name, **cross_encoder_kwargs)
            finally:
                # 恢复环境变量
                if old_offline is not None:
                    os.environ['TRANSFORMERS_OFFLINE'] = old_offline
                else:
                    os.environ.pop('TRANSFORMERS_OFFLINE', None)
                if old_local_files is not None:
                    os.environ['HF_HUB_OFFLINE'] = old_local_files
                else:
                    os.environ.pop('HF_HUB_OFFLINE', None)
                if old_disable_telemetry is not None:
                    os.environ['HF_HUB_DISABLE_TELEMETRY'] = old_disable_telemetry
                else:
                    os.environ.pop('HF_HUB_DISABLE_TELEMETRY', None)
        else:
            model = CrossEncoder(model_name, **cross_encoder_kwargs)
        
        memory_mb = self._estimate_model_memory(model)
        self.cache.put(cache_key, model, memory_mb)
        
        load_time = time.time() - start_time
        logger.info(f"Reranker model loaded in {load_time:.2f}s, estimated memory: {memory_mb} MB")
        
        return model
    
    def get_tokenizer(self, model_name: Optional[str] = None) -> AutoTokenizer:
        """获取分词器"""
        if model_name is None:
            model_name = "deepseek-ai/deepseek-llm-7b-chat"
        
        cache_key = f"tokenizer_{model_name}"
        tokenizer = self.cache.get(cache_key)
        
        if tokenizer is not None:
            return tokenizer
        
        logger.info(f"Loading tokenizer: {model_name}")
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.cache.put(cache_key, tokenizer, 10)  # 分词器占用内存较少
            return tokenizer
        except Exception as e:
            logger.warning(f"Failed to load tokenizer {model_name}: {e}")
            return None
    
    def preload_models(self):
        """预加载模型"""
        with self._preload_lock:
            if self._preloaded:
                return
            
            logger.info("Starting model preloading...")
            start_time = time.time()
            
            # 预加载UniXcoder
            self.get_unixcoder()
            
            # 预加载重排序模型（如果启用）
            if self.config.retrieval.use_reranking:
                self.get_reranker()
            
            # 预加载分词器
            self.get_tokenizer()
            
            self._preloaded = True
            total_time = time.time() - start_time
            logger.info(f"Model preloading completed in {total_time:.2f}s")
    
    @contextmanager
    def model_context(self, *model_names):
        """模型上下文管理器，确保模型在使用期间不被回收"""
        models = []
        try:
            for name in model_names:
                if name == "unixcoder":
                    models.append(self.get_unixcoder())
                elif name == "reranker":
                    models.append(self.get_reranker())
                elif name == "tokenizer":
                    models.append(self.get_tokenizer())
            yield models
        finally:
            # 在这里可以添加模型使用后的清理逻辑
            pass
    
    def unload_model(self, model_type: str):
        """卸载指定类型的模型"""
        if model_type == "unixcoder":
            cache_key = f"unixcoder_{self.config.model.name}"
        elif model_type == "reranker":
            cache_key = f"reranker_{self.config.retrieval.reranker_model}"
        elif model_type == "tokenizer":
            cache_key = f"tokenizer_deepseek-ai/deepseek-llm-7b-chat"
        else:
            logger.warning(f"Unknown model type: {model_type}")
            return
        
        self.cache.remove(cache_key)
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        return {
            "current_memory_mb": self.cache.current_memory_mb,
            "max_memory_mb": self.cache.max_memory_mb,
            "cached_models": list(self.cache.cache.keys()),
            "memory_per_model": self.cache.memory_usage.copy()
        }
    
    def _estimate_model_memory(self, model) -> int:
        """估算模型内存使用量（MB）"""
        try:
            if hasattr(model, 'parameters'):
                param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
                # 假设每个参数4字节（float32）
                memory_bytes = param_count * 4
                # 考虑模型状态、梯度等额外开销，乘以2
                memory_mb = int(memory_bytes * 2 / (1024 * 1024))
                return max(memory_mb, 100)  # 最少100MB
            else:
                return 100  # 默认值
        except Exception as e:
            logger.warning(f"Failed to estimate model memory: {e}")
            return 100
    
    def cleanup(self):
        """清理所有模型"""
        logger.info("Cleaning up model manager...")
        self.cache.clear()
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

# 全局模型管理器实例
model_manager = ModelManager()

def get_model_manager() -> ModelManager:
    """获取全局模型管理器"""
    return model_manager