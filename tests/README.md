# 测试文件说明

本目录包含UniXcoder RAG系统的所有测试和评估文件。

## 文件说明

### 🧪 测试文件

- **`test_evaluation.py`** - 检索与问答评测脚本
  - 包含预定义的测试用例
  - 测试检索准确率和问答效果
  - 运行方式: `python tests/test_evaluation.py`

- **`test_integration.py`** - 集成测试脚本
  - 验证系统核心功能
  - 测试各模块间的集成
  - 运行方式: `python tests/test_integration.py`

### 📊 性能测试

- **`benchmark.py`** - 性能基准测试
  - 全面的性能评估和监控
  - 生成详细的性能报告
  - 运行方式: `python tests/benchmark.py`

- **`optimization_demo.py`** - 优化效果演示
  - 展示系统优化功能
  - 演示性能改进效果
  - 运行方式: `python tests/optimization_demo.py`

## 运行所有测试

```bash
# 从项目根目录运行
cd /path/to/Unixcoder

# 运行评估测试
python tests/test_evaluation.py

# 运行集成测试
python tests/test_integration.py

# 运行性能基准测试
python tests/benchmark.py

# 运行优化演示
python tests/optimization_demo.py
```

## 测试结果

测试结果和报告将保存在以下位置：
- 基准测试报告: `benchmark_report_*.json`
- 测试日志: 控制台输出
- 性能统计: 内存中或临时文件

## 注意事项

1. 运行测试前请确保已构建索引 (`python build_index.py`)
2. 确保环境变量已正确配置 (DEEPSEEK_API_KEY)
3. 某些测试可能需要GPU支持以获得最佳性能
