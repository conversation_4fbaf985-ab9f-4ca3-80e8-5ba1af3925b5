#!/usr/bin/env python3
"""
集成测试脚本
验证升级后的系统核心功能
"""

import sys
import traceback
from pathlib import Path

def test_imports():
    """测试核心组件导入"""
    print("🔍 Testing core component imports...")
    
    try:
        from config import get_config
        print("✅ config module imported")
        
        from logger import get_logger
        print("✅ logger module imported")
        
        from hybrid_retriever import get_hybrid_retriever
        print("✅ hybrid_retriever module imported")
        
        from advanced_prompt_builder import get_prompt_builder
        print("✅ advanced_prompt_builder module imported")
        
        from dependency_analyzer import get_dependency_analyzer
        print("✅ dependency_analyzer module imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置系统"""
    print("\n🔍 Testing configuration system...")
    
    try:
        from config import get_config
        config = get_config()
        
        # 检查关键配置项
        assert hasattr(config, 'model'), "Missing model config"
        assert hasattr(config, 'retrieval'), "Missing retrieval config"
        assert hasattr(config, 'system'), "Missing system config"
        
        print("✅ Configuration system working")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_query_expansion():
    """测试查询扩展"""
    print("\n🔍 Testing query expansion...")
    
    try:
        from hybrid_retriever import QueryExpander
        
        expander = QueryExpander()
        result = expander.expand_query("how to implement function")
        
        assert 'original' in result, "Missing original query"
        assert 'keywords' in result, "Missing keywords"
        assert 'synonyms' in result, "Missing synonyms"
        
        print(f"✅ Query expansion working: {len(result['keywords'])} keywords, {len(result['synonyms'])} synonyms")
        return True
        
    except Exception as e:
        print(f"❌ Query expansion test failed: {e}")
        return False

def test_code_pattern_detection():
    """测试代码模式检测"""
    print("\n🔍 Testing code pattern detection...")
    
    try:
        from advanced_prompt_builder import CodePatternDetector
        
        detector = CodePatternDetector()
        
        # 测试单例模式检测
        singleton_code = """
        class Singleton:
            _instance = None
            def __new__(cls):
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                return cls._instance
        """
        
        patterns = detector.detect_patterns(singleton_code)
        
        print(f"✅ Pattern detection working: found {len(patterns)} patterns")
        return True
        
    except Exception as e:
        print(f"❌ Pattern detection test failed: {e}")
        return False

def test_complexity_analysis():
    """测试复杂度分析"""
    print("\n🔍 Testing complexity analysis...")
    
    try:
        from advanced_prompt_builder import CodeComplexityAnalyzer
        
        analyzer = CodeComplexityAnalyzer()
        
        # 测试复杂代码
        complex_code = """
        def complex_function(x, y):
            if x > 0:
                for i in range(10):
                    if i % 2 == 0:
                        try:
                            result = x / i
                        except ZeroDivisionError:
                            result = 0
                    else:
                        result = x * i
            else:
                result = y
            return result
        """
        
        analysis = analyzer.analyze_complexity(complex_code)
        
        assert 'cyclomatic_complexity' in analysis, "Missing cyclomatic complexity"
        assert 'complexity_level' in analysis, "Missing complexity level"
        
        print(f"✅ Complexity analysis working: {analysis['complexity_level']} complexity")
        return True
        
    except Exception as e:
        print(f"❌ Complexity analysis test failed: {e}")
        return False

def test_prompt_generation():
    """测试高级Prompt生成"""
    print("\n🔍 Testing advanced prompt generation...")
    
    try:
        from advanced_prompt_builder import get_prompt_builder
        
        builder = get_prompt_builder()
        
        # 模拟代码块
        mock_chunks = [{
            'code': 'def test_function():\n    return "Hello"',
            'metadata': {
                'name': 'test_function',
                'file_path': 'test.py',
                'start_line': 1
            }
        }]
        
        prompt = builder.build_prompt("explain this function", mock_chunks, "explanation")
        
        assert len(prompt) > 100, "Prompt too short"
        assert "explain" in prompt.lower(), "Missing explanation context"
        
        print(f"✅ Advanced prompt generation working: {len(prompt)} characters")
        return True
        
    except Exception as e:
        print(f"❌ Prompt generation test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 Starting UniXcoder RAG System Integration Tests")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_configuration,
        test_query_expansion,
        test_code_pattern_detection,
        test_complexity_analysis,
        test_prompt_generation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system upgrade is successful.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)