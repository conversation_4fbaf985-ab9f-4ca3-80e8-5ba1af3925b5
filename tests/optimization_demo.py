"""
优化效果演示

展示系统优化后的功能和性能改进。
"""

import time
import asyncio
from typing import List, Dict, Any

# 导入优化后的模块
from config_manager import get_config_manager, get_config
from cache_manager import get_cache_manager, cached
from performance_monitor import performance_monitor, monitor_performance, generate_performance_report
from resource_manager import get_resource_manager, managed_resource, ResourceType, resource_manager, gpu_memory_context
from exceptions import BaseServiceError, ModelInferenceError, error_handler, handle_errors
from dynamic_batch_processor import create_batch_processor
from search_strategies import SearchOrchestrator, SearchMethod

from logger import get_logger

logger = get_logger(__name__)


class DemoResource:
    """一个用于演示的简单资源类"""
    def __init__(self, name):
        self.name = name
        logger.info(f"Resource '{self.name}' created.")

    def __str__(self):
        return f"DemoResource(name={self.name})"

    def close(self):
        """模拟资源关闭"""
        logger.info(f"Resource '{self.name}' closed.")


class OptimizationDemo:
    """优化效果演示类"""
    
    def __init__(self):
        self.config = get_config()
        self.cache_manager = get_cache_manager()
        self.resource_manager = get_resource_manager()
        
        logger.info("Optimization demo initialized")
    
    @performance_monitor("demo_cache_optimization")
    def demo_cache_optimization(self):
        """演示缓存优化"""
        logger.info("=== 缓存优化演示 ===")
        
        # 创建演示缓存
        cache = self.cache_manager.create_cache(
            name="demo_cache",
            max_size=100,
            max_memory_mb=64,
            ttl=300
        )
        
        # 测试缓存性能
        test_data = [f"test_data_{i}" for i in range(50)]
        
        # 第一次写入（无缓存）
        start_time = time.time()
        for i, data in enumerate(test_data):
            result = self._simulate_expensive_operation(data)
            cache.put(f"key_{i}", result)
        write_time = time.time() - start_time
        
        # 第二次读取（有缓存）
        start_time = time.time()
        cached_results = []
        for i in range(len(test_data)):
            result = cache.get(f"key_{i}")
            cached_results.append(result)
        read_time = time.time() - start_time
        
        # 显示缓存统计
        cache_stats = cache.get_stats()
        logger.info(f"Cache write time: {write_time:.3f}s")
        logger.info(f"Cache read time: {read_time:.3f}s")
        logger.info(f"Cache stats: {cache_stats.size} items, hit rate: {cache_stats.hit_rate:.2f}")
        logger.info(f"Speed improvement: {write_time/read_time:.1f}x faster")
    
    @cached(cache_name="expensive_ops", ttl=300)
    def _simulate_expensive_operation(self, data: str) -> str:
        """模拟耗时操作（带缓存装饰器）"""
        time.sleep(0.01)  # 模拟计算时间
        return f"processed_{data}"
    
    @performance_monitor("demo_error_handling")
    def demo_error_handling(self):
        """演示错误处理优化"""
        logger.info("=== 错误处理优化演示 ===")
        
        # 正常操作
        try:
            with monitor_performance("safe_operation"):
                result = self._safe_operation("valid_input")
                logger.info(f"Safe operation result: {result}")
        except BaseServiceError as e:
            logger.error(f"Service error: {e}")
        
        # 异常操作
        try:
            with monitor_performance("unsafe_operation"):
                result = self._unsafe_operation("invalid_input")
        except BaseServiceError as e:
            logger.info(f"Caught service error: {e.error_code.value} - {e.message}")
            if e.cause:
                logger.info(f"Caused by: {e.cause}")
    
    @handle_errors(ModelInferenceError, logger=logger, reraise_as=BaseServiceError)
    def _safe_operation(self, input_data: str) -> str:
        """安全的操作（带错误处理）"""
        if input_data == "valid_input":
            return "success"
        raise ValueError("Invalid input")
    
    def _unsafe_operation(self, input_data: str) -> str:
        """不安全的操作"""
        if input_data == "invalid_input":
            raise ModelInferenceError(
                "Model inference failed",
                model_name="demo_model",
                cause=ValueError("Invalid input data")
            )
        return "success"
    
    @performance_monitor("demo_resource_management")
    def demo_resource_management(self):
        """演示资源管理优化"""
        logger.info("=== 资源管理优化演示 ===")
        
        # 创建一个模拟资源对象
        demo_res = DemoResource("demo_resource")

        # 使用资源管理器
        with managed_resource(
            resource=demo_res,
            resource_type=ResourceType.TEMP_FILE, # 保持类型用于演示
            memory_usage_mb=10.0
        ) as resource:
            logger.info(f"Using managed resource: {resource}")
            time.sleep(0.1)
        
        # 显示资源状态
        status = self.resource_manager.get_status()
        logger.info(f"Resource manager status: {status['resource_count']} resources")
        logger.info(f"System memory: {status['system_memory']['rss_mb']:.1f}MB")
    
    @performance_monitor("demo_dynamic_batching")
    def demo_dynamic_batching(self):
        """演示动态批处理优化"""
        logger.info("=== 动态批处理优化演示 ===")
        
        # 创建批处理器
        def batch_processor(items: List[str]) -> List[str]:
            # 模拟批处理逻辑
            time.sleep(len(items) * 0.001)  # 批处理时间与大小相关
            return [f"processed_{item}" for item in items]
        
        processor = create_batch_processor(
            processor_func=batch_processor,
            min_batch_size=1,
            max_batch_size=32,
            target_latency_ms=100.0
        )
        
        # 测试不同大小的数据
        test_sizes = [10, 50, 100, 200]
        
        for size in test_sizes:
            test_data = [f"item_{i}" for i in range(size)]
            
            start_time = time.time()
            results = processor.process_batch(test_data)
            process_time = time.time() - start_time
            
            stats = processor.get_stats()
            logger.info(f"Processed {size} items in {process_time:.3f}s")
            logger.info(f"Current batch size: {stats['current_batch_size']}")
            logger.info(f"Throughput: {stats['average_throughput']:.1f} items/s")
    
    @performance_monitor("demo_config_management")
    def demo_config_management(self):
        """演示配置管理优化"""
        logger.info("=== 配置管理优化演示 ===")
        
        config_manager = get_config_manager()
        config = config_manager.get_config()
        
        logger.info(f"Current model: {config.model.name}")
        logger.info(f"Batch size: {config.system.batch_size}")
        logger.info(f"Cache enabled: {config.cache.enable_cache}")
        
        # 动态更新配置
        config_manager.update_config(
            system__batch_size=64,
            cache__embedding_max_size=5000
        )
        
        updated_config = config_manager.get_config()
        logger.info(f"Updated batch size: {updated_config.system.batch_size}")
    
    def demo_search_strategies(self):
        """演示搜索策略优化"""
        logger.info("=== 搜索策略优化演示 ===")
        
        # 这里只是演示架构，实际使用需要具体的检索器实例
        logger.info("Search orchestrator supports pluggable strategies:")
        logger.info(f"- {SearchMethod.SEMANTIC.value}: Vector similarity search")
        logger.info(f"- {SearchMethod.BM25.value}: Term frequency search") 
        logger.info(f"- {SearchMethod.STRUCTURED.value}: Code structure search")
        logger.info("- Weighted fusion and reranking strategies")
    
    def run_full_demo(self):
        """运行完整演示"""
        logger.info("开始系统优化效果演示")
        
        try:
            self.demo_cache_optimization()
            self.demo_error_handling()
            self.demo_resource_management()
            self.demo_dynamic_batching()
            self.demo_config_management()
            self.demo_search_strategies()
            
            # 生成性能报告
            report = generate_performance_report()
            logger.info("=== 性能报告 ===")
            logger.info(f"总操作数: {report['summary']['total_calls']}")
            logger.info(f"总体成功率: {report['summary']['overall_success_rate']:.2%}")
            
            # 显示各操作统计
            for op_name, stats in report['operation_stats'].items():
                if 'demo_' in op_name:
                    logger.info(f"{op_name}: {stats['average_duration']:.3f}s avg, {stats['success_rate']:.2%} success")
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
        
        logger.info("系统优化效果演示完成")


def main():
    """主函数"""
    try:
        demo = OptimizationDemo()
        demo.run_full_demo()
    except Exception as e:
        logger.error(f"Demo initialization failed: {e}")


if __name__ == "__main__":
    main()