import os
import sys
from ask import CodeAssistant

# --- 测试套件定义 ---
# 每个测试用例包含:
# - query: 向系统提出的自然语言问题。
# - expected_file: 我们期望在结果中看到的源文件名。
# - expected_entity: 我们期望在结果中找到的关键实体（类名或函数名）。
# - expected_type: 我们期望找到的实体类型（类或函数）。
# - description: 对测试目标的简短描述。
TEST_CASES = [
    {
        "query": "how to chunk source code into smaller pieces",
        "expected_file": "code_chunker.py",
        "expected_entity": "CodeChunker",
        "expected_type": "class",
        "description": "测试检索核心代码分块逻辑的能力。"
    },
    {
        "query": "how do you build the faiss index from code chunks?",
        "expected_file": "build_index.py",
        "expected_entity": "main",
        "expected_type": "function",
        "description": "测试检索FAISS索引构建逻辑的能力。"
    },
    {
        "query": "how does the interactive query cli work?",
        "expected_file": "ask.py",
        "expected_entity": "CodeAssistant",
        "expected_type": "class",
        "description": "测试检索交互式查询主类的能力。"
    },
    {
        "query": "what is the unixcoder model wrapper",
        "expected_file": "unixcoder.py",
        "expected_entity": "UniXcoder",
        "expected_type": "class",
        "description": "测试检索UniXcoder模型封装类的能力。"
    },
    {
        "query": "how are search results filtered dynamically",
        "expected_file": "ask.py",
        "expected_entity": "filter_results_dynamically",
        "expected_type": "function",
        "description": "测试检索动态过滤逻辑的能力。"
    },
    # --- 新增的鲁棒性测试用例 ---
    {
        "query": "how does the beam search algorithm work?",
        "expected_file": "unixcoder.py",
        "expected_entity": "Beam",
        "expected_type": "class",
        "description": "测试对特定算法概念的检索能力。"
    },
    {
        "query": "how to get the final hypothesis from the beam search",
        "expected_file": "unixcoder.py",
        "expected_entity": "getFinal",
        "expected_type": "function",
        "description": "测试检索嵌套类中具体方法的能力。"
    },
    {
        "query": "how to get tokens for a piece of code",
        "expected_file": "unixcoder.py",
        "expected_entity": "tokenize",
        "expected_type": "function",
        "description": "测试对模糊或通用术语查询的处理能力。"
    },
    {
        "query": "how to extract import statements from a file",
        "expected_file": "code_chunker.py",
        "expected_entity": "_extract_imports",
        "expected_type": "function",
        "description": "测试对特定实现细节的检索能力。"
    }
]

def entity_match(meta, expected_entity, expected_type):
    """
    更智能地检查一个实体是否匹配预期。
    - 如果期望的是类，则匹配类本身或其任何方法。
    - 如果期望的是函数，则精确匹配函数名。
    """
    expected_entity_lower = expected_entity.lower()

    # 规则1: 如果期望的是类
    if expected_type == 'class':
        # 检查分块是否就是该类的定义
        if meta.get('type') == 'class_definition' and meta.get('name', '').lower() == expected_entity_lower:
            return True
        # 检查分块是否是该类的一个方法
        if meta.get('parent_class', '').lower() == expected_entity_lower:
            return True

    # 规则2: 如果期望的是函数
    if expected_type == 'function':
        if meta.get('name', '').lower() == expected_entity_lower:
            return True
            
    # 规则3: 作为后备，进行通用的宽松匹配 (检查签名和文档字符串)
    if expected_entity_lower in meta.get('signature', '').lower():
        return True
    if 'docstring' in meta and expected_entity_lower in meta['docstring'].lower():
        return True
        
    return False

def evaluate_results(results, case):
    """
    评估搜索结果，并返回第一个正确匹配项的排名。
    如果找到匹配项，返回其排名（1-based）；否则返回0。
    """
    if not results:
        return 0

    expected_file = case['expected_file']
    expected_entity = case['expected_entity']
    expected_type = case['expected_type']

    for i, res in enumerate(results):
        rank = i + 1
        meta = res['chunk']['metadata']
        
        file_path = meta.get('file_path', '')
        file_match = os.path.basename(file_path) == expected_file
        
        # 使用新的、更智能的匹配逻辑
        if file_match and entity_match(meta, expected_entity, expected_type):
            return rank  # 找到正确结果
    
    return 0  # 未找到

def main():
    # 抑制在评估期间来自 ask.py 的详细 DEBUG 输出
    original_stdout = sys.stdout
    sys.stdout = open(os.devnull, 'w')
    
    try:
        print("Initializing Code Assistant for evaluation...")
        assistant = CodeAssistant()
        print("Initialization complete.")
    finally:
        # 恢复 stdout
        sys.stdout.close()
        sys.stdout = original_stdout

    passed_count = 0
    total_reciprocal_rank = 0.0

    print("\n--- Running Evaluation Suite ---")
    for i, case in enumerate(TEST_CASES):
        print(f"\n[Test Case {i+1}/{len(TEST_CASES)}] {case['description']}")
        print(f"  Query: '{case['query']}'")
        
        # 执行搜索
        results = assistant.dynamic_search(case['query'])
        
        # 评估结果
        rank = evaluate_results(results, case)
        
        if rank > 0:
            passed_count += 1
            reciprocal_rank = 1.0 / rank
            total_reciprocal_rank += reciprocal_rank
            print(f"  -> SUCCESS: Found '{case['expected_entity']}' in '{case['expected_file']}' at rank {rank}.")
        else:
            reciprocal_rank = 0.0
            print(f"  -> FAILURE: Did not find expected entity in results.")
            # 打印Top3结果以供调试
            if results:
                print("  -> Top 3 results were:")
                for res in results[:3]:
                    meta = res['chunk']['metadata']
                    print(f"     - Rank {results.index(res)+1}: {meta}")
            else:
                print("  -> No results were returned.")


    print("\n\n--- Evaluation Summary ---")
    if not TEST_CASES:
        print("No test cases were run.")
        return
        
    success_rate = (passed_count / len(TEST_CASES)) * 100
    mrr = total_reciprocal_rank / len(TEST_CASES)
    
    print(f"Success Rate: {passed_count}/{len(TEST_CASES)} ({success_rate:.2f}%)")
    print(f"Mean Reciprocal Rank (MRR): {mrr:.4f}")

if __name__ == "__main__":
    main() 