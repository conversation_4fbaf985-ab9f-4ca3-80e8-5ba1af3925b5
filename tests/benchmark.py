"""
性能基准测试和监控系统
提供全面的性能评估和监控功能
"""

import time
import psutil
import gc
import json
import numpy as np
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from pathlib import Path
import threading
from contextlib import contextmanager

from config import get_config
from logger import get_logger
from model_manager import get_model_manager
from index_manager import get_index_manager
from embedding_generator import get_batch_generator

logger = get_logger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation: str
    duration: float
    memory_before: float
    memory_after: float
    memory_peak: float
    cpu_percent: float
    timestamp: float
    success: bool
    error_message: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

@dataclass
class BenchmarkResult:
    """基准测试结果"""
    test_name: str
    metrics: List[PerformanceMetrics]
    summary_stats: Dict[str, float]
    timestamp: float
    config_snapshot: Dict[str, Any]

class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self, interval: float = 0.1):
        self.interval = interval
        self.monitoring = False
        self.metrics = []
        self.thread = None
        self._lock = threading.Lock()
    
    def start(self):
        """开始监控"""
        with self._lock:
            if self.monitoring:
                return
            
            self.monitoring = True
            self.metrics = []
            self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.thread.start()
    
    def stop(self) -> List[Dict[str, Any]]:
        """停止监控并返回数据"""
        with self._lock:
            if not self.monitoring:
                return []
            
            self.monitoring = False
            if self.thread:
                self.thread.join()
            
            return self.metrics.copy()
    
    def _monitor_loop(self):
        """监控循环"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                # 收集系统资源信息
                memory_info = process.memory_info()
                cpu_percent = process.cpu_percent()
                
                metric = {
                    'timestamp': time.time(),
                    'memory_rss': memory_info.rss / 1024 / 1024,  # MB
                    'memory_vms': memory_info.vms / 1024 / 1024,  # MB
                    'cpu_percent': cpu_percent,
                    'num_threads': process.num_threads()
                }
                
                # GPU内存（如果可用）
                try:
                    import torch
                    if torch.cuda.is_available():
                        metric['gpu_memory'] = torch.cuda.memory_allocated() / 1024 / 1024  # MB
                        metric['gpu_memory_cached'] = torch.cuda.memory_reserved() / 1024 / 1024  # MB
                except:
                    pass
                
                with self._lock:
                    self.metrics.append(metric)
                
                time.sleep(self.interval)
                
            except Exception as e:
                logger.warning(f"Error in resource monitoring: {e}")
                break

class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self):
        self.config = get_config()
        self.model_manager = get_model_manager()
        self.index_manager = get_index_manager()
        self.batch_generator = get_batch_generator()
        self.monitor = ResourceMonitor()
        
        # 测试数据
        self.test_queries = [
            "how does the code chunking work",
            "implement a search function",
            "class definition with methods",
            "error handling in python",
            "async function implementation",
            "data processing pipeline",
            "machine learning model training",
            "database connection and queries",
            "file I/O operations",
            "network request handling"
        ]
        
        self.test_code_samples = [
            "def hello_world():\n    print('Hello, World!')",
            "class Calculator:\n    def add(self, a, b):\n        return a + b",
            "import os\n\ndef read_file(path):\n    with open(path, 'r') as f:\n        return f.read()",
            "async def fetch_data(url):\n    async with aiohttp.ClientSession() as session:\n        async with session.get(url) as response:\n            return await response.text()",
            "try:\n    result = risky_operation()\nexcept Exception as e:\n    logger.error(f'Error: {e}')\n    result = None",
        ]
    
    @contextmanager
    def measure_performance(self, operation: str, **kwargs):
        """性能测量上下文管理器"""
        # 获取初始状态
        gc.collect()  # 清理垃圾回收
        
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 启动资源监控
        self.monitor.start()
        start_time = time.time()
        
        success = True
        error_message = None
        
        try:
            yield
        except Exception as e:
            success = False
            error_message = str(e)
            logger.error(f"Benchmark operation '{operation}' failed: {e}")
            raise
        finally:
            # 停止监控
            duration = time.time() - start_time
            monitor_data = self.monitor.stop()
            
            # 获取最终状态
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            cpu_percent = process.cpu_percent()
            
            # 计算峰值内存
            memory_peak = memory_after
            if monitor_data:
                memory_peak = max(m['memory_rss'] for m in monitor_data)
            
            # 创建性能指标
            metrics = PerformanceMetrics(
                operation=operation,
                duration=duration,
                memory_before=memory_before,
                memory_after=memory_after,
                memory_peak=memory_peak,
                cpu_percent=cpu_percent,
                timestamp=start_time,
                success=success,
                error_message=error_message,
                additional_data={
                    'monitor_data': monitor_data,
                    'kwargs': kwargs
                }
            )
            
            # 记录日志
            if success:
                logger.info(f"Benchmark '{operation}' completed in {duration:.3f}s, "
                           f"memory: {memory_before:.1f}MB -> {memory_after:.1f}MB "
                           f"(peak: {memory_peak:.1f}MB)")
            else:
                logger.error(f"Benchmark '{operation}' failed after {duration:.3f}s: {error_message}")
            
            # 将指标存储在实例中以供后续分析
            if not hasattr(self, '_current_metrics'):\n                self._current_metrics = []\n            self._current_metrics.append(metrics)
    
    def benchmark_model_loading(self) -> BenchmarkResult:
        """基准测试模型加载"""
        logger.info("Starting model loading benchmark...")
        
        metrics = []
        
        # 测试UniXcoder加载
        with self.measure_performance("unixcoder_loading"):
            model = self.model_manager.get_unixcoder()
        
        # 测试重排序模型加载
        if self.config.retrieval.use_reranking:
            with self.measure_performance("reranker_loading"):
                reranker = self.model_manager.get_reranker()
        
        # 测试分词器加载
        with self.measure_performance("tokenizer_loading"):
            tokenizer = self.model_manager.get_tokenizer()
        
        metrics = getattr(self, '_current_metrics', [])
        self._current_metrics = []
        
        # 计算汇总统计
        loading_times = [m.duration for m in metrics]
        summary_stats = {
            'total_time': sum(loading_times),
            'avg_time': np.mean(loading_times),
            'max_time': max(loading_times),
            'min_time': min(loading_times),
            'total_memory_change': sum(m.memory_after - m.memory_before for m in metrics)
        }
        
        return BenchmarkResult(
            test_name="model_loading",
            metrics=metrics,
            summary_stats=summary_stats,
            timestamp=time.time(),
            config_snapshot=asdict(self.config)
        )
    
    def benchmark_embedding_generation(self) -> BenchmarkResult:
        """基准测试嵌入生成"""
        logger.info("Starting embedding generation benchmark...")
        
        from embedding_generator import EmbeddingTask
        
        metrics = []
        
        # 测试不同批次大小的性能
        batch_sizes = [1, 4, 8, 16, 32]
        
        for batch_size in batch_sizes:
            if batch_size > len(self.test_code_samples):
                continue
            
            # 准备任务
            tasks = [
                EmbeddingTask(
                    id=f"test_{i}",
                    text=self.test_code_samples[i % len(self.test_code_samples)],
                    metadata={'original_text': self.test_code_samples[i % len(self.test_code_samples)]}
                )
                for i in range(batch_size)
            ]
            
            with self.measure_performance(f"embedding_batch_size_{batch_size}", batch_size=batch_size):
                results = self.batch_generator.generate_embeddings(tasks)
                assert len(results) == batch_size
        
        metrics = getattr(self, '_current_metrics', [])
        self._current_metrics = []
        
        # 计算性能统计
        summary_stats = self._calculate_embedding_stats(metrics)
        
        return BenchmarkResult(
            test_name="embedding_generation",
            metrics=metrics,
            summary_stats=summary_stats,
            timestamp=time.time(),
            config_snapshot=asdict(self.config)
        )
    
    def benchmark_index_operations(self) -> BenchmarkResult:
        """基准测试索引操作"""
        logger.info("Starting index operations benchmark...")
        
        metrics = []
        
        # 生成测试数据
        test_embeddings = np.random.random((1000, self.config.index.dimension)).astype(np.float32)
        test_metadata = [{'id': i, 'type': 'test'} for i in range(1000)]
        
        # 测试索引创建
        with self.measure_performance("index_creation", num_vectors=len(test_embeddings)):
            index = self.index_manager.create_index(test_embeddings)
        
        # 测试搜索性能
        query_embeddings = test_embeddings[:10]  # 使用前10个作为查询
        
        for k in [1, 5, 10, 25]:
            with self.measure_performance(f"search_k_{k}", k=k, num_queries=len(query_embeddings)):
                similarities, indices = self.index_manager.search(query_embeddings, k)
        
        # 测试索引保存
        with self.measure_performance("index_save"):
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                index_file = str(Path(temp_dir) / "test_index.bin")
                metadata_file = str(Path(temp_dir) / "test_metadata.pkl")
                self.index_manager.save_index(index_file, metadata_file)
        
        metrics = getattr(self, '_current_metrics', [])
        self._current_metrics = []
        
        # 计算汇总统计
        summary_stats = self._calculate_index_stats(metrics)
        
        return BenchmarkResult(
            test_name="index_operations",
            metrics=metrics,
            summary_stats=summary_stats,
            timestamp=time.time(),
            config_snapshot=asdict(self.config)
        )
    
    def benchmark_end_to_end(self) -> BenchmarkResult:
        """端到端性能基准测试"""
        logger.info("Starting end-to-end benchmark...")
        
        # 确保系统已初始化
        try:
            self.index_manager.load_index()
        except:
            logger.warning("No existing index found, skipping end-to-end benchmark")
            return BenchmarkResult(
                test_name="end_to_end",
                metrics=[],
                summary_stats={'error': 'no_index'},
                timestamp=time.time(),
                config_snapshot=asdict(self.config)
            )
        
        metrics = []
        
        for i, query in enumerate(self.test_queries):
            with self.measure_performance(f"end_to_end_query_{i}", query=query):
                # 模拟完整的查询流程
                from ask import CodeAssistant
                assistant = CodeAssistant()
                results = assistant.dynamic_search(query)
        
        metrics = getattr(self, '_current_metrics', [])
        self._current_metrics = []
        
        # 计算端到端统计
        query_times = [m.duration for m in metrics if m.success]
        summary_stats = {
            'avg_query_time': np.mean(query_times) if query_times else 0,
            'max_query_time': max(query_times) if query_times else 0,
            'min_query_time': min(query_times) if query_times else 0,
            'success_rate': len(query_times) / len(metrics) if metrics else 0,
            'queries_per_second': 1 / np.mean(query_times) if query_times else 0
        }
        
        return BenchmarkResult(
            test_name="end_to_end",
            metrics=metrics,
            summary_stats=summary_stats,
            timestamp=time.time(),
            config_snapshot=asdict(self.config)
        )
    
    def run_full_benchmark(self) -> Dict[str, BenchmarkResult]:
        """运行完整的基准测试套件"""
        logger.info("Starting full benchmark suite...")
        
        results = {}
        
        try:
            results['model_loading'] = self.benchmark_model_loading()
        except Exception as e:
            logger.error(f"Model loading benchmark failed: {e}")
        
        try:
            results['embedding_generation'] = self.benchmark_embedding_generation()
        except Exception as e:
            logger.error(f"Embedding generation benchmark failed: {e}")
        
        try:
            results['index_operations'] = self.benchmark_index_operations()
        except Exception as e:
            logger.error(f"Index operations benchmark failed: {e}")
        
        try:
            results['end_to_end'] = self.benchmark_end_to_end()
        except Exception as e:
            logger.error(f"End-to-end benchmark failed: {e}")
        
        # 生成报告
        self._generate_report(results)
        
        return results
    
    def _calculate_embedding_stats(self, metrics: List[PerformanceMetrics]) -> Dict[str, float]:
        """计算嵌入生成统计"""
        successful_metrics = [m for m in metrics if m.success]
        
        if not successful_metrics:
            return {'error': 'no_successful_operations'}
        
        durations = [m.duration for m in successful_metrics]
        batch_sizes = [m.additional_data.get('kwargs', {}).get('batch_size', 1) 
                      for m in successful_metrics]
        
        throughputs = [batch_sizes[i] / durations[i] for i in range(len(durations))]
        
        return {
            'avg_duration': np.mean(durations),
            'max_duration': max(durations),
            'min_duration': min(durations),
            'avg_throughput': np.mean(throughputs),
            'max_throughput': max(throughputs),
            'total_operations': len(successful_metrics)
        }
    
    def _calculate_index_stats(self, metrics: List[PerformanceMetrics]) -> Dict[str, float]:
        """计算索引操作统计"""
        successful_metrics = [m for m in metrics if m.success]
        
        if not successful_metrics:
            return {'error': 'no_successful_operations'}
        
        # 分类不同操作
        creation_metrics = [m for m in successful_metrics if 'creation' in m.operation]
        search_metrics = [m for m in successful_metrics if 'search' in m.operation]
        save_metrics = [m for m in successful_metrics if 'save' in m.operation]
        
        stats = {}
        
        if creation_metrics:
            stats['creation_time'] = creation_metrics[0].duration
        
        if search_metrics:
            search_times = [m.duration for m in search_metrics]
            stats['avg_search_time'] = np.mean(search_times)
            stats['max_search_time'] = max(search_times)
            stats['min_search_time'] = min(search_times)
        
        if save_metrics:
            stats['save_time'] = save_metrics[0].duration
        
        return stats
    
    def _generate_report(self, results: Dict[str, BenchmarkResult]):
        """生成性能报告"""
        report = {
            'timestamp': time.time(),
            'config': asdict(self.config),
            'system_info': {
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
                'python_version': __import__('sys').version
            },
            'results': {}
        }
        
        for test_name, result in results.items():
            report['results'][test_name] = {
                'summary_stats': result.summary_stats,
                'num_operations': len(result.metrics),
                'success_rate': len([m for m in result.metrics if m.success]) / len(result.metrics) if result.metrics else 0
            }
        
        # 保存报告
        report_file = f"benchmark_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Benchmark report saved to {report_file}")
        
        # 打印摘要
        self._print_summary(report)
    
    def _print_summary(self, report: Dict[str, Any]):
        """打印基准测试摘要"""
        print("\n" + "="*60)
        print("PERFORMANCE BENCHMARK SUMMARY")
        print("="*60)
        
        for test_name, result in report['results'].items():
            print(f"\n{test_name.upper()}:")
            print(f"  Operations: {result['num_operations']}")
            print(f"  Success Rate: {result['success_rate']:.2%}")
            
            for stat_name, value in result['summary_stats'].items():
                if isinstance(value, float):
                    if 'time' in stat_name:
                        print(f"  {stat_name}: {value:.3f}s")
                    elif 'memory' in stat_name:
                        print(f"  {stat_name}: {value:.1f}MB")
                    else:
                        print(f"  {stat_name}: {value:.3f}")
                else:
                    print(f"  {stat_name}: {value}")

def run_benchmark():
    """运行基准测试的入口函数"""
    benchmark = PerformanceBenchmark()
    return benchmark.run_full_benchmark()

if __name__ == "__main__":
    run_benchmark()