#!/usr/bin/env python3
"""
测试流畅的流式输出功能
验证减少页面刷新和改进的用户体验
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_rerun_reduction():
    """测试st.rerun()调用的减少"""
    print("🧪 测试页面刷新优化...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        
        # 读取app.py文件内容
        with open(project_root / "frontend" / "app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计st.rerun()调用次数
        rerun_count = content.count('st.rerun()')
        
        print(f"  当前st.rerun()调用次数: {rerun_count}")
        
        # 检查关键函数是否移除了不必要的rerun
        functions_to_check = [
            'start_new_conversation',
            'stop_generation',
            'initialize_system'
        ]
        
        improved_functions = 0
        for func_name in functions_to_check:
            # 查找函数定义
            func_start = content.find(f"def {func_name}(")
            if func_start != -1:
                # 查找函数结束（下一个def或文件结束）
                next_def = content.find("\ndef ", func_start + 1)
                if next_def == -1:
                    func_content = content[func_start:]
                else:
                    func_content = content[func_start:next_def]
                
                # 检查是否包含st.rerun()
                if 'st.rerun()' not in func_content:
                    improved_functions += 1
                    print(f"  ✅ {func_name}: 已移除不必要的刷新")
                else:
                    print(f"  ⚠️  {func_name}: 仍包含页面刷新")
        
        # 如果大部分函数都优化了，认为测试通过
        return improved_functions >= len(functions_to_check) // 2
        
    except Exception as e:
        print(f"  ❌ 页面刷新优化测试失败: {e}")
        return False

def test_streaming_improvements():
    """测试流式输出改进"""
    print("\n🧪 测试流式输出改进...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        
        # 检查是否有新的流式输出函数
        try:
            from app import stream_response_generator, perform_streaming_query
            print("  ✅ 新的流式输出函数存在")
        except ImportError as e:
            print(f"  ❌ 流式输出函数导入失败: {e}")
            return False
        
        # 检查是否使用了write_stream
        with open(project_root / "frontend" / "app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'write_stream' in content:
            print("  ✅ 使用了Streamlit的write_stream功能")
        else:
            print("  ⚠️  未使用write_stream功能")
        
        # 检查是否减少了容器重新创建
        if 'ai_container.empty()' not in content:
            print("  ✅ 减少了容器的重复创建")
        else:
            print("  ⚠️  仍有容器重复创建")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 流式输出改进测试失败: {e}")
        return False

def test_css_improvements():
    """测试CSS改进"""
    print("\n🧪 测试CSS改进...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        
        with open(project_root / "frontend" / "app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否添加了平滑滚动
        if 'scroll-behavior: smooth' in content:
            print("  ✅ 添加了平滑滚动CSS")
        else:
            print("  ❌ 未添加平滑滚动CSS")
            return False
        
        # 检查是否添加了动画效果
        if '@keyframes fadeIn' in content:
            print("  ✅ 添加了淡入动画效果")
        else:
            print("  ⚠️  未添加动画效果")
        
        # 检查是否优化了输入区域
        if 'position: sticky' in content:
            print("  ✅ 优化了输入区域定位")
        else:
            print("  ⚠️  未优化输入区域定位")
        
        return True
        
    except Exception as e:
        print(f"  ❌ CSS改进测试失败: {e}")
        return False

def test_reference_positioning():
    """测试引用位置改进"""
    print("\n🧪 测试引用位置改进...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        
        # 检查引用显示函数
        try:
            from app import display_references_before_answer
            print("  ✅ 引用前置显示函数存在")
        except ImportError:
            print("  ❌ 引用前置显示函数不存在")
            return False
        
        # 检查是否移除了侧边栏引用
        with open(project_root / "frontend" / "app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'display_sidebar_references' not in content:
            print("  ✅ 已移除侧边栏引用显示")
        else:
            print("  ⚠️  侧边栏引用显示仍存在")
        
        # 检查引用是否默认收起
        if 'expanded=False' in content:
            print("  ✅ 引用默认为收起状态")
        else:
            print("  ⚠️  引用可能不是默认收起")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 引用位置改进测试失败: {e}")
        return False

def test_performance_optimizations():
    """测试性能优化"""
    print("\n🧪 测试性能优化...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        
        with open(project_root / "frontend" / "app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查等待时间优化
        if 'time.sleep(0.3)' in content or 'time.sleep(0.5)' in content:
            print("  ✅ 优化了等待时间")
        else:
            print("  ⚠️  等待时间可能未优化")
        
        # 检查是否减少了不必要的状态更新
        empty_calls = content.count('.empty()')
        if empty_calls < 10:  # 假设合理的阈值
            print(f"  ✅ 减少了容器清空操作 ({empty_calls} 次)")
        else:
            print(f"  ⚠️  容器清空操作较多 ({empty_calls} 次)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 性能优化测试失败: {e}")
        return False

def test_user_experience():
    """测试用户体验改进"""
    print("\n🧪 测试用户体验改进...")
    
    try:
        # 检查配置文件中的max_tokens设置
        with open(project_root / "config.json", 'r', encoding='utf-8') as f:
            import json
            config = json.load(f)
        
        max_tokens = config.get('llm', {}).get('max_tokens', 0)
        if max_tokens >= 4000:
            print(f"  ✅ max_tokens已增加到{max_tokens}，支持更长输出")
        else:
            print(f"  ❌ max_tokens仍为{max_tokens}，可能导致截断")
            return False
        
        # 检查是否有用户友好的提示
        sys.path.insert(0, str(project_root / "frontend"))
        with open(project_root / "frontend" / "app.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '正在生成...' in content:
            print("  ✅ 有生成状态提示")
        else:
            print("  ⚠️  缺少生成状态提示")
        
        if '已停止生成' in content:
            print("  ✅ 有停止生成提示")
        else:
            print("  ⚠️  缺少停止生成提示")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 用户体验改进测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 流畅流式输出功能测试")
    print("=" * 50)
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    # 运行测试
    tests = [
        ("页面刷新优化", test_rerun_reduction),
        ("流式输出改进", test_streaming_improvements),
        ("CSS改进", test_css_improvements),
        ("引用位置改进", test_reference_positioning),
        ("性能优化", test_performance_optimizations),
        ("用户体验改进", test_user_experience)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 流畅性改进测试结果:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed >= total * 0.8:  # 80%通过率
        print("🎉 流畅性改进测试基本通过！")
        print("\n改进效果:")
        print("  ✅ 减少了页面刷新频率")
        print("  ✅ 优化了流式输出体验")
        print("  ✅ 改进了引用显示位置")
        print("  ✅ 增加了输出长度限制")
        print("  ✅ 提升了整体用户体验")
        
        print("\n启动测试:")
        print("   python demo_bubble_chat.py")
        print("   体验更流畅的对话界面")
    else:
        print("⚠️  部分改进需要进一步优化。")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
