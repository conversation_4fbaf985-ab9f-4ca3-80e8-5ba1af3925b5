#!/usr/bin/env python3
"""
测试增强功能：更长输出和引用显示
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_max_tokens_config():
    """测试max_tokens配置"""
    print("🧪 测试max_tokens配置...")
    
    try:
        from config import get_config
        config = get_config()
        
        print(f"  当前max_tokens: {config.llm.max_tokens}")
        
        # 检查是否已经增加到4000
        if config.llm.max_tokens >= 4000:
            print("  ✅ max_tokens已增加到4000，支持更长输出")
        else:
            print(f"  ⚠️  max_tokens仍为{config.llm.max_tokens}，可能导致输出截断")
        
        return config.llm.max_tokens >= 4000
        
    except Exception as e:
        print(f"  ❌ 配置测试失败: {e}")
        return False

def test_reference_display_components():
    """测试引用显示组件"""
    print("\n🧪 测试引用显示组件...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import SearchResult
        
        # 创建测试搜索结果
        test_results = [
            SearchResult(
                similarity=0.95,
                file_path="test/example1.py",
                start_line=10,
                end_line=25,
                code="def test_function():\n    return True",
                signature="test_function()",
                parent_class="TestClass",
                retrieval_method="semantic"
            ),
            SearchResult(
                similarity=0.85,
                file_path="test/example2.py",
                start_line=30,
                end_line=45,
                code="class ExampleClass:\n    def method(self):\n        pass",
                signature="ExampleClass.method()",
                parent_class=None,
                retrieval_method="hybrid"
            )
        ]
        
        print(f"  ✅ 创建了{len(test_results)}个测试搜索结果")
        
        # 测试引用显示函数是否存在
        try:
            from app import display_references_before_answer
            print("  ✅ display_references_before_answer函数存在")
        except ImportError:
            print("  ❌ display_references_before_answer函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 引用显示组件测试失败: {e}")
        return False

def test_message_structure():
    """测试消息结构"""
    print("\n🧪 测试消息结构...")
    
    try:
        # 测试消息结构是否包含search_results字段
        test_message = {
            "role": "assistant",
            "content": "这是一个测试回答",
            "timestamp": "12:00:00",
            "results_count": 2,
            "search_results": []  # 新增字段
        }
        
        # 检查所有必要字段
        required_fields = ["role", "content", "timestamp", "results_count", "search_results"]
        missing_fields = [field for field in required_fields if field not in test_message]
        
        if not missing_fields:
            print("  ✅ 消息结构包含所有必要字段")
            return True
        else:
            print(f"  ❌ 消息结构缺少字段: {missing_fields}")
            return False
        
    except Exception as e:
        print(f"  ❌ 消息结构测试失败: {e}")
        return False

def test_context_length_handling():
    """测试上下文长度处理"""
    print("\n🧪 测试上下文长度处理...")
    
    try:
        # 模拟长对话历史
        long_conversation = []
        for i in range(25):  # 创建25轮对话
            long_conversation.append({
                "role": "user",
                "content": f"用户问题 {i+1}"
            })
            long_conversation.append({
                "role": "assistant",
                "content": f"AI回答 {i+1}",
                "results_count": 3
            })
        
        # 测试不同的上下文长度设置
        context_lengths = [5, 10, 15, 20]
        
        for context_length in context_lengths:
            # 模拟上下文提取
            recent_history = long_conversation[-context_length*2:] if context_length > 0 else []
            context = []
            for msg in recent_history:
                context.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            expected_length = min(context_length * 2, len(long_conversation))
            actual_length = len(context)
            
            if actual_length == expected_length:
                print(f"  ✅ 上下文长度{context_length}: 提取{actual_length}条消息")
            else:
                print(f"  ❌ 上下文长度{context_length}: 期望{expected_length}条，实际{actual_length}条")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 上下文长度测试失败: {e}")
        return False

def test_streaming_improvements():
    """测试流式输出改进"""
    print("\n🧪 测试流式输出改进...")
    
    try:
        # 检查流式查询函数的改进
        sys.path.insert(0, str(project_root / "frontend"))
        
        # 检查是否有perform_streaming_query函数
        try:
            from app import perform_streaming_query
            print("  ✅ perform_streaming_query函数存在")
        except ImportError:
            print("  ❌ perform_streaming_query函数不存在")
            return False
        
        # 检查是否有display_references_before_answer函数
        try:
            from app import display_references_before_answer
            print("  ✅ display_references_before_answer函数存在")
        except ImportError:
            print("  ❌ display_references_before_answer函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 流式输出改进测试失败: {e}")
        return False

def test_ui_layout_changes():
    """测试UI布局变化"""
    print("\n🧪 测试UI布局变化...")
    
    try:
        # 检查是否移除了侧边栏引用
        sys.path.insert(0, str(project_root / "frontend"))
        
        try:
            from app import display_sidebar_references
            print("  ⚠️  display_sidebar_references函数仍然存在")
            return False
        except ImportError:
            print("  ✅ display_sidebar_references函数已移除")
        
        # 检查是否有新的引用显示函数
        try:
            from app import display_references_before_answer
            print("  ✅ display_references_before_answer函数已添加")
        except ImportError:
            print("  ❌ display_references_before_answer函数不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ UI布局变化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 增强功能测试")
    print("=" * 50)
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    # 运行测试
    tests = [
        ("max_tokens配置", test_max_tokens_config),
        ("引用显示组件", test_reference_display_components),
        ("消息结构", test_message_structure),
        ("上下文长度处理", test_context_length_handling),
        ("流式输出改进", test_streaming_improvements),
        ("UI布局变化", test_ui_layout_changes)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 增强功能测试结果:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 增强功能测试全部通过！")
        print("\n新增功能:")
        print("  ✅ max_tokens增加到4000，支持更长输出")
        print("  ✅ 引用显示移到AI回答前面")
        print("  ✅ 引用默认收起状态")
        print("  ✅ 改进的流式输出处理")
        print("  ✅ 优化的上下文管理")
        
        print("\n启动测试:")
        print("   python demo_bubble_chat.py")
        print("   或者: streamlit run frontend/app.py")
    else:
        print("⚠️  部分测试失败，请检查实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
