#!/usr/bin/env python3
"""
UniXcoder RAG系统完善程度综合测试
评估系统的各个方面：功能完整性、性能、用户体验、稳定性等
"""

import sys
import os
import time
import json
import subprocess
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class SystemTester:
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
    def log_test(self, category, test_name, result, details=""):
        """记录测试结果"""
        if category not in self.test_results:
            self.test_results[category] = []
        
        self.test_results[category].append({
            "name": test_name,
            "result": result,
            "details": details,
            "timestamp": datetime.now().strftime("%H:%M:%S")
        })
        
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}: {details}")

def test_core_functionality():
    """测试核心功能完整性"""
    print("🔧 测试核心功能完整性")
    print("-" * 40)
    
    tester = SystemTester()
    
    # 1. 配置文件测试
    try:
        with open("config.json", 'r') as f:
            config = json.load(f)
        
        # 检查关键配置
        required_configs = [
            ("llm.max_tokens", config.get("llm", {}).get("max_tokens", 0) >= 4000),
            ("llm.model_name", bool(config.get("llm", {}).get("model_name"))),
            ("retrieval.top_k", config.get("retrieval", {}).get("top_k", 0) > 0),
            ("index.chunk_size", config.get("index", {}).get("chunk_size", 0) > 0)
        ]
        
        for config_name, is_valid in required_configs:
            tester.log_test("核心功能", f"配置项 {config_name}", is_valid)
            
    except Exception as e:
        tester.log_test("核心功能", "配置文件加载", False, str(e))
    
    # 2. 索引文件测试
    index_files = ["faiss_index.bin", "metadata.pkl"]
    for file in index_files:
        exists = Path(file).exists()
        size = Path(file).stat().st_size if exists else 0
        tester.log_test("核心功能", f"索引文件 {file}", exists and size > 0, 
                       f"大小: {size/1024/1024:.1f}MB" if exists else "文件不存在")
    
    # 3. 前端组件测试
    frontend_files = [
        "frontend/app.py",
        "frontend/api_service.py"
    ]
    
    for file in frontend_files:
        exists = Path(file).exists()
        tester.log_test("核心功能", f"前端文件 {file}", exists)
    
    # 4. 核心模块导入测试
    try:
        sys.path.insert(0, str(project_root))
        from config import get_config
        from rag_system import RAGSystem
        tester.log_test("核心功能", "核心模块导入", True)
    except Exception as e:
        tester.log_test("核心功能", "核心模块导入", False, str(e))
    
    return tester.test_results

def test_retrieval_quality():
    """测试检索质量"""
    print("\n🔍 测试检索质量")
    print("-" * 40)
    
    tester = SystemTester()
    
    # 测试查询
    test_queries = [
        "代码分块算法是如何工作的？",
        "如何实现语义搜索？",
        "UniXcoder模型的特点",
        "混合检索器的实现",
        "如何优化检索性能？"
    ]
    
    try:
        sys.path.insert(0, str(project_root))
        from rag_system import RAGSystem
        
        # 初始化系统
        rag = RAGSystem()
        rag.initialize()
        
        for query in test_queries:
            try:
                results = rag.search(query, top_k=5)
                
                # 评估检索质量
                if results:
                    avg_similarity = sum(r.similarity for r in results) / len(results)
                    high_quality_count = len([r for r in results if r.similarity > 0.7])
                    
                    quality_score = (avg_similarity + high_quality_count/len(results)) / 2
                    is_good = quality_score > 0.6
                    
                    tester.log_test("检索质量", f"查询: {query[:20]}...", is_good,
                                   f"平均相似度: {avg_similarity:.3f}, 高质量: {high_quality_count}/{len(results)}")
                else:
                    tester.log_test("检索质量", f"查询: {query[:20]}...", False, "无结果")
                    
            except Exception as e:
                tester.log_test("检索质量", f"查询: {query[:20]}...", False, str(e))
                
    except Exception as e:
        tester.log_test("检索质量", "系统初始化", False, str(e))
    
    return tester.test_results

def test_ai_response_quality():
    """测试AI回答质量"""
    print("\n🤖 测试AI回答质量")
    print("-" * 40)
    
    tester = SystemTester()
    
    # 测试不同类型的问题
    test_cases = [
        {
            "query": "什么是代码分块？",
            "expected_keywords": ["分块", "chunk", "代码", "算法"],
            "type": "概念解释"
        },
        {
            "query": "如何实现语义搜索？",
            "expected_keywords": ["语义", "搜索", "embedding", "向量"],
            "type": "技术实现"
        },
        {
            "query": "UniXcoder模型有什么优势？",
            "expected_keywords": ["UniXcoder", "模型", "优势", "特点"],
            "type": "模型特性"
        }
    ]
    
    try:
        sys.path.insert(0, str(project_root))
        from rag_system import RAGSystem
        
        rag = RAGSystem()
        rag.initialize()
        
        for case in test_cases:
            try:
                # 获取AI回答
                response = rag.query(case["query"])
                
                if response:
                    # 检查回答长度
                    length_ok = len(response) > 100
                    
                    # 检查关键词覆盖
                    keywords_found = sum(1 for kw in case["expected_keywords"] 
                                       if kw.lower() in response.lower())
                    keyword_coverage = keywords_found / len(case["expected_keywords"])
                    
                    # 检查是否有代码引用
                    has_references = "根据代码" in response or "从代码中" in response
                    
                    overall_quality = (length_ok + (keyword_coverage > 0.5) + has_references) / 3
                    is_good = overall_quality > 0.6
                    
                    tester.log_test("AI回答质量", f"{case['type']}", is_good,
                                   f"长度: {len(response)}, 关键词: {keywords_found}/{len(case['expected_keywords'])}")
                else:
                    tester.log_test("AI回答质量", f"{case['type']}", False, "无回答")
                    
            except Exception as e:
                tester.log_test("AI回答质量", f"{case['type']}", False, str(e))
                
    except Exception as e:
        tester.log_test("AI回答质量", "系统初始化", False, str(e))
    
    return tester.test_results

def test_frontend_functionality():
    """测试前端功能"""
    print("\n🖥️ 测试前端功能")
    print("-" * 40)
    
    tester = SystemTester()
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        
        # 1. 前端组件导入测试
        try:
            from app import (
                initialize_session_state,
                display_message_bubble,
                display_references_before_answer,
                perform_streaming_query
            )
            tester.log_test("前端功能", "关键组件导入", True)
        except ImportError as e:
            tester.log_test("前端功能", "关键组件导入", False, str(e))
        
        # 2. API服务测试
        try:
            from api_service import get_api_instance, QueryRequest, SearchResult
            api = get_api_instance()
            tester.log_test("前端功能", "API服务初始化", True)
        except Exception as e:
            tester.log_test("前端功能", "API服务初始化", False, str(e))
        
        # 3. 会话状态结构测试
        required_states = [
            "conversation_history",
            "current_results", 
            "is_generating",
            "current_response"
        ]
        
        # 模拟会话状态
        mock_session = {}
        for state in required_states:
            mock_session[state] = [] if "history" in state or "results" in state else False
        
        all_states_present = len(mock_session) == len(required_states)
        tester.log_test("前端功能", "会话状态结构", all_states_present)
        
    except Exception as e:
        tester.log_test("前端功能", "前端模块加载", False, str(e))
    
    return tester.test_results

def test_performance():
    """测试系统性能"""
    print("\n⚡ 测试系统性能")
    print("-" * 40)
    
    tester = SystemTester()
    
    try:
        sys.path.insert(0, str(project_root))
        from rag_system import RAGSystem
        
        rag = RAGSystem()
        
        # 1. 初始化性能测试
        start_time = time.time()
        rag.initialize()
        init_time = time.time() - start_time
        
        init_ok = init_time < 30  # 30秒内初始化完成
        tester.log_test("系统性能", "初始化时间", init_ok, f"{init_time:.2f}秒")
        
        # 2. 检索性能测试
        test_query = "代码分块算法"
        start_time = time.time()
        results = rag.search(test_query, top_k=10)
        search_time = time.time() - start_time
        
        search_ok = search_time < 5  # 5秒内完成检索
        tester.log_test("系统性能", "检索响应时间", search_ok, f"{search_time:.2f}秒")
        
        # 3. 内存使用测试
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        
        memory_ok = memory_mb < 2048  # 小于2GB
        tester.log_test("系统性能", "内存使用", memory_ok, f"{memory_mb:.1f}MB")
        
    except Exception as e:
        tester.log_test("系统性能", "性能测试", False, str(e))
    
    return tester.test_results

def test_error_handling():
    """测试错误处理"""
    print("\n🛡️ 测试错误处理")
    print("-" * 40)
    
    tester = SystemTester()
    
    try:
        sys.path.insert(0, str(project_root))
        from rag_system import RAGSystem
        
        rag = RAGSystem()
        rag.initialize()
        
        # 1. 空查询测试
        try:
            result = rag.search("")
            empty_query_handled = result is not None  # 应该返回空结果而不是崩溃
            tester.log_test("错误处理", "空查询处理", empty_query_handled)
        except Exception as e:
            tester.log_test("错误处理", "空查询处理", False, str(e))
        
        # 2. 超长查询测试
        try:
            long_query = "测试" * 1000  # 超长查询
            result = rag.search(long_query)
            long_query_handled = True
            tester.log_test("错误处理", "超长查询处理", long_query_handled)
        except Exception as e:
            tester.log_test("错误处理", "超长查询处理", False, str(e))
        
        # 3. 特殊字符测试
        try:
            special_query = "!@#$%^&*()_+{}|:<>?[]\\;'\",./"
            result = rag.search(special_query)
            special_char_handled = True
            tester.log_test("错误处理", "特殊字符处理", special_char_handled)
        except Exception as e:
            tester.log_test("错误处理", "特殊字符处理", False, str(e))
            
    except Exception as e:
        tester.log_test("错误处理", "错误处理测试", False, str(e))
    
    return tester.test_results

def test_user_experience():
    """测试用户体验"""
    print("\n👥 测试用户体验")
    print("-" * 40)
    
    tester = SystemTester()
    
    # 1. 界面响应性测试
    try:
        with open("frontend/app.py", 'r', encoding='utf-8') as f:
            frontend_code = f.read()
        
        # 检查是否有用户友好的提示
        user_friendly_features = [
            ("加载提示", "正在" in frontend_code),
            ("错误提示", "错误" in frontend_code or "失败" in frontend_code),
            ("成功提示", "成功" in frontend_code or "完成" in frontend_code),
            ("帮助信息", "help" in frontend_code or "帮助" in frontend_code),
            ("示例问题", "示例" in frontend_code or "example" in frontend_code)
        ]
        
        for feature_name, has_feature in user_friendly_features:
            tester.log_test("用户体验", feature_name, has_feature)
            
    except Exception as e:
        tester.log_test("用户体验", "界面友好性", False, str(e))
    
    # 2. 文档完整性测试
    doc_files = [
        "README.md",
        "docs/STREAMLIT_FRONTEND.md",
        "SMOOTH_STREAMING_OPTIMIZATION.md"
    ]
    
    for doc_file in doc_files:
        exists = Path(doc_file).exists()
        if exists:
            size = Path(doc_file).stat().st_size
            is_substantial = size > 1000  # 至少1KB的文档
            tester.log_test("用户体验", f"文档 {doc_file}", is_substantial, 
                           f"{size}字节" if exists else "不存在")
        else:
            tester.log_test("用户体验", f"文档 {doc_file}", False, "不存在")
    
    return tester.test_results

def generate_comprehensive_report(all_results):
    """生成综合测试报告"""
    print("\n" + "="*60)
    print("📊 UniXcoder RAG系统完善程度评估报告")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    # 统计各类别结果
    category_scores = {}
    
    for category, results in all_results.items():
        if not results:
            continue
            
        category_total = len(results)
        category_passed = sum(1 for r in results if r["result"])
        category_score = category_passed / category_total if category_total > 0 else 0
        
        category_scores[category] = {
            "passed": category_passed,
            "total": category_total,
            "score": category_score
        }
        
        total_tests += category_total
        passed_tests += category_passed
        
        print(f"\n📋 {category}")
        print(f"   通过: {category_passed}/{category_total} ({category_score*100:.1f}%)")
        
        # 显示失败的测试
        failed_tests = [r for r in results if not r["result"]]
        if failed_tests:
            print("   ❌ 失败项目:")
            for test in failed_tests[:3]:  # 只显示前3个失败项目
                print(f"      - {test['name']}: {test['details']}")
    
    # 总体评分
    overall_score = passed_tests / total_tests if total_tests > 0 else 0
    
    print(f"\n🎯 总体评估")
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   通过率: {overall_score*100:.1f}%")
    
    # 评级
    if overall_score >= 0.9:
        grade = "A+ (优秀)"
        emoji = "🏆"
    elif overall_score >= 0.8:
        grade = "A (良好)"
        emoji = "🥇"
    elif overall_score >= 0.7:
        grade = "B (合格)"
        emoji = "🥈"
    elif overall_score >= 0.6:
        grade = "C (需改进)"
        emoji = "🥉"
    else:
        grade = "D (需重构)"
        emoji = "⚠️"
    
    print(f"   系统评级: {emoji} {grade}")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    
    for category, score_info in category_scores.items():
        if score_info["score"] < 0.8:
            print(f"   - {category}: 需要重点关注 (通过率: {score_info['score']*100:.1f}%)")
    
    # 优势分析
    print(f"\n✨ 系统优势:")
    for category, score_info in category_scores.items():
        if score_info["score"] >= 0.9:
            print(f"   - {category}: 表现优秀 (通过率: {score_info['score']*100:.1f}%)")
    
    return overall_score, category_scores

def main():
    """主测试函数"""
    print("🚀 UniXcoder RAG系统完善程度综合测试")
    print("="*60)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试环境: Python {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 执行各项测试
    all_results = {}
    
    test_functions = [
        ("核心功能", test_core_functionality),
        ("检索质量", test_retrieval_quality),
        ("AI回答质量", test_ai_response_quality),
        ("前端功能", test_frontend_functionality),
        ("系统性能", test_performance),
        ("错误处理", test_error_handling),
        ("用户体验", test_user_experience)
    ]
    
    for category, test_func in test_functions:
        try:
            results = test_func()
            all_results.update(results)
        except Exception as e:
            print(f"❌ {category}测试异常: {e}")
            all_results[category] = [{"name": "测试执行", "result": False, "details": str(e)}]
    
    # 生成综合报告
    overall_score, category_scores = generate_comprehensive_report(all_results)
    
    # 保存详细报告
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "overall_score": overall_score,
            "category_scores": category_scores,
            "detailed_results": all_results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存至: {report_file}")
    
    return overall_score >= 0.7

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
