import os
import hashlib
from typing import List, Dict, Any, Optional, <PERSON>ple
from dataclasses import dataclass
from tree_sitter import Language, Parser
import tree_sitter_python

from config import get_config
from logger import get_logger, performance_monitor

logger = get_logger(__name__)

@dataclass
class ChunkMetadata:
    """代码块元数据"""
    file_path: str
    start_line: int
    end_line: int
    chunk_type: str
    name: Optional[str] = None
    signature: Optional[str] = None
    docstring: Optional[str] = None
    parent_class: Optional[str] = None
    imports: List[str] = None
    complexity_score: float = 0.0
    semantic_hash: Optional[str] = None

@dataclass
class CodeChunk:
    """代码块"""
    code: str
    metadata: ChunkMetadata
    embedding: Optional[Any] = None

class AdvancedCodeChunker:
    """高级代码分块器"""
    
    def __init__(self):
        self.config = get_config()
        
        # 设置 Python 解析器
        self.parser = Parser()
        py_language = Language(tree_sitter_python.language())
        self.parser.language = py_language
        self.PY_LANGUAGE = py_language
        
        # 分块配置
        self.max_tokens = self.config.chunking.max_tokens
        self.overlap_ratio = self.config.chunking.overlap_ratio
        self.min_chunk_size = self.config.chunking.min_chunk_size
        self.semantic_splitting = self.config.chunking.semantic_splitting
        self.preserve_structure = self.config.chunking.preserve_structure
        
        logger.info(f"Initialized AdvancedCodeChunker with max_tokens={self.max_tokens}")

    def _get_chunk_metadata(self, node, code_bytes: bytes) -> Dict[str, Any]:
        """为单个函数或类节点提取详细元数据。"""
        metadata = {
            "start_line": node.start_point[0] + 1,
            "end_line": node.end_point[0] + 1,
            "type": node.type
        }
        # 提取name
        name_node = node.child_by_field_name('name')
        if name_node:
            metadata['name'] = name_node.text.decode('utf8')
        # 提取简化signature
        body_node = node.child_by_field_name('body')
        if body_node:
            signature_end_byte = body_node.start_byte
            signature = code_bytes[node.start_byte:signature_end_byte].decode('utf8').strip()
            if signature.endswith(':'):
                signature = signature[:-1]
            # 只保留 def/class 后的名字部分
            if node.type == 'function_definition' or node.type == 'class_definition':
                tokens = signature.split()
                if len(tokens) >= 2:
                    metadata['signature'] = tokens[0] + ' ' + tokens[1]
                else:
                    metadata['signature'] = signature
            else:
                metadata['signature'] = signature
        # 提取文档字符串
        docstring_query = self.PY_LANGUAGE.query("""
            ( (function_definition body: (block . (expression_statement (string) @docstring)))
              (class_definition body: (block . (expression_statement (string) @docstring))) )
        """)
        captures = docstring_query.captures(node)
        if captures:
            docstring_node = captures[0][0]
            if docstring_node.parent.parent == node:
                 metadata['docstring'] = docstring_node.text.decode('utf8').strip()
        # 提取父类上下文
        parent = node.parent
        while parent:
            if parent.type == 'class_definition':
                class_name_node = parent.child_by_field_name('name')
                if class_name_node:
                    metadata['parent_class'] = class_name_node.text.decode('utf8')
                break
            parent = parent.parent
        return metadata
    
    def _count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        # 更精确的token计数：考虑代码的特殊性
        # 代码中的标识符、操作符等通常占用更多token
        words = text.split()
        
        # 基础单词计数
        token_count = len(words)
        
        # 调整因子：代码比普通文本需要更多token
        code_adjustment = 1.3
        
        # 考虑特殊字符和操作符
        special_chars = sum(1 for c in text if c in '()[]{}.,;:="\'`')
        token_count += special_chars * 0.1
        
        return int(token_count * code_adjustment)
    
    def _calculate_complexity_score(self, node, code_bytes: bytes) -> float:
        """计算代码复杂度分数"""
        complexity = 0.0
        
        # 基于节点类型的复杂度
        complexity_weights = {
            'if_statement': 1.0,
            'while_statement': 1.0,
            'for_statement': 1.0,
            'try_statement': 1.0,
            'with_statement': 0.5,
            'function_definition': 2.0,
            'class_definition': 3.0,
            'lambda': 0.5,
            'list_comprehension': 1.0,
            'dictionary_comprehension': 1.0
        }
        
        def traverse(node):
            nonlocal complexity
            complexity += complexity_weights.get(node.type, 0)
            for child in node.children:
                traverse(child)
        
        traverse(node)
        
        # 基于代码长度的调整
        code_length = len(code_bytes)
        length_factor = min(code_length / 1000, 2.0)  # 最多2倍
        
        return complexity * (1 + length_factor)
    
    def _generate_semantic_hash(self, code: str) -> str:
        """生成代码的语义哈希"""
        # 移除注释和空白行，生成基于代码结构的哈希
        lines = [line.strip() for line in code.split('\n') 
                if line.strip() and not line.strip().startswith('#')]
        
        # 提取关键词和结构
        structural_elements = []
        for line in lines:
            # 提取函数定义、类定义等关键结构
            if any(keyword in line for keyword in ['def ', 'class ', 'if ', 'for ', 'while ']):
                structural_elements.append(line)
        
        content = '\n'.join(structural_elements)
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

    def _extract_imports(self, node, code_bytes: bytes) -> List[str]:
        """使用 tree-sitter 查询提取文件中的所有导入语句。"""
        import_query = self.PY_LANGUAGE.query("""
            (import_statement) @import
            (import_from_statement) @import_from
        """)
        imports = []
        captures = import_query.captures(node)
        for capture in captures:
            try:
                if isinstance(capture, (list, tuple)) and len(capture) >= 1:
                    capture_node = capture[0]
                    if hasattr(capture_node, 'start_byte') and hasattr(capture_node, 'end_byte'):
                        import_text = code_bytes[capture_node.start_byte:capture_node.end_byte].decode('utf-8', errors='ignore')
                        imports.append(import_text.strip())
            except Exception as e:
                logger.warning(f"Failed to extract import: {str(e)}")
        return imports

    @performance_monitor("file_chunking")
    def chunk_file(self, file_path: str) -> List[CodeChunk]:
        """对单个代码文件进行高级分块"""
        logger.debug(f"Chunking file: {file_path}")
        
        try:
            with open(file_path, "rb") as f:
                code_bytes = f.read()
                tree = self.parser.parse(code_bytes)
        except Exception as e:
            logger.error(f"Failed to parse file {file_path}: {e}")
            return []
        
        root_node = tree.root_node
        
        # 1. 提取文件级别的元数据
        imports = self._extract_imports(root_node, code_bytes)
        
        # 2. 智能分块策略
        if self.semantic_splitting:
            chunks = self._semantic_chunking(root_node, code_bytes, file_path, imports)
        else:
            chunks = self._traditional_chunking(root_node, code_bytes, file_path, imports)
        
        # 3. 后处理：添加重叠块（如果启用）
        if self.overlap_ratio > 0 and len(chunks) > 1:
            chunks = self._add_overlap_chunks(chunks, code_bytes)
        
        # 4. 过滤小块
        chunks = [chunk for chunk in chunks 
                 if self._count_tokens(chunk.code) >= self.min_chunk_size]
        
        logger.debug(f"Generated {len(chunks)} chunks for {file_path}")
        return chunks
    
    def _semantic_chunking(self, root_node, code_bytes: bytes, file_path: str, 
                          imports: List[str]) -> List[CodeChunk]:
        """语义感知分块"""
        chunks = []
        
        # 1. 提取所有语义单元
        semantic_units = self._extract_semantic_units(root_node, code_bytes)
        
        # 2. 按复杂度和大小分组
        grouped_units = self._group_semantic_units(semantic_units)
        
        # 3. 为每组创建块
        for group in grouped_units:
            chunk = self._create_chunk_from_units(group, code_bytes, file_path, imports)
            if chunk:
                chunks.append(chunk)
        
        return chunks
    
    def _traditional_chunking(self, root_node, code_bytes: bytes, file_path: str,
                            imports: List[str]) -> List[CodeChunk]:
        """传统递归分块（保留原有逻辑但增强）"""
        raw_chunks = self._recursive_chunking(root_node, code_bytes)
        
        chunks = []
        for raw_chunk in raw_chunks:
            metadata = ChunkMetadata(
                file_path=file_path,
                start_line=raw_chunk['metadata']['start_line'],
                end_line=raw_chunk['metadata']['end_line'],
                chunk_type=raw_chunk['metadata']['type'],
                name=raw_chunk['metadata'].get('name'),
                signature=raw_chunk['metadata'].get('signature'),
                docstring=raw_chunk['metadata'].get('docstring'),
                parent_class=raw_chunk['metadata'].get('parent_class'),
                imports=imports,
                semantic_hash=self._generate_semantic_hash(raw_chunk['code'])
            )
            
            chunk = CodeChunk(
                code=raw_chunk['code'],
                metadata=metadata
            )
            chunks.append(chunk)
        
        return chunks
    
    def _extract_semantic_units(self, root_node, code_bytes: bytes) -> List[Dict[str, Any]]:
        """提取语义单元"""
        units = []
        
        def traverse(node, context=None):
            # 识别语义重要的节点
            if node.type in ['function_definition', 'class_definition', 'async_function_definition']:
                unit = {
                    'node': node,
                    'type': node.type,
                    'context': context,
                    'complexity': self._calculate_complexity_score(node, code_bytes),
                    'size': node.end_byte - node.start_byte
                }
                units.append(unit)
                
                # 为类，继续遍历其方法
                if node.type == 'class_definition':
                    for child in node.children:
                        if child.type in ['function_definition', 'async_function_definition']:
                            traverse(child, context=node)
                return  # 不再深入
            
            # 继续遍历子节点
            for child in node.children:
                traverse(child, context)
        
        traverse(root_node)
        return units
    
    def _group_semantic_units(self, units: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """将语义单元分组"""
        groups = []
        current_group = []
        current_size = 0
        
        # 按复杂度排序，复杂度低的优先组合
        units.sort(key=lambda u: u['complexity'])
        
        for unit in units:
            estimated_tokens = self._count_tokens(
                unit['node'].text.decode('utf-8', errors='ignore')
            )
            
            # 如果单个单元就超过限制，单独成组
            if estimated_tokens > self.max_tokens:
                if current_group:
                    groups.append(current_group)
                    current_group = []
                    current_size = 0
                groups.append([unit])
                continue
            
            # 检查是否可以加入当前组
            if current_size + estimated_tokens <= self.max_tokens:
                current_group.append(unit)
                current_size += estimated_tokens
            else:
                # 开始新组
                if current_group:
                    groups.append(current_group)
                current_group = [unit]
                current_size = estimated_tokens
        
        # 添加最后一组
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _create_chunk_from_units(self, units: List[Dict[str, Any]], code_bytes: bytes,
                               file_path: str, imports: List[str]) -> Optional[CodeChunk]:
        """从语义单元组创建代码块"""
        if not units:
            return None
        
        # 找到代码范围
        start_byte = min(unit['node'].start_byte for unit in units)
        end_byte = max(unit['node'].end_byte for unit in units)
        
        code = code_bytes[start_byte:end_byte].decode('utf-8', errors='ignore')
        
        # 创建元数据（使用主要单元的信息）
        main_unit = max(units, key=lambda u: u['complexity'])
        main_node = main_unit['node']
        
        metadata = ChunkMetadata(
            file_path=file_path,
            start_line=main_node.start_point[0] + 1,
            end_line=main_node.end_point[0] + 1,
            chunk_type='semantic_group',
            imports=imports,
            complexity_score=sum(u['complexity'] for u in units),
            semantic_hash=self._generate_semantic_hash(code)
        )
        
        # 提取主要单元的详细信息
        detailed_metadata = self._get_chunk_metadata(main_node, code_bytes)
        for key, value in detailed_metadata.items():
            if hasattr(metadata, key) and value:
                setattr(metadata, key, value)
        
        return CodeChunk(code=code, metadata=metadata)
    
    def _add_overlap_chunks(self, chunks: List[CodeChunk], 
                          code_bytes: bytes) -> List[CodeChunk]:
        """添加重叠块以提高检索召回率"""
        overlap_chunks = []
        
        for i in range(len(chunks) - 1):
            current_chunk = chunks[i]
            next_chunk = chunks[i + 1]
            
            # 计算重叠区域
            overlap_size = int(self.max_tokens * self.overlap_ratio)
            
            # 创建重叠块
            overlap_start = max(0, current_chunk.metadata.end_line - overlap_size // 2)
            overlap_end = min(len(code_bytes.decode('utf-8').split('\n')), 
                            next_chunk.metadata.start_line + overlap_size // 2)
            
            if overlap_end > overlap_start:
                lines = code_bytes.decode('utf-8').split('\n')
                overlap_code = '\n'.join(lines[overlap_start-1:overlap_end])
                
                overlap_metadata = ChunkMetadata(
                    file_path=current_chunk.metadata.file_path,
                    start_line=overlap_start,
                    end_line=overlap_end,
                    chunk_type='overlap',
                    imports=current_chunk.metadata.imports,
                    semantic_hash=self._generate_semantic_hash(overlap_code)
                )
                
                overlap_chunk = CodeChunk(
                    code=overlap_code,
                    metadata=overlap_metadata
                )
                overlap_chunks.append(overlap_chunk)
        
        # 合并原始块和重叠块
        all_chunks = []
        for i, chunk in enumerate(chunks):
            all_chunks.append(chunk)
            if i < len(overlap_chunks):
                all_chunks.append(overlap_chunks[i])
        
        return all_chunks

    def _recursive_chunking(self, node, code_bytes):
        """
        递归地遍历AST，实现层级化/重叠分块。
        - 将函数作为基础的、独立的块。
        - 将类也作为块，但同时会继续递归，将其中的方法也作为独立的块。
        """
        chunks = []
        
        # 规则1: 如果是函数，将其作为块，并停止在此分支的递归
        if node.type == "function_definition":
            chunk_text = node.text.decode('utf8')
            if self._count_tokens(chunk_text) <= self.max_tokens:
                chunks.append({
                    "code": chunk_text,
                    "metadata": self._get_chunk_metadata(node, code_bytes)
                })
            return chunks

        # 规则2: 如果是类，将其作为块，但继续递归以寻找方法
        if node.type == "class_definition":
            chunk_text = node.text.decode('utf8')
            if self._count_tokens(chunk_text) <= self.max_tokens:
                chunks.append({
                    "code": chunk_text,
                    "metadata": self._get_chunk_metadata(node, code_bytes)
                })

        # 规则3: 对非函数节点，继续深入其子节点
        for child in node.children:
            chunks.extend(self._recursive_chunking(child, code_bytes))
            
        return chunks

# 保持向后兼容
class CodeChunker(AdvancedCodeChunker):
    """向后兼容的代码分块器"""
    
    def __init__(self, model_name="microsoft/unixcoder-base", max_tokens=900):
        super().__init__()
        self.max_tokens = max_tokens

def chunk_file_legacy(file_path: str) -> List[Dict[str, Any]]:
    """向后兼容的文件分块函数"""
    chunker = CodeChunker()
    chunks = chunker.chunk_file(file_path)
    
    # 转换为旧格式
    legacy_chunks = []
    for chunk in chunks:
        legacy_chunk = {
            'code': chunk.code,
            'metadata': {
                'file_path': chunk.metadata.file_path,
                'start_line': chunk.metadata.start_line,
                'end_line': chunk.metadata.end_line,
                'type': chunk.metadata.chunk_type,
                'name': chunk.metadata.name,
                'signature': chunk.metadata.signature,
                'docstring': chunk.metadata.docstring,
                'parent_class': chunk.metadata.parent_class,
                'imports': chunk.metadata.imports or []
            }
        }
        legacy_chunks.append(legacy_chunk)
    
    return legacy_chunks

if __name__ == '__main__':
    chunker = AdvancedCodeChunker()
    test_file = os.path.join(os.path.dirname(__file__), 'unixcoder.py')
    
    print(f"Testing advanced chunker on {test_file}")
    file_chunks = chunker.chunk_file(test_file)
    
    print(f"Found {len(file_chunks)} chunks:")
    for i, chunk in enumerate(file_chunks):
        print(f"--- Chunk {i+1} (Lines {chunk.metadata.start_line}-{chunk.metadata.end_line}) ---")
        print(f"  Type: {chunk.metadata.chunk_type}")
        print(f"  Complexity: {chunk.metadata.complexity_score:.2f}")
        print(f"  Hash: {chunk.metadata.semantic_hash}")
        
        if chunk.metadata.name:
            print(f"  Name: {chunk.metadata.name}")
        if chunk.metadata.signature:
            print(f"  Signature: {chunk.metadata.signature}")
        if chunk.metadata.docstring:
            print(f"  Docstring: {chunk.metadata.docstring[:70]}...")
        if chunk.metadata.parent_class:
            print(f"  Parent Class: {chunk.metadata.parent_class}")
        
        print(f"  Token Count: {chunker._count_tokens(chunk.code)}")
        print("  Code Preview:")
        print(chunk.code[:200] + "..." if len(chunk.code) > 200 else chunk.code)
        print("-" * 60) 