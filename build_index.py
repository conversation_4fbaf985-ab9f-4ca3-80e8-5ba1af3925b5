import os
import sys
from tqdm import tqdm

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import get_config
from logger import get_logger, performance_monitor
from model_manager import get_model_manager
from index_manager import get_index_manager
from embedding_generator import get_batch_generator, EmbeddingTask
from code_chunker import AdvancedCodeChunker

logger = get_logger(__name__)

def initialize_components():
    """初始化所有组件"""
    config = get_config()
    
    logger.info("Initializing components...")
    
    # 初始化模型管理器和预加载模型
    model_manager = get_model_manager()
    model_manager.preload_models()
    
    # 初始化其他组件
    index_manager = get_index_manager()
    batch_generator = get_batch_generator()
    chunker = AdvancedCodeChunker()
    
    logger.info("All components initialized successfully")
    
    return config, model_manager, index_manager, batch_generator, chunker

@performance_monitor("find_code_files")
def find_code_files(target_directories, extensions, exclude_patterns=None):
    """从指定目录列表中递归查找指定扩展名的代码文件"""
    if exclude_patterns is None:
        exclude_patterns = ['__pycache__', '*.pyc', '*.pyo', '*.egg-info', '.git', '.venv', 'node_modules', 'build', 'dist']

    code_files = []

    # 遍历所有目标目录
    for target_dir in target_directories:
        if not os.path.exists(target_dir):
            logger.warning(f"Target directory does not exist: {target_dir}")
            continue

        logger.info(f"Scanning directory: {target_dir}")

        for root, _, files in os.walk(target_dir):
            # 检查是否应该忽略这个目录
            if any(pattern in root for pattern in exclude_patterns):
                continue

            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)

                    # 检查文件大小，跳过过大的文件
                    try:
                        if os.path.getsize(file_path) > 1024 * 1024:  # 1MB
                            logger.warning(f"Skipping large file: {file_path}")
                            continue
                    except OSError:
                        continue

                    code_files.append(file_path)
    
    return code_files

@performance_monitor("batch_embeddings")
def generate_embeddings_batch(chunks, batch_generator):
    """批量生成嵌入"""
    logger.info(f"Generating embeddings for {len(chunks)} chunks...")
    
    # 准备嵌入任务
    tasks = []
    for i, chunk in enumerate(chunks):
        # 构建输入文本
        input_text = f"File: {chunk.metadata.file_path}\n"
        if chunk.metadata.parent_class:
            input_text += f"Class: {chunk.metadata.parent_class}\n"
        if chunk.metadata.name:
            input_text += f"Name: {chunk.metadata.name}\n"
        if chunk.metadata.signature:
            input_text += f"Signature: {chunk.metadata.signature}\n"
        if chunk.metadata.docstring:
            input_text += f"Docstring: {chunk.metadata.docstring}\n"
        input_text += f"Code:\n{chunk.code}"
        
        task = EmbeddingTask(
            id=str(i),
            text=input_text,
            metadata={
                'chunk_index': i,
                'original_text': input_text
            }
        )
        tasks.append(task)
    
    # 批量生成嵌入
    results = batch_generator.generate_embeddings(tasks)
    
    # 提取嵌入向量
    embeddings = []
    for result in sorted(results, key=lambda r: int(r.id)):
        embeddings.append(result.embedding)
    
    return embeddings

@performance_monitor("full_indexing")
def main():
    """主函数：构建并保存索引"""
    try:
        # 初始化组件
        config, model_manager, index_manager, batch_generator, chunker = initialize_components()
        
        logger.info("=== Starting Code Indexing ===")
        
        # Step 1: 查找代码文件
        logger.info("Step 1: Finding code files...")
        logger.info(f"Project root: {config.project.root_dir}")
        logger.info(f"Target directories: {config.project.target_directories}")
        logger.info(f"File extensions: {config.project.file_extensions}")
        logger.info(f"Exclude patterns: {config.project.exclude_patterns}")

        code_files = find_code_files(
            config.project.target_directories,
            config.project.file_extensions,
            config.project.exclude_patterns
        )
        logger.info(f"Found {len(code_files)} code files to index")
        
        if not code_files:
            logger.error("No code files found to index!")
            return
        
        # Step 2: 分块代码文件
        logger.info("Step 2: Chunking code files...")
        all_chunks = []
        
        for file_path in tqdm(code_files, desc="Chunking files"):
            try:
                chunks = chunker.chunk_file(file_path)
                all_chunks.extend(chunks)
                logger.debug(f"Generated {len(chunks)} chunks from {file_path}")
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {e}")
                continue
        
        if not all_chunks:
            logger.error("No chunks were generated!")
            return
        
        logger.info(f"Generated {len(all_chunks)} total chunks")
        
        # Step 3: 生成嵌入
        logger.info("Step 3: Generating embeddings...")
        embeddings = generate_embeddings_batch(all_chunks, batch_generator)
        
        if not embeddings:
            logger.error("No embeddings were generated!")
            return
        
        # 转换为numpy数组
        import numpy as np
        embeddings_np = np.vstack(embeddings)
        logger.info(f"Generated embeddings shape: {embeddings_np.shape}")
        
        # Step 4: 构建索引
        logger.info("Step 4: Building FAISS index...")
        index = index_manager.create_index(embeddings_np)
        
        # Step 5: 准备元数据（转换为旧格式以兼容现有代码）
        logger.info("Step 5: Preparing metadata...")
        metadata = []
        for chunk in all_chunks:
            chunk_metadata = {
                'file_path': chunk.metadata.file_path,
                'start_line': chunk.metadata.start_line,
                'end_line': chunk.metadata.end_line,
                'type': chunk.metadata.chunk_type,
                'name': chunk.metadata.name,
                'signature': chunk.metadata.signature,
                'docstring': chunk.metadata.docstring,
                'parent_class': chunk.metadata.parent_class,
                'imports': chunk.metadata.imports or [],
                'complexity_score': chunk.metadata.complexity_score,
                'semantic_hash': chunk.metadata.semantic_hash
            }
            metadata.append({
                'code': chunk.code,
                'metadata': chunk_metadata
            })
        
        # 设置元数据到索引管理器
        index_manager.metadata = metadata
        
        # Step 6: 保存索引和元数据
        logger.info("Step 6: Saving index and metadata...")
        index_manager.save_index()
        
        # Step 7: 优化索引
        logger.info("Step 7: Optimizing index...")
        index_manager.optimize_index()
        
        # 生成索引信息报告
        index_info = index_manager.get_index_info()
        logger.info(f"Index build completed successfully!")
        logger.info(f"Index info: {index_info}")
        
        # 清理缓存
        batch_generator.clear_cache()
        
        logger.info("=== Indexing Complete ===")
        
    except Exception as e:
        logger.error(f"Indexing failed: {e}")
        raise

if __name__ == "__main__":
    main() 