"""
动态批处理处理器

自适应批处理大小，根据系统资源和性能动态调整批处理参数。
"""

import time
import psutil
import torch
import threading
from typing import List, Any, Callable, Optional, Dict, Tuple, Generic, TypeVar, Union
from dataclasses import dataclass
from queue import Queue, Empty
from collections import deque
import statistics

from logger import get_logger, performance_monitor
from config_manager import get_config
from exceptions import BaseServiceError, error_handler, handle_errors
from resource_manager import get_resource_manager, gpu_memory_context

logger = get_logger(__name__)


@dataclass
class BatchMetrics:
    """批处理性能指标"""
    batch_size: int
    processing_time: float
    throughput: float  # items/second
    memory_usage_mb: float
    gpu_memory_usage_mb: float
    cpu_percent: float
    success: bool
    error_message: Optional[str] = None


@dataclass
class BatchConfig:
    """批处理配置"""
    min_batch_size: int = 1
    max_batch_size: int = 128
    initial_batch_size: int = 32
    target_latency_ms: float = 1000.0
    memory_threshold_mb: float = 8192
    cpu_threshold_percent: float = 80.0
    adaptation_rate: float = 0.1
    metrics_window: int = 10


class PerformanceTracker:
    """性能跟踪器"""
    
    def __init__(self, window_size: int = 10):
        self.window_size = window_size
        self.metrics_history: deque = deque(maxlen=window_size)
        self._lock = threading.Lock()
    
    def add_metrics(self, metrics: BatchMetrics):
        """添加性能指标"""
        with self._lock:
            self.metrics_history.append(metrics)
    
    def get_average_throughput(self) -> float:
        """获取平均吞吐量"""
        with self._lock:
            if not self.metrics_history:
                return 0.0
            
            successful_metrics = [m for m in self.metrics_history if m.success]
            if not successful_metrics:
                return 0.0
            
            return statistics.mean(m.throughput for m in successful_metrics)
    
    def get_average_latency(self) -> float:
        """获取平均延迟"""
        with self._lock:
            if not self.metrics_history:
                return 0.0
            
            successful_metrics = [m for m in self.metrics_history if m.success]
            if not successful_metrics:
                return 0.0
            
            return statistics.mean(m.processing_time * 1000 for m in successful_metrics)  # ms
    
    def get_memory_usage_trend(self) -> float:
        """获取内存使用趋势"""
        with self._lock:
            if len(self.metrics_history) < 2:
                return 0.0
            
            recent_memory = [m.memory_usage_mb for m in list(self.metrics_history)[-3:]]
            if len(recent_memory) < 2:
                return 0.0
            
            # 计算内存使用的变化率
            return (recent_memory[-1] - recent_memory[0]) / len(recent_memory)
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        with self._lock:
            if not self.metrics_history:
                return 1.0
            
            successful_count = sum(1 for m in self.metrics_history if m.success)
            return successful_count / len(self.metrics_history)


class BatchSizeOptimizer:
    """批处理大小优化器"""
    
    def __init__(self, config: BatchConfig):
        self.config = config
        self.current_batch_size = config.initial_batch_size
        self.performance_tracker = PerformanceTracker(config.metrics_window)
        self._lock = threading.Lock()
    
    def update_batch_size(self, metrics: BatchMetrics) -> int:
        """根据性能指标更新批处理大小"""
        with self._lock:
            self.performance_tracker.add_metrics(metrics)
            
            if not metrics.success:
                # 处理失败，减小批处理大小
                self.current_batch_size = max(
                    self.config.min_batch_size,
                    int(self.current_batch_size * 0.8)
                )
                logger.warning(f"Batch failed, reducing batch size to {self.current_batch_size}")
                return self.current_batch_size
            
            # 获取性能指标
            avg_latency = self.performance_tracker.get_average_latency()
            avg_throughput = self.performance_tracker.get_average_throughput()
            memory_trend = self.performance_tracker.get_memory_usage_trend()
            success_rate = self.performance_tracker.get_success_rate()
            
            # 决策逻辑
            new_batch_size = self._optimize_batch_size(
                metrics, avg_latency, avg_throughput, memory_trend, success_rate
            )
            
            # 应用自适应调整
            if new_batch_size != self.current_batch_size:
                change = int((new_batch_size - self.current_batch_size) * self.config.adaptation_rate)
                self.current_batch_size = max(
                    self.config.min_batch_size,
                    min(self.config.max_batch_size, self.current_batch_size + change)
                )
                
                logger.debug(f"Adjusted batch size to {self.current_batch_size}")
            
            return self.current_batch_size
    
    def _optimize_batch_size(
        self,
        current_metrics: BatchMetrics,
        avg_latency: float,
        avg_throughput: float,
        memory_trend: float,
        success_rate: float
    ) -> int:
        """优化批处理大小的核心逻辑"""
        current_size = self.current_batch_size
        target_size = current_size
        
        # 1. 延迟约束
        if avg_latency > self.config.target_latency_ms:
            target_size = int(current_size * 0.9)
            logger.debug(f"High latency ({avg_latency:.1f}ms), suggesting smaller batch")
        elif avg_latency < self.config.target_latency_ms * 0.7:
            target_size = int(current_size * 1.1)
            logger.debug(f"Low latency ({avg_latency:.1f}ms), suggesting larger batch")
        
        # 2. 内存约束
        if current_metrics.memory_usage_mb > self.config.memory_threshold_mb:
            target_size = int(current_size * 0.8)
            logger.debug(f"High memory usage ({current_metrics.memory_usage_mb:.1f}MB)")
        elif memory_trend > 100:  # 内存增长过快
            target_size = int(current_size * 0.9)
            logger.debug("Memory usage trending up")
        
        # 3. CPU约束
        if current_metrics.cpu_percent > self.config.cpu_threshold_percent:
            target_size = int(current_size * 0.9)
            logger.debug(f"High CPU usage ({current_metrics.cpu_percent:.1f}%)")
        
        # 4. 成功率约束
        if success_rate < 0.95:
            target_size = int(current_size * 0.8)
            logger.debug(f"Low success rate ({success_rate:.2f})")
        
        # 5. GPU内存约束（如果使用GPU）
        if torch.cuda.is_available() and current_metrics.gpu_memory_usage_mb > 0:
            gpu_memory_free = torch.cuda.get_device_properties(0).total_memory / 1024 / 1024
            gpu_memory_used = current_metrics.gpu_memory_usage_mb
            gpu_utilization = gpu_memory_used / gpu_memory_free
            
            if gpu_utilization > 0.8:
                target_size = int(current_size * 0.8)
                logger.debug(f"High GPU memory usage ({gpu_utilization:.1f})")
        
        return max(self.config.min_batch_size, min(self.config.max_batch_size, target_size))
    
    def get_current_batch_size(self) -> int:
        """获取当前批处理大小"""
        return self.current_batch_size
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            'current_batch_size': self.current_batch_size,
            'average_throughput': self.performance_tracker.get_average_throughput(),
            'average_latency_ms': self.performance_tracker.get_average_latency(),
            'success_rate': self.performance_tracker.get_success_rate(),
            'memory_trend_mb': self.performance_tracker.get_memory_usage_trend()
        }


class DynamicBatchProcessor:
    """动态批处理处理器"""
    
    def __init__(
        self,
        processor_func: Callable[[List[Any]], List[Any]],
        config: Optional[BatchConfig] = None
    ):
        self.processor_func = processor_func
        self.config = config or BatchConfig()
        self.optimizer = BatchSizeOptimizer(self.config)
        self.resource_manager = get_resource_manager()
        self._lock = threading.Lock()
    
    @handle_errors(BaseServiceError, logger=logger)
    def process_batch(self, items: List[Any]) -> List[Any]:
        """处理批次数据"""
        if not items:
            return []
        
        batch_size = self.optimizer.get_current_batch_size()
        results = []
        
        # 分批处理
        for i in range(0, len(items), batch_size):
            batch_items = items[i:i + batch_size]
            batch_results = self._process_single_batch(batch_items)
            results.extend(batch_results)
        
        return results
    
    def _process_single_batch(self, batch_items: List[Any]) -> List[Any]:
        """处理单个批次"""
        start_time = time.time()
        initial_memory = self._get_memory_usage()
        initial_gpu_memory = self._get_gpu_memory_usage()
        
        success = True
        error_message = None
        results = []
        
        try:
            with gpu_memory_context():
                results = self.processor_func(batch_items)
            
        except Exception as e:
            success = False
            error_message = str(e)
            logger.error(f"Batch processing failed: {e}")
            # 降级处理：逐个处理
            results = self._fallback_processing(batch_items)
        
        # 收集性能指标
        processing_time = time.time() - start_time
        final_memory = self._get_memory_usage()
        final_gpu_memory = self._get_gpu_memory_usage()
        
        metrics = BatchMetrics(
            batch_size=len(batch_items),
            processing_time=processing_time,
            throughput=len(batch_items) / processing_time if processing_time > 0 else 0,
            memory_usage_mb=final_memory,
            gpu_memory_usage_mb=final_gpu_memory,
            cpu_percent=psutil.cpu_percent(),
            success=success,
            error_message=error_message
        )
        
        # 更新优化器
        self.optimizer.update_batch_size(metrics)
        
        return results
    
    def _fallback_processing(self, batch_items: List[Any]) -> List[Any]:
        """降级处理：逐个处理"""
        results = []
        for item in batch_items:
            try:
                result = self.processor_func([item])
                results.extend(result)
            except Exception as e:
                logger.error(f"Individual item processing failed: {e}")
                # 根据需要决定是否跳过或添加默认值
                results.append(None)
        
        return results
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量(MB)"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_gpu_memory_usage(self) -> float:
        """获取GPU内存使用量(MB)"""
        try:
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / 1024 / 1024
            return 0.0
        except Exception:
            return 0.0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        return self.optimizer.get_performance_summary()
    
    def reset_optimizer(self):
        """重置优化器"""
        with self._lock:
            self.optimizer = BatchSizeOptimizer(self.config)


class StreamingBatchProcessor:
    """流式批处理处理器"""
    
    def __init__(
        self,
        processor_func: Callable[[List[Any]], List[Any]],
        config: Optional[BatchConfig] = None,
        queue_size: int = 1000
    ):
        self.processor_func = processor_func
        self.config = config or BatchConfig()
        self.dynamic_processor = DynamicBatchProcessor(processor_func, config)
        
        self.input_queue = Queue(maxsize=queue_size)
        self.output_queue = Queue()
        
        self._processing = False
        self._worker_thread = None
        self._result_handlers = []
    
    def start_processing(self):
        """开始处理"""
        if not self._processing:
            self._processing = True
            self._worker_thread = threading.Thread(
                target=self._processing_loop,
                daemon=True
            )
            self._worker_thread.start()
            logger.info("Streaming batch processor started")
    
    def stop_processing(self):
        """停止处理"""
        self._processing = False
        if self._worker_thread:
            self._worker_thread.join(timeout=5)
        logger.info("Streaming batch processor stopped")
    
    def add_item(self, item: Any, timeout: float = 1.0) -> bool:
        """添加处理项"""
        try:
            self.input_queue.put(item, timeout=timeout)
            return True
        except Exception:
            return False
    
    def get_result(self, timeout: float = 1.0) -> Optional[Any]:
        """获取处理结果"""
        try:
            return self.output_queue.get(timeout=timeout)
        except Empty:
            return None
    
    def add_result_handler(self, handler: Callable[[Any], None]):
        """添加结果处理器"""
        self._result_handlers.append(handler)
    
    def _processing_loop(self):
        """处理循环"""
        batch_items = []
        last_batch_time = time.time()
        
        while self._processing:
            try:
                # 收集批次
                current_time = time.time()
                batch_timeout = 0.1  # 100ms 批次超时
                
                # 尝试获取项目
                try:
                    item = self.input_queue.get(timeout=0.01)
                    batch_items.append(item)
                except Empty:
                    pass
                
                # 决定是否处理批次
                should_process = (
                    len(batch_items) >= self.dynamic_processor.optimizer.get_current_batch_size() or
                    (batch_items and current_time - last_batch_time > batch_timeout) or
                    not self._processing
                )
                
                if should_process and batch_items:
                    # 处理批次
                    results = self.dynamic_processor.process_batch(batch_items)
                    
                    # 输出结果
                    for result in results:
                        if result is not None:
                            self.output_queue.put(result)
                            
                            # 调用结果处理器
                            for handler in self._result_handlers:
                                try:
                                    handler(result)
                                except Exception as e:
                                    logger.error(f"Result handler error: {e}")
                    
                    # 重置批次
                    batch_items = []
                    last_batch_time = current_time
                
            except Exception as e:
                logger.error(f"Processing loop error: {e}")
                time.sleep(0.1)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.dynamic_processor.get_stats()
        stats.update({
            'input_queue_size': self.input_queue.qsize(),
            'output_queue_size': self.output_queue.qsize(),
            'processing': self._processing
        })
        return stats


def create_batch_processor(
    processor_func: Callable[[List[Any]], List[Any]],
    min_batch_size: int = 1,
    max_batch_size: int = 128,
    target_latency_ms: float = 1000.0,
    streaming: bool = False
) -> Union[DynamicBatchProcessor, StreamingBatchProcessor]:
    """创建批处理处理器的工厂函数"""
    config = BatchConfig(
        min_batch_size=min_batch_size,
        max_batch_size=max_batch_size,
        target_latency_ms=target_latency_ms
    )
    
    if streaming:
        return StreamingBatchProcessor(processor_func, config)
    else:
        return DynamicBatchProcessor(processor_func, config)