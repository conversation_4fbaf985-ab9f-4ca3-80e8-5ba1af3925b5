{"name": "tree-sitter-python", "version": "0.23.6", "description": "Python grammar for tree-sitter", "repository": "https://github.com/tree-sitter/tree-sitter-python", "license": "MIT", "author": {"name": "<PERSON>", "email": "maxbrun<PERSON><PERSON>@gmail.com"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "main": "bindings/node", "types": "bindings/node", "keywords": ["incremental", "parsing", "tree-sitter", "python"], "files": ["grammar.js", "tree-sitter.json", "binding.gyp", "prebuilds/**", "bindings/node/*", "queries/*", "src/**", "*.wasm"], "dependencies": {"node-addon-api": "^8.3.0", "node-gyp-build": "^4.8.4"}, "devDependencies": {"eslint": "^9.17.0", "eslint-config-treesitter": "^1.0.2", "prebuildify": "^6.0.1", "tree-sitter-cli": "^0.24.5"}, "peerDependencies": {"tree-sitter": "^0.22.1"}, "peerDependenciesMeta": {"tree-sitter": {"optional": true}}, "scripts": {"install": "node-gyp-build", "lint": "eslint grammar.js", "prestart": "tree-sitter build --wasm", "start": "tree-sitter playground", "test": "node --test bindings/node/*_test.js"}}