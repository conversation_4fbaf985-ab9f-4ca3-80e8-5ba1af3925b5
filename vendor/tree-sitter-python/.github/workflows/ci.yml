name: CI

on:
  push:
    branches: [master]
    paths:
      - grammar.js
      - src/**
      - test/**
      - bindings/**
      - binding.gyp
  pull_request:
    paths:
      - grammar.js
      - src/**
      - test/**
      - bindings/**
      - binding.gyp

concurrency:
  group: ${{github.workflow}}-${{github.ref}}
  cancel-in-progress: true

jobs:
  test:
    name: Test parser
    runs-on: ${{matrix.os}}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-14]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up tree-sitter
        uses: tree-sitter/setup-action/cli@v2
      - name: Set up examples
        run: |-
          git clone https://github.com/numpy/numpy examples/numpy --single-branch --depth=1 --filter=blob:none
          git clone https://github.com/django/django examples/django --single-branch --depth=1 --filter=blob:none
          git clone https://github.com/pallets/flask examples/flask --single-branch --depth=1 --filter=blob:none
          git clone https://github.com/python/cpython examples/cpython --single-branch --depth=1 --filter=blob:none
      - name: Run tests
        uses: tree-sitter/parser-test-action@v2
        with:
          test-rust: true
          test-node: true
          test-python: true
          test-go: true
          test-swift: true
      - name: Parse examples
        uses: tree-sitter/parse-action@v4
        with:
          files: |
            examples/**/*.py
            !examples/cpython/Lib/test/test_annotationlib.py
            !examples/cpython/Lib/test/test_type_params.py
            !examples/cpython/Lib/test/test_compile.py
            !examples/cpython/Tools/build/generate_re_casefix.py
            !examples/cpython/Lib/test/test_annotationlib.py
            !examples/cpython/Lib/test/test_type_params.py
          invalid-files: |
            examples/cpython/Lib/test/tokenizedata/badsyntax_3131.py
