[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tree-sitter-python"
description = "Python grammar for tree-sitter"
version = "0.23.6"
keywords = ["incremental", "parsing", "tree-sitter", "python"]
classifiers = [
  "Intended Audience :: Developers",
  "License :: OSI Approved :: MIT License",
  "Topic :: Software Development :: Compilers",
  "Topic :: Text Processing :: Linguistic",
  "Typing :: Typed",
]
authors = [
  { name = "<PERSON>", email = "<EMAIL>" },
  { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
requires-python = ">=3.9"
license.text = "MIT"
readme = "README.md"

[project.urls]
Homepage = "https://github.com/tree-sitter/tree-sitter-python"

[project.optional-dependencies]
core = ["tree-sitter~=0.22"]

[tool.cibuildwheel]
build = "cp39-*"
build-frontend = "build"
