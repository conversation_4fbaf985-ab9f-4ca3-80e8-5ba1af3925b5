[{"type": "_compound_statement", "named": true, "subtypes": [{"type": "class_definition", "named": true}, {"type": "decorated_definition", "named": true}, {"type": "for_statement", "named": true}, {"type": "function_definition", "named": true}, {"type": "if_statement", "named": true}, {"type": "match_statement", "named": true}, {"type": "try_statement", "named": true}, {"type": "while_statement", "named": true}, {"type": "with_statement", "named": true}]}, {"type": "_simple_statement", "named": true, "subtypes": [{"type": "assert_statement", "named": true}, {"type": "break_statement", "named": true}, {"type": "continue_statement", "named": true}, {"type": "delete_statement", "named": true}, {"type": "exec_statement", "named": true}, {"type": "expression_statement", "named": true}, {"type": "future_import_statement", "named": true}, {"type": "global_statement", "named": true}, {"type": "import_from_statement", "named": true}, {"type": "import_statement", "named": true}, {"type": "nonlocal_statement", "named": true}, {"type": "pass_statement", "named": true}, {"type": "print_statement", "named": true}, {"type": "raise_statement", "named": true}, {"type": "return_statement", "named": true}, {"type": "type_alias_statement", "named": true}]}, {"type": "expression", "named": true, "subtypes": [{"type": "as_pattern", "named": true}, {"type": "boolean_operator", "named": true}, {"type": "comparison_operator", "named": true}, {"type": "conditional_expression", "named": true}, {"type": "lambda", "named": true}, {"type": "named_expression", "named": true}, {"type": "not_operator", "named": true}, {"type": "primary_expression", "named": true}]}, {"type": "parameter", "named": true, "subtypes": [{"type": "default_parameter", "named": true}, {"type": "dictionary_splat_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "keyword_separator", "named": true}, {"type": "list_splat_pattern", "named": true}, {"type": "positional_separator", "named": true}, {"type": "tuple_pattern", "named": true}, {"type": "typed_default_parameter", "named": true}, {"type": "typed_parameter", "named": true}]}, {"type": "pattern", "named": true, "subtypes": [{"type": "attribute", "named": true}, {"type": "identifier", "named": true}, {"type": "list_pattern", "named": true}, {"type": "list_splat_pattern", "named": true}, {"type": "subscript", "named": true}, {"type": "tuple_pattern", "named": true}]}, {"type": "primary_expression", "named": true, "subtypes": [{"type": "attribute", "named": true}, {"type": "await", "named": true}, {"type": "binary_operator", "named": true}, {"type": "call", "named": true}, {"type": "concatenated_string", "named": true}, {"type": "dictionary", "named": true}, {"type": "dictionary_comprehension", "named": true}, {"type": "ellipsis", "named": true}, {"type": "false", "named": true}, {"type": "float", "named": true}, {"type": "generator_expression", "named": true}, {"type": "identifier", "named": true}, {"type": "integer", "named": true}, {"type": "list", "named": true}, {"type": "list_comprehension", "named": true}, {"type": "list_splat", "named": true}, {"type": "none", "named": true}, {"type": "parenthesized_expression", "named": true}, {"type": "set", "named": true}, {"type": "set_comprehension", "named": true}, {"type": "string", "named": true}, {"type": "subscript", "named": true}, {"type": "true", "named": true}, {"type": "tuple", "named": true}, {"type": "unary_operator", "named": true}]}, {"type": "aliased_import", "named": true, "fields": {"alias": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "dotted_name", "named": true}]}}}, {"type": "argument_list", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "dictionary_splat", "named": true}, {"type": "expression", "named": true}, {"type": "keyword_argument", "named": true}, {"type": "list_splat", "named": true}, {"type": "parenthesized_expression", "named": true}]}}, {"type": "as_pattern", "named": true, "fields": {"alias": {"multiple": false, "required": false, "types": [{"type": "as_pattern_target", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "case_pattern", "named": true}, {"type": "expression", "named": true}, {"type": "identifier", "named": true}]}}, {"type": "assert_statement", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "assignment", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "pattern", "named": true}, {"type": "pattern_list", "named": true}]}, "right": {"multiple": false, "required": false, "types": [{"type": "assignment", "named": true}, {"type": "augmented_assignment", "named": true}, {"type": "expression", "named": true}, {"type": "expression_list", "named": true}, {"type": "pattern_list", "named": true}, {"type": "yield", "named": true}]}, "type": {"multiple": false, "required": false, "types": [{"type": "type", "named": true}]}}}, {"type": "attribute", "named": true, "fields": {"attribute": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "object": {"multiple": false, "required": true, "types": [{"type": "primary_expression", "named": true}]}}}, {"type": "augmented_assignment", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "pattern", "named": true}, {"type": "pattern_list", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "%=", "named": false}, {"type": "&=", "named": false}, {"type": "**=", "named": false}, {"type": "*=", "named": false}, {"type": "+=", "named": false}, {"type": "-=", "named": false}, {"type": "//=", "named": false}, {"type": "/=", "named": false}, {"type": "<<=", "named": false}, {"type": ">>=", "named": false}, {"type": "@=", "named": false}, {"type": "^=", "named": false}, {"type": "|=", "named": false}]}, "right": {"multiple": false, "required": true, "types": [{"type": "assignment", "named": true}, {"type": "augmented_assignment", "named": true}, {"type": "expression", "named": true}, {"type": "expression_list", "named": true}, {"type": "pattern_list", "named": true}, {"type": "yield", "named": true}]}}}, {"type": "await", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "primary_expression", "named": true}]}}, {"type": "binary_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "primary_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "%", "named": false}, {"type": "&", "named": false}, {"type": "*", "named": false}, {"type": "**", "named": false}, {"type": "+", "named": false}, {"type": "-", "named": false}, {"type": "/", "named": false}, {"type": "//", "named": false}, {"type": "<<", "named": false}, {"type": ">>", "named": false}, {"type": "@", "named": false}, {"type": "^", "named": false}, {"type": "|", "named": false}]}, "right": {"multiple": false, "required": true, "types": [{"type": "primary_expression", "named": true}]}}}, {"type": "block", "named": true, "fields": {"alternative": {"multiple": true, "required": false, "types": [{"type": "case_clause", "named": true}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "_compound_statement", "named": true}, {"type": "_simple_statement", "named": true}]}}, {"type": "boolean_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "and", "named": false}, {"type": "or", "named": false}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "break_statement", "named": true, "fields": {}}, {"type": "call", "named": true, "fields": {"arguments": {"multiple": false, "required": true, "types": [{"type": "argument_list", "named": true}, {"type": "generator_expression", "named": true}]}, "function": {"multiple": false, "required": true, "types": [{"type": "primary_expression", "named": true}]}}}, {"type": "case_clause", "named": true, "fields": {"consequence": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}, "guard": {"multiple": false, "required": false, "types": [{"type": "if_clause", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "case_pattern", "named": true}]}}, {"type": "case_pattern", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "as_pattern", "named": true}, {"type": "class_pattern", "named": true}, {"type": "complex_pattern", "named": true}, {"type": "concatenated_string", "named": true}, {"type": "dict_pattern", "named": true}, {"type": "dotted_name", "named": true}, {"type": "false", "named": true}, {"type": "float", "named": true}, {"type": "integer", "named": true}, {"type": "keyword_pattern", "named": true}, {"type": "list_pattern", "named": true}, {"type": "none", "named": true}, {"type": "splat_pattern", "named": true}, {"type": "string", "named": true}, {"type": "true", "named": true}, {"type": "tuple_pattern", "named": true}, {"type": "union_pattern", "named": true}]}}, {"type": "chevron", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "class_definition", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "superclasses": {"multiple": false, "required": false, "types": [{"type": "argument_list", "named": true}]}, "type_parameters": {"multiple": false, "required": false, "types": [{"type": "type_parameter", "named": true}]}}}, {"type": "class_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "case_pattern", "named": true}, {"type": "dotted_name", "named": true}]}}, {"type": "comparison_operator", "named": true, "fields": {"operators": {"multiple": true, "required": true, "types": [{"type": "!=", "named": false}, {"type": "<", "named": false}, {"type": "<=", "named": false}, {"type": "<>", "named": false}, {"type": "==", "named": false}, {"type": ">", "named": false}, {"type": ">=", "named": false}, {"type": "in", "named": false}, {"type": "is", "named": false}, {"type": "is not", "named": false}, {"type": "not in", "named": false}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "primary_expression", "named": true}]}}, {"type": "complex_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "float", "named": true}, {"type": "integer", "named": true}]}}, {"type": "concatenated_string", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "string", "named": true}]}}, {"type": "conditional_expression", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "constrained_type", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "type", "named": true}]}}, {"type": "continue_statement", "named": true, "fields": {}}, {"type": "decorated_definition", "named": true, "fields": {"definition": {"multiple": false, "required": true, "types": [{"type": "class_definition", "named": true}, {"type": "function_definition", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "decorator", "named": true}]}}, {"type": "decorator", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "default_parameter", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "tuple_pattern", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "delete_statement", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "expression_list", "named": true}]}}, {"type": "dict_pattern", "named": true, "fields": {"key": {"multiple": true, "required": false, "types": [{"type": "-", "named": false}, {"type": "_", "named": false}, {"type": "class_pattern", "named": true}, {"type": "complex_pattern", "named": true}, {"type": "concatenated_string", "named": true}, {"type": "dict_pattern", "named": true}, {"type": "dotted_name", "named": true}, {"type": "false", "named": true}, {"type": "float", "named": true}, {"type": "integer", "named": true}, {"type": "list_pattern", "named": true}, {"type": "none", "named": true}, {"type": "splat_pattern", "named": true}, {"type": "string", "named": true}, {"type": "true", "named": true}, {"type": "tuple_pattern", "named": true}, {"type": "union_pattern", "named": true}]}, "value": {"multiple": true, "required": false, "types": [{"type": "case_pattern", "named": true}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "splat_pattern", "named": true}]}}, {"type": "dictionary", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "dictionary_splat", "named": true}, {"type": "pair", "named": true}]}}, {"type": "dictionary_comprehension", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "pair", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "for_in_clause", "named": true}, {"type": "if_clause", "named": true}]}}, {"type": "dictionary_splat", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "dictionary_splat_pattern", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "attribute", "named": true}, {"type": "identifier", "named": true}, {"type": "subscript", "named": true}]}}, {"type": "dotted_name", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "elif_clause", "named": true, "fields": {"condition": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "consequence": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}}}, {"type": "else_clause", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}}}, {"type": "except_clause", "named": true, "fields": {"alias": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}, "value": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}}, {"type": "except_group_clause", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "block", "named": true}, {"type": "expression", "named": true}]}}, {"type": "exec_statement", "named": true, "fields": {"code": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "string", "named": true}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "expression", "named": true}]}}, {"type": "expression_list", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "expression_statement", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "assignment", "named": true}, {"type": "augmented_assignment", "named": true}, {"type": "expression", "named": true}, {"type": "yield", "named": true}]}}, {"type": "finally_clause", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}}, {"type": "for_in_clause", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "pattern", "named": true}, {"type": "pattern_list", "named": true}]}, "right": {"multiple": true, "required": true, "types": [{"type": ",", "named": false}, {"type": "expression", "named": true}]}}}, {"type": "for_statement", "named": true, "fields": {"alternative": {"multiple": false, "required": false, "types": [{"type": "else_clause", "named": true}]}, "body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}, "left": {"multiple": false, "required": true, "types": [{"type": "pattern", "named": true}, {"type": "pattern_list", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "expression_list", "named": true}]}}}, {"type": "format_expression", "named": true, "fields": {"expression": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "expression_list", "named": true}, {"type": "pattern_list", "named": true}, {"type": "yield", "named": true}]}, "format_specifier": {"multiple": false, "required": false, "types": [{"type": "format_specifier", "named": true}]}, "type_conversion": {"multiple": false, "required": false, "types": [{"type": "type_conversion", "named": true}]}}}, {"type": "format_specifier", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "format_expression", "named": true}]}}, {"type": "function_definition", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "parameters": {"multiple": false, "required": true, "types": [{"type": "parameters", "named": true}]}, "return_type": {"multiple": false, "required": false, "types": [{"type": "type", "named": true}]}, "type_parameters": {"multiple": false, "required": false, "types": [{"type": "type_parameter", "named": true}]}}}, {"type": "future_import_statement", "named": true, "fields": {"name": {"multiple": true, "required": true, "types": [{"type": "aliased_import", "named": true}, {"type": "dotted_name", "named": true}]}}}, {"type": "generator_expression", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "for_in_clause", "named": true}, {"type": "if_clause", "named": true}]}}, {"type": "generic_type", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "type_parameter", "named": true}]}}, {"type": "global_statement", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "if_clause", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "if_statement", "named": true, "fields": {"alternative": {"multiple": true, "required": false, "types": [{"type": "elif_clause", "named": true}, {"type": "else_clause", "named": true}]}, "condition": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "consequence": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}}}, {"type": "import_from_statement", "named": true, "fields": {"module_name": {"multiple": false, "required": true, "types": [{"type": "dotted_name", "named": true}, {"type": "relative_import", "named": true}]}, "name": {"multiple": true, "required": false, "types": [{"type": "aliased_import", "named": true}, {"type": "dotted_name", "named": true}]}}, "children": {"multiple": false, "required": false, "types": [{"type": "wildcard_import", "named": true}]}}, {"type": "import_prefix", "named": true, "fields": {}}, {"type": "import_statement", "named": true, "fields": {"name": {"multiple": true, "required": true, "types": [{"type": "aliased_import", "named": true}, {"type": "dotted_name", "named": true}]}}}, {"type": "interpolation", "named": true, "fields": {"expression": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "expression_list", "named": true}, {"type": "pattern_list", "named": true}, {"type": "yield", "named": true}]}, "format_specifier": {"multiple": false, "required": false, "types": [{"type": "format_specifier", "named": true}]}, "type_conversion": {"multiple": false, "required": false, "types": [{"type": "type_conversion", "named": true}]}}}, {"type": "is not", "named": false, "fields": {}}, {"type": "keyword_argument", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "keyword_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "class_pattern", "named": true}, {"type": "complex_pattern", "named": true}, {"type": "concatenated_string", "named": true}, {"type": "dict_pattern", "named": true}, {"type": "dotted_name", "named": true}, {"type": "false", "named": true}, {"type": "float", "named": true}, {"type": "identifier", "named": true}, {"type": "integer", "named": true}, {"type": "list_pattern", "named": true}, {"type": "none", "named": true}, {"type": "splat_pattern", "named": true}, {"type": "string", "named": true}, {"type": "true", "named": true}, {"type": "tuple_pattern", "named": true}, {"type": "union_pattern", "named": true}]}}, {"type": "keyword_separator", "named": true, "fields": {}}, {"type": "lambda", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "parameters": {"multiple": false, "required": false, "types": [{"type": "lambda_parameters", "named": true}]}}}, {"type": "lambda_parameters", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "parameter", "named": true}]}}, {"type": "list", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "expression", "named": true}, {"type": "list_splat", "named": true}, {"type": "parenthesized_list_splat", "named": true}, {"type": "yield", "named": true}]}}, {"type": "list_comprehension", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "for_in_clause", "named": true}, {"type": "if_clause", "named": true}]}}, {"type": "list_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "case_pattern", "named": true}, {"type": "pattern", "named": true}]}}, {"type": "list_splat", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "attribute", "named": true}, {"type": "expression", "named": true}, {"type": "identifier", "named": true}, {"type": "subscript", "named": true}]}}, {"type": "list_splat_pattern", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "attribute", "named": true}, {"type": "identifier", "named": true}, {"type": "subscript", "named": true}]}}, {"type": "match_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}, "subject": {"multiple": true, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "member_type", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "type", "named": true}]}}, {"type": "module", "named": true, "root": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "_compound_statement", "named": true}, {"type": "_simple_statement", "named": true}]}}, {"type": "named_expression", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "nonlocal_statement", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "not in", "named": false, "fields": {}}, {"type": "not_operator", "named": true, "fields": {"argument": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "pair", "named": true, "fields": {"key": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "parameters", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "parameter", "named": true}]}}, {"type": "parenthesized_expression", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "list_splat", "named": true}, {"type": "parenthesized_expression", "named": true}, {"type": "yield", "named": true}]}}, {"type": "parenthesized_list_splat", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "list_splat", "named": true}, {"type": "parenthesized_expression", "named": true}]}}, {"type": "pass_statement", "named": true, "fields": {}}, {"type": "pattern_list", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "pattern", "named": true}]}}, {"type": "positional_separator", "named": true, "fields": {}}, {"type": "print_statement", "named": true, "fields": {"argument": {"multiple": true, "required": false, "types": [{"type": "expression", "named": true}]}}, "children": {"multiple": false, "required": false, "types": [{"type": "chevron", "named": true}]}}, {"type": "raise_statement", "named": true, "fields": {"cause": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}}, "children": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}, {"type": "expression_list", "named": true}]}}, {"type": "relative_import", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "dotted_name", "named": true}, {"type": "import_prefix", "named": true}]}}, {"type": "return_statement", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}, {"type": "expression_list", "named": true}]}}, {"type": "set", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "expression", "named": true}, {"type": "list_splat", "named": true}, {"type": "parenthesized_list_splat", "named": true}, {"type": "yield", "named": true}]}}, {"type": "set_comprehension", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "for_in_clause", "named": true}, {"type": "if_clause", "named": true}]}}, {"type": "slice", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "expression", "named": true}]}}, {"type": "splat_pattern", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}}, {"type": "splat_type", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "string", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "interpolation", "named": true}, {"type": "string_content", "named": true}, {"type": "string_end", "named": true}, {"type": "string_start", "named": true}]}}, {"type": "string_content", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "escape_interpolation", "named": true}, {"type": "escape_sequence", "named": true}]}}, {"type": "subscript", "named": true, "fields": {"subscript": {"multiple": true, "required": true, "types": [{"type": "expression", "named": true}, {"type": "slice", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "primary_expression", "named": true}]}}}, {"type": "try_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "else_clause", "named": true}, {"type": "except_clause", "named": true}, {"type": "except_group_clause", "named": true}, {"type": "finally_clause", "named": true}]}}, {"type": "tuple", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "expression", "named": true}, {"type": "list_splat", "named": true}, {"type": "parenthesized_list_splat", "named": true}, {"type": "yield", "named": true}]}}, {"type": "tuple_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "case_pattern", "named": true}, {"type": "pattern", "named": true}]}}, {"type": "type", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "constrained_type", "named": true}, {"type": "expression", "named": true}, {"type": "generic_type", "named": true}, {"type": "member_type", "named": true}, {"type": "splat_type", "named": true}, {"type": "union_type", "named": true}]}}, {"type": "type_alias_statement", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "type", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "type", "named": true}]}}}, {"type": "type_parameter", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "type", "named": true}]}}, {"type": "typed_default_parameter", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "type": {"multiple": false, "required": true, "types": [{"type": "type", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "typed_parameter", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "type", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "dictionary_splat_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "list_splat_pattern", "named": true}]}}, {"type": "unary_operator", "named": true, "fields": {"argument": {"multiple": false, "required": true, "types": [{"type": "primary_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "+", "named": false}, {"type": "-", "named": false}, {"type": "~", "named": false}]}}}, {"type": "union_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "class_pattern", "named": true}, {"type": "complex_pattern", "named": true}, {"type": "concatenated_string", "named": true}, {"type": "dict_pattern", "named": true}, {"type": "dotted_name", "named": true}, {"type": "false", "named": true}, {"type": "float", "named": true}, {"type": "integer", "named": true}, {"type": "list_pattern", "named": true}, {"type": "none", "named": true}, {"type": "splat_pattern", "named": true}, {"type": "string", "named": true}, {"type": "true", "named": true}, {"type": "tuple_pattern", "named": true}, {"type": "union_pattern", "named": true}]}}, {"type": "union_type", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "type", "named": true}]}}, {"type": "while_statement", "named": true, "fields": {"alternative": {"multiple": false, "required": false, "types": [{"type": "else_clause", "named": true}]}, "body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}, "condition": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "wildcard_import", "named": true, "fields": {}}, {"type": "with_clause", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "with_item", "named": true}]}}, {"type": "with_item", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "with_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "block", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "with_clause", "named": true}]}}, {"type": "yield", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}, {"type": "expression_list", "named": true}]}}, {"type": "!=", "named": false}, {"type": "%", "named": false}, {"type": "%=", "named": false}, {"type": "&", "named": false}, {"type": "&=", "named": false}, {"type": "(", "named": false}, {"type": ")", "named": false}, {"type": "*", "named": false}, {"type": "**", "named": false}, {"type": "**=", "named": false}, {"type": "*=", "named": false}, {"type": "+", "named": false}, {"type": "+=", "named": false}, {"type": ",", "named": false}, {"type": "-", "named": false}, {"type": "-=", "named": false}, {"type": "->", "named": false}, {"type": ".", "named": false}, {"type": "/", "named": false}, {"type": "//", "named": false}, {"type": "//=", "named": false}, {"type": "/=", "named": false}, {"type": ":", "named": false}, {"type": ":=", "named": false}, {"type": ";", "named": false}, {"type": "<", "named": false}, {"type": "<<", "named": false}, {"type": "<<=", "named": false}, {"type": "<=", "named": false}, {"type": "<>", "named": false}, {"type": "=", "named": false}, {"type": "==", "named": false}, {"type": ">", "named": false}, {"type": ">=", "named": false}, {"type": ">>", "named": false}, {"type": ">>=", "named": false}, {"type": "@", "named": false}, {"type": "@=", "named": false}, {"type": "[", "named": false}, {"type": "\\", "named": false}, {"type": "]", "named": false}, {"type": "^", "named": false}, {"type": "^=", "named": false}, {"type": "_", "named": false}, {"type": "__future__", "named": false}, {"type": "and", "named": false}, {"type": "as", "named": false}, {"type": "assert", "named": false}, {"type": "async", "named": false}, {"type": "await", "named": false}, {"type": "break", "named": false}, {"type": "case", "named": false}, {"type": "class", "named": false}, {"type": "comment", "named": true}, {"type": "continue", "named": false}, {"type": "def", "named": false}, {"type": "del", "named": false}, {"type": "elif", "named": false}, {"type": "ellipsis", "named": true}, {"type": "else", "named": false}, {"type": "escape_interpolation", "named": true}, {"type": "escape_sequence", "named": true}, {"type": "except", "named": false}, {"type": "except*", "named": false}, {"type": "exec", "named": false}, {"type": "false", "named": true}, {"type": "finally", "named": false}, {"type": "float", "named": true}, {"type": "for", "named": false}, {"type": "from", "named": false}, {"type": "global", "named": false}, {"type": "identifier", "named": true}, {"type": "if", "named": false}, {"type": "import", "named": false}, {"type": "in", "named": false}, {"type": "integer", "named": true}, {"type": "is", "named": false}, {"type": "lambda", "named": false}, {"type": "line_continuation", "named": true}, {"type": "match", "named": false}, {"type": "none", "named": true}, {"type": "nonlocal", "named": false}, {"type": "not", "named": false}, {"type": "or", "named": false}, {"type": "pass", "named": false}, {"type": "print", "named": false}, {"type": "raise", "named": false}, {"type": "return", "named": false}, {"type": "string_end", "named": true}, {"type": "string_start", "named": true}, {"type": "true", "named": true}, {"type": "try", "named": false}, {"type": "type", "named": false}, {"type": "type_conversion", "named": true}, {"type": "while", "named": false}, {"type": "with", "named": false}, {"type": "yield", "named": false}, {"type": "{", "named": false}, {"type": "|", "named": false}, {"type": "|=", "named": false}, {"type": "}", "named": false}, {"type": "~", "named": false}]