"""
高级Prompt构建器
基于代码理解、依赖关系和语义分析构建高质量的LLM提示词
"""

import re
import ast
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from config import get_config
from logger import get_logger, performance_monitor
from dependency_analyzer import get_dependency_analyzer

logger = get_logger(__name__)

@dataclass
class CodeAnnotation:
    """代码注解"""
    type: str  # 'algorithm', 'pattern', 'complexity', 'purpose'
    content: str
    confidence: float

@dataclass
class AnalyzedChunk:
    """分析后的代码块"""
    original_chunk: Dict[str, Any]
    annotations: List[CodeAnnotation]
    complexity_analysis: Dict[str, Any]
    related_entities: List[str]
    call_chain: List[str]
    design_patterns: List[str]

class CodePatternDetector:
    """代码模式检测器"""
    
    def __init__(self):
        # 设计模式特征
        self.pattern_signatures = {
            'singleton': [
                r'class\s+\w+.*:\s*\n.*_instance\s*=\s*None',
                r'def\s+__new__\s*\(',
                r'if\s+.*_instance\s+is\s+None'
            ],
            'factory': [
                r'def\s+create_\w+\(',
                r'def\s+\w*factory\w*\(',
                r'if\s+.*==.*:\s*return\s+\w+\('
            ],
            'observer': [
                r'def\s+add_observer\(',
                r'def\s+notify\w*\(',
                r'observers?\s*=\s*\[\]'
            ],
            'decorator': [
                r'@\w+',
                r'def\s+\w+\(.*func.*\):',
                r'def\s+wrapper\('
            ],
            'builder': [
                r'def\s+set_\w+\(',
                r'def\s+build\(\)',
                r'return\s+self'
            ],
            'strategy': [
                r'def\s+execute\(',
                r'strategy\s*=',
                r'algorithm\s*='
            ]
        }
        
        # 算法模式
        self.algorithm_patterns = {
            'sorting': [
                r'\.sort\(',
                r'sorted\(',
                r'key\s*=',
                r'reverse\s*='
            ],
            'searching': [
                r'\.find\(',
                r'\.search\(',
                r'in\s+\w+',
                r'binary.search'
            ],
            'recursion': [
                r'def\s+(?P<func>\w+)\(.*\):.*\n.*(?P=func)\(',
                r'return\s+.*\w+\(.*\w+.*\)'
            ],
            'dynamic_programming': [
                r'memo\w*\s*=',
                r'cache\s*=',
                r'dp\s*=',
                r'@lru_cache'
            ],
            'graph_traversal': [
                r'queue\s*=',
                r'stack\s*=',
                r'visited\s*=',
                r'dfs\|bfs'
            ]
        }
    
    def detect_patterns(self, code: str) -> List[str]:
        """检测代码中的设计模式和算法模式"""
        detected_patterns = []
        
        # 检测设计模式
        for pattern_name, signatures in self.pattern_signatures.items():
            match_count = sum(1 for sig in signatures if re.search(sig, code, re.MULTILINE | re.IGNORECASE))
            if match_count >= len(signatures) * 0.6:  # 60%匹配度
                detected_patterns.append(f"design_pattern:{pattern_name}")
        
        # 检测算法模式
        for algorithm_name, signatures in self.algorithm_patterns.items():
            match_count = sum(1 for sig in signatures if re.search(sig, code, re.MULTILINE | re.IGNORECASE))
            if match_count >= 1:  # 只需要一个匹配即可
                detected_patterns.append(f"algorithm:{algorithm_name}")
        
        return detected_patterns

class CodeComplexityAnalyzer:
    """代码复杂度分析器"""
    
    def analyze_complexity(self, code: str) -> Dict[str, Any]:
        """分析代码复杂度"""
        try:
            # 清理代码，去除前导空白
            code = code.strip()
            if not code:
                return self._get_default_complexity()

            # 检查代码是否完整（简单启发式检查）
            if not self._is_code_complete(code):
                logger.debug(f"Code appears incomplete, using default complexity")
                return self._get_default_complexity()

            tree = ast.parse(code)
            analyzer = ComplexityVisitor()
            analyzer.visit(tree)

            return {
                'cyclomatic_complexity': analyzer.cyclomatic_complexity,
                'cognitive_complexity': analyzer.cognitive_complexity,
                'nesting_depth': analyzer.max_nesting_depth,
                'lines_of_code': len([line for line in code.split('\n') if line.strip()]),
                'complexity_level': self._get_complexity_level(analyzer.cyclomatic_complexity)
            }
        except SyntaxError as e:
            logger.debug(f"Syntax error in code complexity analysis: {e}")
            return self._get_default_complexity()
        except Exception as e:
            logger.warning(f"Failed to analyze code complexity: {e}")
            return self._get_default_complexity()

    def _is_code_complete(self, code: str) -> bool:
        """检查代码是否完整"""
        lines = code.split('\n')

        # 检查是否有未闭合的结构
        open_brackets = code.count('(') - code.count(')')
        open_braces = code.count('{') - code.count('}')
        open_squares = code.count('[') - code.count(']')

        if open_brackets != 0 or open_braces != 0 or open_squares != 0:
            return False

        # 检查是否以不完整的语句结尾
        last_line = lines[-1].strip() if lines else ""
        incomplete_endings = [':', ',', '\\', 'and', 'or', '+', '-', '*', '/', '=']

        if any(last_line.endswith(ending) for ending in incomplete_endings):
            return False

        return True

    def _get_default_complexity(self) -> Dict[str, Any]:
        """获取默认复杂度信息"""
        return {
            'cyclomatic_complexity': 1,
            'cognitive_complexity': 1,
            'nesting_depth': 1,
            'lines_of_code': 1,
            'complexity_level': 'simple'
        }
    
    def _get_complexity_level(self, complexity: int) -> str:
        """获取复杂度等级"""
        if complexity <= 5:
            return 'simple'
        elif complexity <= 10:
            return 'moderate'
        elif complexity <= 20:
            return 'complex'
        else:
            return 'very_complex'

class ComplexityVisitor(ast.NodeVisitor):
    """AST复杂度访问器"""
    
    def __init__(self):
        self.cyclomatic_complexity = 1  # 基础复杂度
        self.cognitive_complexity = 0
        self.nesting_depth = 0
        self.max_nesting_depth = 0
        self.current_depth = 0
    
    def visit_If(self, node):
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.current_depth
        self._enter_block()
        self.generic_visit(node)
        self._exit_block()
    
    def visit_While(self, node):
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.current_depth
        self._enter_block()
        self.generic_visit(node)
        self._exit_block()
    
    def visit_For(self, node):
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.current_depth
        self._enter_block()
        self.generic_visit(node)
        self._exit_block()
    
    def visit_ExceptHandler(self, node):
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.current_depth
        self._enter_block()
        self.generic_visit(node)
        self._exit_block()
    
    def visit_With(self, node):
        self.cognitive_complexity += 1 + self.current_depth
        self._enter_block()
        self.generic_visit(node)
        self._exit_block()
    
    def _enter_block(self):
        self.current_depth += 1
        self.max_nesting_depth = max(self.max_nesting_depth, self.current_depth)
    
    def _exit_block(self):
        self.current_depth -= 1

class AdvancedPromptBuilder:
    """高级Prompt构建器"""
    
    def __init__(self):
        self.config = get_config()
        self.dependency_analyzer = get_dependency_analyzer()
        self.pattern_detector = CodePatternDetector()
        self.complexity_analyzer = CodeComplexityAnalyzer()
        
        # Prompt模板
        self.templates = {
            'analysis': self._get_analysis_template(),
            'implementation': self._get_implementation_template(),
            'explanation': self._get_explanation_template(),
            'debugging': self._get_debugging_template()
        }
    
    @performance_monitor("build_advanced_prompt")
    def build_prompt(self, query: str, chunks: List[Dict[str, Any]], 
                    query_type: str = "analysis") -> str:
        """构建高级Prompt"""
        logger.debug(f"Building advanced prompt for query type: {query_type}")
        
        # 1. 分析代码块
        analyzed_chunks = self._analyze_chunks(chunks)
        
        # 2. 构建上下文信息
        context_info = self._build_context_info(analyzed_chunks)
        
        # 3. 检测查询意图
        query_intent = self._detect_query_intent(query)
        
        # 4. 选择合适的模板
        template = self.templates.get(query_intent.get('type', 'analysis'), self.templates['analysis'])
        
        # 5. 构建最终Prompt
        final_prompt = self._generate_prompt(
            template, query, analyzed_chunks, context_info, query_intent
        )
        
        logger.debug(f"Generated prompt with {len(final_prompt)} characters")
        return final_prompt
    
    def _analyze_chunks(self, chunks: List[Dict[str, Any]]) -> List[AnalyzedChunk]:
        """分析代码块"""
        analyzed_chunks = []

        for chunk in chunks:
            # 处理不同的数据格式
            if 'chunk' in chunk:
                # 新格式：来自混合检索器
                chunk_data = chunk['chunk']
            else:
                # 旧格式：直接是chunk数据
                chunk_data = chunk

            code = chunk_data.get('code', '')
            metadata = chunk_data.get('metadata', {})
            
            # 代码注解
            annotations = self._generate_annotations(code, metadata)
            
            # 复杂度分析
            complexity_analysis = self.complexity_analyzer.analyze_complexity(code)
            
            # 相关实体
            related_entities = self._get_related_entities(chunk)
            
            # 调用链
            call_chain = self._build_call_chain(chunk_data, related_entities)

            # 设计模式
            design_patterns = self.pattern_detector.detect_patterns(code)

            analyzed_chunk = AnalyzedChunk(
                original_chunk=chunk_data,
                annotations=annotations,
                complexity_analysis=complexity_analysis,
                related_entities=related_entities,
                call_chain=call_chain,
                design_patterns=design_patterns
            )
            
            analyzed_chunks.append(analyzed_chunk)
        
        return analyzed_chunks
    
    def _generate_annotations(self, code: str, metadata: Dict[str, Any]) -> List[CodeAnnotation]:
        """生成代码注解"""
        annotations = []
        
        # 目的注解
        purpose = self._infer_code_purpose(code, metadata)
        if purpose:
            annotations.append(CodeAnnotation(
                type='purpose',
                content=purpose,
                confidence=0.8
            ))
        
        # 算法注解
        algorithm_info = self._identify_algorithm(code)
        if algorithm_info:
            annotations.append(CodeAnnotation(
                type='algorithm',
                content=algorithm_info,
                confidence=0.7
            ))
        
        # 复杂度注解
        complexity_note = self._generate_complexity_note(code)
        if complexity_note:
            annotations.append(CodeAnnotation(
                type='complexity',
                content=complexity_note,
                confidence=0.9
            ))
        
        return annotations
    
    def _infer_code_purpose(self, code: str, metadata: Dict[str, Any]) -> Optional[str]:
        """推断代码目的"""
        func_name = metadata.get('name', '')
        docstring = metadata.get('docstring', '')
        
        # 基于函数名推断
        if func_name:
            if any(word in func_name.lower() for word in ['search', 'find', 'lookup']):
                return "搜索和查找功能"
            elif any(word in func_name.lower() for word in ['sort', 'order', 'arrange']):
                return "排序和整理功能"
            elif any(word in func_name.lower() for word in ['parse', 'analyze', 'process']):
                return "数据解析和处理功能"
            elif any(word in func_name.lower() for word in ['validate', 'check', 'verify']):
                return "数据验证和检查功能"
            elif any(word in func_name.lower() for word in ['create', 'build', 'generate']):
                return "对象创建和构建功能"
        
        # 基于文档字符串推断
        if docstring:
            return f"根据文档说明：{docstring[:100]}..."
        
        # 基于代码模式推断
        if 'def __init__' in code:
            return "类初始化方法"
        elif 'return ' in code and 'def ' in code:
            return "计算和返回结果的函数"
        
        return None
    
    def _identify_algorithm(self, code: str) -> Optional[str]:
        """识别算法类型"""
        code_lower = code.lower()
        
        if 'quick' in code_lower and 'sort' in code_lower:
            return "快速排序算法"
        elif 'merge' in code_lower and 'sort' in code_lower:
            return "归并排序算法"
        elif 'binary' in code_lower and 'search' in code_lower:
            return "二分搜索算法"
        elif 'dfs' in code_lower or ('recursive' in code_lower and 'visit' in code_lower):
            return "深度优先搜索算法"
        elif 'bfs' in code_lower or 'queue' in code_lower:
            return "广度优先搜索算法"
        elif 'dp' in code_lower or 'memo' in code_lower:
            return "动态规划算法"
        elif 'hash' in code_lower and ('table' in code_lower or 'map' in code_lower):
            return "哈希表算法"
        
        return None
    
    def _generate_complexity_note(self, code: str) -> Optional[str]:
        """生成复杂度注释"""
        complexity_info = self.complexity_analyzer.analyze_complexity(code)
        level = complexity_info['complexity_level']
        cyclomatic = complexity_info['cyclomatic_complexity']
        
        if level == 'simple':
            return f"简单函数 (复杂度: {cyclomatic})"
        elif level == 'moderate':
            return f"中等复杂度函数 (复杂度: {cyclomatic})"
        elif level == 'complex':
            return f"复杂函数，包含多个分支 (复杂度: {cyclomatic})"
        else:
            return f"高度复杂函数，建议重构 (复杂度: {cyclomatic})"
    
    def _get_related_entities(self, chunk: Dict[str, Any]) -> List[str]:
        """获取相关实体"""
        metadata = chunk.get('metadata', {})
        file_path = metadata.get('file_path', '')
        func_name = metadata.get('name', '')
        
        if not func_name or not file_path:
            return []
        
        entity_id = f"{file_path}:{func_name}"
        related = self.dependency_analyzer.get_related_entities(entity_id, max_depth=2)
        
        return related[:5]  # 限制数量
    
    def _build_call_chain(self, chunk: Dict[str, Any], related_entities: List[str]) -> List[str]:
        """构建调用链"""
        # 这里可以基于依赖分析构建调用链
        # 简化实现，返回相关实体作为调用链
        return related_entities[:3]
    
    def _build_context_info(self, analyzed_chunks: List[AnalyzedChunk]) -> Dict[str, Any]:
        """构建上下文信息"""
        context = {
            'total_chunks': len(analyzed_chunks),
            'complexity_distribution': defaultdict(int),
            'pattern_summary': defaultdict(int),
            'file_distribution': defaultdict(int),
            'key_insights': []
        }
        
        for chunk in analyzed_chunks:
            # 复杂度分布
            level = chunk.complexity_analysis['complexity_level']
            context['complexity_distribution'][level] += 1
            
            # 模式分布
            for pattern in chunk.design_patterns:
                context['pattern_summary'][pattern] += 1
            
            # 文件分布
            file_path = chunk.original_chunk.get('metadata', {}).get('file_path', '')
            if file_path:
                file_name = file_path.split('/')[-1]
                context['file_distribution'][file_name] += 1
        
        # 生成关键洞察
        if context['pattern_summary']:
            most_common_pattern = max(context['pattern_summary'].items(), key=lambda x: x[1])
            context['key_insights'].append(f"主要使用了 {most_common_pattern[0]} 模式")
        
        complex_count = context['complexity_distribution']['complex'] + context['complexity_distribution']['very_complex']
        if complex_count > 0:
            context['key_insights'].append(f"包含 {complex_count} 个复杂函数")
        
        return context
    
    def _detect_query_intent(self, query: str) -> Dict[str, Any]:
        """检测查询意图"""
        query_lower = query.lower()
        
        # 实现类查询
        if any(word in query_lower for word in ['implement', '实现', 'create', '创建', 'build', '构建']):
            return {'type': 'implementation', 'confidence': 0.8}
        
        # 解释类查询
        elif any(word in query_lower for word in ['explain', '解释', 'understand', '理解', 'what', '什么']):
            return {'type': 'explanation', 'confidence': 0.8}
        
        # 调试类查询
        elif any(word in query_lower for word in ['debug', '调试', 'fix', '修复', 'error', '错误', 'bug']):
            return {'type': 'debugging', 'confidence': 0.8}
        
        # 默认分析类查询
        else:
            return {'type': 'analysis', 'confidence': 0.6}
    
    def _generate_prompt(self, template: str, query: str, 
                        analyzed_chunks: List[AnalyzedChunk],
                        context_info: Dict[str, Any],
                        query_intent: Dict[str, Any]) -> str:
        """生成最终Prompt"""
        # 构建代码上下文
        code_context = self._build_code_context(analyzed_chunks)
        
        # 构建分析洞察
        analysis_insights = self._build_analysis_insights(analyzed_chunks, context_info)
        
        # 填充模板
        prompt = template.format(
            query=query,
            code_context=code_context,
            analysis_insights=analysis_insights,
            context_summary=self._format_context_summary(context_info),
            query_type=query_intent.get('type', 'analysis')
        )
        
        return prompt
    
    def _build_code_context(self, analyzed_chunks: List[AnalyzedChunk]) -> str:
        """构建代码上下文"""
        context_parts = []
        
        for i, chunk in enumerate(analyzed_chunks):
            metadata = chunk.original_chunk.get('metadata', {})
            code = chunk.original_chunk.get('code', '')
            
            # 构建代码块标题
            title_parts = []
            if metadata.get('file_path'):
                title_parts.append(f"文件: {metadata['file_path']}")
            if metadata.get('name'):
                title_parts.append(f"函数/类: {metadata['name']}")
            
            title = " | ".join(title_parts) if title_parts else f"代码块 {i+1}"
            
            # 添加注解信息
            annotations_text = ""
            if chunk.annotations:
                annotations_text = "\n".join([f"// {ann.content}" for ann in chunk.annotations])
                annotations_text = f"\n{annotations_text}\n"
            
            # 添加复杂度信息
            complexity_info = ""
            if chunk.complexity_analysis:
                level = chunk.complexity_analysis['complexity_level']
                complexity_info = f"\n// 复杂度: {level}\n"
            
            # 添加设计模式信息
            pattern_info = ""
            if chunk.design_patterns:
                patterns = ", ".join(chunk.design_patterns)
                pattern_info = f"\n// 设计模式: {patterns}\n"
            
            context_part = f"""
=== {title} ==={annotations_text}{complexity_info}{pattern_info}
{code}

"""
            context_parts.append(context_part)
        
        return "\n".join(context_parts)
    
    def _build_analysis_insights(self, analyzed_chunks: List[AnalyzedChunk], 
                                context_info: Dict[str, Any]) -> str:
        """构建分析洞察"""
        insights = []
        
        # 代码质量分析
        complex_chunks = [c for c in analyzed_chunks 
                         if c.complexity_analysis['complexity_level'] in ['complex', 'very_complex']]
        if complex_chunks:
            insights.append(f"发现 {len(complex_chunks)} 个高复杂度代码块，可能需要重构优化")
        
        # 设计模式分析
        all_patterns = []
        for chunk in analyzed_chunks:
            all_patterns.extend(chunk.design_patterns)
        
        if all_patterns:
            pattern_count = len(set(all_patterns))
            insights.append(f"代码使用了 {pattern_count} 种设计模式，显示良好的架构设计")
        
        # 关联性分析
        total_relations = sum(len(chunk.related_entities) for chunk in analyzed_chunks)
        if total_relations > 0:
            insights.append(f"代码块间存在 {total_relations} 个依赖关系，形成完整的功能模块")
        
        return "\n".join([f"• {insight}" for insight in insights])
    
    def _format_context_summary(self, context_info: Dict[str, Any]) -> str:
        """格式化上下文摘要"""
        summary_parts = []
        
        # 文件分布
        file_count = len(context_info['file_distribution'])
        summary_parts.append(f"涉及 {file_count} 个文件")
        
        # 复杂度分布
        complexity_dist = context_info['complexity_distribution']
        if complexity_dist:
            complex_ratio = (complexity_dist['complex'] + complexity_dist['very_complex']) / sum(complexity_dist.values())
            if complex_ratio > 0.3:
                summary_parts.append("包含较多复杂逻辑")
            else:
                summary_parts.append("代码结构相对简单")
        
        # 关键洞察
        if context_info['key_insights']:
            summary_parts.extend(context_info['key_insights'])
        
        return " | ".join(summary_parts)
    
    def _get_analysis_template(self) -> str:
        """获取分析模板"""
        return """你是一位资深的代码分析专家。请基于提供的代码片段，深入分析并回答用户的问题。

=== 分析上下文 ===
{context_summary}

=== 代码洞察 ===
{analysis_insights}

=== 代码内容 ===
{code_context}

=== 用户问题 ===
{query}

=== 分析要求 ===
1. **结构化分析**：从代码结构、逻辑流程、设计模式等角度进行分析
2. **深度解读**：解释代码的核心思想、算法原理、实现策略
3. **关联分析**：分析代码块之间的依赖关系和协作模式
4. **质量评估**：评估代码质量、复杂度、可维护性
5. **实用建议**：提供改进建议、最佳实践、注意事项

请提供详细、准确、有洞察力的分析回答："""
    
    def _get_implementation_template(self) -> str:
        """获取实现模板"""
        return """你是一位经验丰富的软件工程师。基于提供的代码示例和用户需求，请协助实现相关功能。

=== 参考代码 ===
{code_context}

=== 代码洞察 ===
{analysis_insights}

=== 实现需求 ===
{query}

=== 实现指南 ===
1. **架构设计**：参考现有代码的架构模式和设计思路
2. **代码风格**：保持与现有代码一致的编程风格和规范
3. **功能实现**：提供完整、可运行的代码实现
4. **错误处理**：包含适当的异常处理和边界条件检查
5. **测试建议**：提供测试用例和验证方法
6. **文档说明**：添加必要的注释和文档字符串

请提供高质量的代码实现："""
    
    def _get_explanation_template(self) -> str:
        """获取解释模板"""
        return """你是一位优秀的编程导师。请用清晰易懂的方式解释代码的工作原理和实现细节。

=== 代码示例 ===
{code_context}

=== 代码分析 ===
{analysis_insights}

=== 解释请求 ===
{query}

=== 解释框架 ===
1. **概述**：简要说明代码的主要功能和用途
2. **逐步解析**：逐行或逐块解释关键代码的作用
3. **算法原理**：解释使用的算法、数据结构、设计模式
4. **执行流程**：描述代码的执行顺序和控制流
5. **关键概念**：解释重要的编程概念和技术要点
6. **实际应用**：说明代码在实际项目中的应用场景

请提供清晰、详细、易理解的解释："""
    
    def _get_debugging_template(self) -> str:
        """获取调试模板"""
        return """你是一位资深的调试专家。请分析代码中可能的问题并提供解决方案。

=== 问题代码 ===
{code_context}

=== 代码分析 ===
{analysis_insights}

=== 调试需求 ===
{query}

=== 调试方法 ===
1. **问题识别**：分析可能的错误类型、异常情况、逻辑缺陷
2. **根因分析**：深入分析问题的根本原因和触发条件
3. **解决方案**：提供具体的修复方法和改进代码
4. **预防措施**：建议如何避免类似问题再次发生
5. **测试验证**：提供测试方法验证修复效果
6. **最佳实践**：分享相关的调试技巧和编程最佳实践

请提供专业的调试分析和解决方案："""

# 全局实例
_prompt_builder = None

def get_prompt_builder() -> AdvancedPromptBuilder:
    """获取高级Prompt构建器"""
    global _prompt_builder
    if _prompt_builder is None:
        _prompt_builder = AdvancedPromptBuilder()
    return _prompt_builder