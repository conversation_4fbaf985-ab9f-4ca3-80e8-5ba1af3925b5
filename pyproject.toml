[project]
name = "unixcoder-rag"
version = "2.0.0"
description = "Advanced UniXcoder-based code intelligence system with RAG capabilities"
# readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT License"}
authors = [
    {name = "UniXcoder Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "torch>=2.7.1",
    "transformers>=4.52.4",
    "tree-sitter>=0.21.0",
    "tree-sitter-python @ file://${PROJECT_ROOT}/vendor/tree-sitter-python",
    "faiss-gpu>=1.7.2",
    "tqdm>=4.66.0",
    "openai>=1.3.0",
    "sentence-transformers>=4.1.0",
    "python-dotenv>=1.0.0",
    "psutil>=5.9.0",
    "numpy<2.0,>=1.24.0",
    "click>=8.0.0",
    "jieba>=0.42.1",
    "networkx>=3.0",
    "scikit-learn>=1.3.0",
    "streamlit>=1.47.1",
    "pydantic>=2.5.0",
    "plotly>=5.0.0",
    "pandas>=1.5.0",
]

[project.scripts]
unixcoder-build = "build_index:main"
unixcoder-ask = "ask:main"
unixcoder-benchmark = "benchmark:run_benchmark"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]
gpu = [
    "faiss-gpu>=1.7.2",
]


[tool.uv.sources]
torch = [
  { index = "pytorch-cu118", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
torchvision = [
  { index = "pytorch-cu118", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]

[[tool.uv.index]]
name="pytorch-cu118"
url = "https://download.pytorch.org/whl/cu118"
explicit = true

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
exclude = ["vendor*", "updatelogs*"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --cov=. --cov-report=term-missing"
testpaths = [
    "tests",
]

[tool.coverage.run]
source = ["."]
