#!/usr/bin/env python3
"""
气泡对话界面演示脚本
展示新的气泡样式对话界面和侧边栏引用功能
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def show_bubble_chat_features():
    """展示气泡对话界面功能特性"""
    print("💬 UniXcoder RAG 气泡对话界面")
    print("=" * 50)
    
    features = [
        ("💬 气泡对话", "类似微信的气泡样式对话界面"),
        ("📝 Markdown渲染", "AI回答支持完整的Markdown格式"),
        ("🎨 渐变设计", "美观的渐变色彩和阴影效果"),
        ("📱 响应式布局", "适配不同屏幕尺寸的响应式设计"),
        ("📚 侧边栏引用", "代码引用移至侧边栏，支持收起展开"),
        ("⚡ 实时更新", "流式输出实时更新对话内容"),
        ("🔄 打字指示器", "生成过程中显示动态打字效果"),
        ("🎯 智能上下文", "自动维护多轮对话上下文"),
        ("📊 引用统计", "侧边栏显示代码引用统计信息"),
        ("🔍 快速预览", "代码片段快速预览和详情查看")
    ]
    
    for feature, description in features:
        print(f"  {feature}: {description}")
    
    print()

def show_ui_improvements():
    """显示界面改进"""
    print("🎨 界面改进亮点")
    print("-" * 30)
    
    improvements = [
        "✨ 用户消息：蓝紫渐变气泡，右对齐",
        "🤖 AI消息：白色气泡带阴影，左对齐，支持Markdown",
        "👤 头像设计：圆形渐变头像，增强视觉识别",
        "📱 自适应滚动：对话自动滚动到最新消息",
        "🎭 动画效果：打字指示器和生成动画",
        "📋 侧边栏优化：紧凑的代码引用展示",
        "🔧 交互优化：更直观的按钮和控件布局",
        "🎨 色彩搭配：专业的渐变色彩方案"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    print()

def show_usage_scenarios():
    """显示使用场景"""
    print("🎯 使用场景示例")
    print("-" * 30)
    
    scenarios = [
        {
            "title": "📚 代码学习",
            "description": "学习代码实现原理，查看相关代码片段",
            "example": "用户: 代码分块是如何实现的？\nAI: 通过AST解析器分析代码结构..."
        },
        {
            "title": "🔍 问题诊断",
            "description": "诊断代码问题，获取解决方案",
            "example": "用户: 为什么检索结果为空？\nAI: 可能是索引文件问题，请检查..."
        },
        {
            "title": "⚡ 性能优化",
            "description": "了解性能优化方法和最佳实践",
            "example": "用户: 如何提高搜索性能？\nAI: 可以通过缓存、并行处理等方式..."
        },
        {
            "title": "🛠️ 功能扩展",
            "description": "探索新功能实现思路",
            "example": "用户: 如何添加新的检索方法？\nAI: 需要在hybrid_retriever.py中..."
        }
    ]
    
    for scenario in scenarios:
        print(f"  {scenario['title']}: {scenario['description']}")
        print(f"    示例: {scenario['example']}")
        print()

def check_system_status():
    """检查系统状态"""
    print("🔍 检查系统状态...")
    
    # 检查必要文件
    required_files = [
        "frontend/app.py",
        "frontend/api_service.py",
        "faiss_index.bin",
        "metadata.pkl"
    ]
    
    missing_files = []
    for file in required_files:
        if Path(file).exists():
            print(f"  ✅ {file}")
        else:
            missing_files.append(file)
            print(f"  ❌ {file}")
    
    if missing_files:
        print(f"\n⚠️  缺少文件: {missing_files}")
        if "faiss_index.bin" in missing_files or "metadata.pkl" in missing_files:
            print("请先运行: python build_index.py")
        return False
    
    print("\n✅ 系统文件检查通过")
    return True

def start_bubble_chat_demo():
    """启动气泡对话演示"""
    print("\n🚀 启动气泡对话界面...")
    
    try:
        # 检查端口
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8501))
        sock.close()
        
        if result == 0:
            print("  ⚠️  端口8501已被占用")
            print("  请手动停止其他应用")
            return False
        
        print("  📡 启动Streamlit服务器...")
        print("  🌐 URL: http://localhost:8501")
        print("  💬 体验全新的气泡对话界面")
        print("  ⏹️  按 Ctrl+C 停止服务")
        print()
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:8501')
                print("  🌐 已自动打开浏览器")
                print("  💡 提示: 点击'初始化系统'开始体验")
            except:
                print("  ⚠️  无法自动打开浏览器")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "frontend/app.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ])
        
        return True
        
    except KeyboardInterrupt:
        print("\n  ⏹️  演示已停止")
        return True
    except Exception as e:
        print(f"  ❌ 启动失败: {e}")
        return False

def show_interaction_tips():
    """显示交互提示"""
    print("💡 交互提示")
    print("-" * 20)
    
    tips = [
        "🚀 点击'初始化系统'按钮开始",
        "💭 在输入框中输入问题",
        "📝 可以点击示例问题快速开始",
        "👀 观察气泡样式的对话效果",
        "📚 查看侧边栏的代码引用",
        "🔄 尝试多轮连续对话",
        "⏹️ 可以随时停止AI生成",
        "🆕 使用'新对话'重新开始"
    ]
    
    for tip in tips:
        print(f"  {tip}")
    
    print()

def main():
    """主函数"""
    print("💬 UniXcoder RAG 气泡对话界面演示")
    print("=" * 50)
    
    # 显示功能特性
    show_bubble_chat_features()
    
    # 显示界面改进
    show_ui_improvements()
    
    # 显示使用场景
    show_usage_scenarios()
    
    # 显示交互提示
    show_interaction_tips()
    
    # 检查系统状态
    if not check_system_status():
        print("\n❌ 系统检查失败")
        return False
    
    # 询问是否启动演示
    print("❓ 是否启动气泡对话界面演示？(y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '']:
            return start_bubble_chat_demo()
        else:
            print("\n📝 手动启动命令:")
            print("   ./start_streamlit.sh")
            print("   或者: streamlit run frontend/app.py")
            
            print("\n🎨 界面特色:")
            print("   • 气泡样式对话界面")
            print("   • Markdown格式AI回答")
            print("   • 侧边栏代码引用")
            print("   • 流式输出动画效果")
            return True
    except KeyboardInterrupt:
        print("\n⏹️  演示已取消")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  演示已停止")
        sys.exit(0)
