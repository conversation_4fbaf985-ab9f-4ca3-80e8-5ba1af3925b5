"""
日志系统
提供统一的日志记录和性能监控功能
注意：此模块中的performance_monitor将被弃用，请使用performance_monitor模块
"""

import logging
import os
import sys
import time
import json
from typing import Dict, Any, Optional
from functools import wraps
from datetime import datetime
from pathlib import Path

from config import get_config

class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """开始计时"""
        self.start_times[operation] = time.time()
    
    def end_timer(self, operation: str) -> float:
        """结束计时并返回耗时"""
        if operation not in self.start_times:
            return 0.0
        
        elapsed = time.time() - self.start_times[operation]
        self.metrics[operation] = self.metrics.get(operation, [])
        self.metrics[operation].append(elapsed)
        
        return elapsed
    
    def get_stats(self, operation: str) -> Dict[str, float]:
        """获取操作的统计信息"""
        if operation not in self.metrics:
            return {}
        
        times = self.metrics[operation]
        return {
            'count': len(times),
            'total': sum(times),
            'avg': sum(times) / len(times),
            'min': min(times),
            'max': max(times)
        }
    
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """获取所有操作的统计信息"""
        return {op: self.get_stats(op) for op in self.metrics.keys()}

class CustomFormatter(logging.Formatter):
    """自定义日志格式化器"""
    
    def __init__(self):
        super().__init__()
        self.start_time = time.time()
    
    def format(self, record):
        # 添加相对时间戳
        record.relative_time = time.time() - self.start_time
        
        # 彩色输出（如果支持）
        if hasattr(record, 'levelname'):
            if record.levelname == 'ERROR':
                color = '\033[91m'  # 红色
            elif record.levelname == 'WARNING':
                color = '\033[93m'  # 黄色
            elif record.levelname == 'INFO':
                color = '\033[92m'  # 绿色
            elif record.levelname == 'DEBUG':
                color = '\033[96m'  # 青色
            else:
                color = '\033[0m'   # 默认
            
            end_color = '\033[0m'
            
            # 如果输出到终端，使用彩色
            if sys.stdout.isatty():
                record.levelname = f"{color}{record.levelname}{end_color}"
        
        # 格式化消息
        fmt = '[{asctime}] [{levelname}] [{name}] [{relative_time:.2f}s] {message}'
        formatter = logging.Formatter(fmt, style='{')
        return formatter.format(record)

def setup_logging():
    """设置日志系统"""
    config = get_config()
    
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.system.log_level.upper()))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(CustomFormatter())
    root_logger.addHandler(console_handler)
    
    # 文件处理器（如果配置了）
    if config.system.log_file:
        log_dir = Path(config.system.log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(config.system.log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '[{asctime}] [{levelname}] [{name}] {message}',
            style='{'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    return root_logger

def performance_monitor(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        op_name = operation_name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            logger = logging.getLogger(func.__module__)
            perf_logger = getattr(logger, '_perf_logger', None)
            
            if perf_logger is None:
                perf_logger = PerformanceLogger()
                logger._perf_logger = perf_logger
            
            perf_logger.start_timer(op_name)
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                elapsed = perf_logger.end_timer(op_name)
                
                if get_config().system.performance_monitoring:
                    logger.info(f"Operation {op_name} completed in {elapsed:.4f}s")
                
                return result
            except Exception as e:
                elapsed = perf_logger.end_timer(op_name)
                logger.error(f"Operation {op_name} failed after {elapsed:.4f}s: {e}")
                raise
        
        return wrapper
    return decorator

class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.perf_logger = PerformanceLogger()
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self._log(logging.ERROR, message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self._log(logging.DEBUG, message, **kwargs)
    
    def _log(self, level: int, message: str, **kwargs):
        """内部日志记录方法"""
        if kwargs:
            # 如果有额外的结构化数据，以JSON格式附加
            structured_data = json.dumps(kwargs, ensure_ascii=False)
            full_message = f"{message} | {structured_data}"
        else:
            full_message = message
        
        self.logger.log(level, full_message)
    
    def log_retrieval(self, query: str, results_count: int, elapsed_time: float, **kwargs):
        """记录检索操作"""
        self.info(
            f"Retrieval completed",
            query=query,
            results_count=results_count,
            elapsed_time=elapsed_time,
            **kwargs
        )
    
    def log_model_loading(self, model_name: str, elapsed_time: float, memory_mb: int):
        """记录模型加载"""
        self.info(
            f"Model loaded",
            model_name=model_name,
            elapsed_time=elapsed_time,
            memory_mb=memory_mb
        )
    
    def log_error_with_context(self, error: Exception, context: Dict[str, Any]):
        """记录带上下文的错误"""
        self.error(
            f"Error occurred: {str(error)}",
            error_type=type(error).__name__,
            **context
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self.perf_logger.get_all_stats()

# 全局日志器实例
_loggers = {}

def get_logger(name: str) -> StructuredLogger:
    """获取结构化日志器"""
    if name not in _loggers:
        _loggers[name] = StructuredLogger(name)
    return _loggers[name]

def get_performance_stats() -> Dict[str, Any]:
    """获取所有日志器的性能统计"""
    all_stats = {}
    for name, logger in _loggers.items():
        stats = logger.get_performance_stats()
        if stats:
            all_stats[name] = stats
    return all_stats

# 初始化日志系统
setup_logging()