#!/usr/bin/env python3
"""
调试搜索结果格式
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置离线环境
offline_env = {
    'TRANSFORMERS_OFFLINE': '1',
    'HF_HUB_OFFLINE': '1',
    'HF_HUB_DISABLE_TELEMETRY': '1',
    'CURL_CA_BUNDLE': '',
    'REQUESTS_CA_BUNDLE': '',
    'HF_DATASETS_OFFLINE': '1',
    'TOKENIZERS_PARALLELISM': 'false',
    'HF_HUB_DISABLE_PROGRESS_BARS': '1',
    'HF_HUB_DISABLE_SYMLINKS_WARNING': '1',
    'HF_HUB_DISABLE_EXPERIMENTAL_WARNING': '1',
}

for key, value in offline_env.items():
    os.environ[key] = value

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_search_results():
    """调试搜索结果"""
    print("🔍 Debugging search results...")
    
    try:
        from ask import CodeAssistant
        
        # 初始化助手
        print("Initializing CodeAssistant...")
        assistant = CodeAssistant()
        
        # 执行搜索
        query = "code chunking"
        print(f"Searching for: {query}")
        results = assistant.dynamic_search(query)
        
        print(f"Found {len(results)} results")
        
        # 详细分析前3个结果
        for i, result in enumerate(results[:3]):
            print(f"\n=== Result {i+1} ===")
            print(f"Type: {type(result)}")
            print(f"Keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            if isinstance(result, dict):
                for key, value in result.items():
                    if key == 'chunk':
                        print(f"  {key}: {type(value)}")
                        if isinstance(value, dict):
                            print(f"    chunk keys: {list(value.keys())}")
                            if 'code' in value:
                                code = value['code']
                                print(f"    code length: {len(code)}")
                                print(f"    code preview: {repr(code[:100])}...")
                            if 'metadata' in value:
                                meta = value['metadata']
                                print(f"    metadata: {type(meta)}")
                                if isinstance(meta, dict):
                                    print(f"    metadata keys: {list(meta.keys())}")
                                    print(f"    file_path: {meta.get('file_path', 'N/A')}")
                                    print(f"    start_line: {meta.get('start_line', 'N/A')}")
                    else:
                        print(f"  {key}: {repr(value) if len(str(value)) < 50 else f'{type(value)} (length: {len(str(value))})'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Starting search results debugging...")
    print("=" * 60)
    
    success = debug_search_results()
    
    print("=" * 60)
    print("🏁 Debugging completed")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
