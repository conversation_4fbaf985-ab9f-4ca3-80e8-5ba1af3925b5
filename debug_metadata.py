#!/usr/bin/env python3
"""
调试元数据结构
"""

import pickle
import json

def debug_metadata():
    """调试元数据结构"""
    print("🔍 Debugging metadata structure...")
    
    try:
        # 加载元数据
        with open('metadata.pkl', 'rb') as f:
            metadata = pickle.load(f)
        
        print(f"✅ Metadata loaded successfully")
        print(f"   Total chunks: {len(metadata)}")
        
        if metadata:
            # 检查第一个chunk的结构
            first_chunk = metadata[0]
            print(f"\n📋 First chunk structure:")
            print(f"   Type: {type(first_chunk)}")
            print(f"   Keys: {list(first_chunk.keys()) if isinstance(first_chunk, dict) else 'Not a dict'}")
            
            # 显示第一个chunk的详细信息
            if isinstance(first_chunk, dict):
                print(f"\n📄 First chunk details:")
                for key, value in first_chunk.items():
                    if key == 'code':
                        print(f"   {key}: {len(value)} characters")
                        print(f"   Code preview: {repr(value[:100])}...")
                    elif key == 'metadata':
                        print(f"   {key}: {type(value)}")
                        if isinstance(value, dict):
                            for meta_key, meta_value in value.items():
                                print(f"     {meta_key}: {repr(meta_value)}")
                    else:
                        print(f"   {key}: {repr(value)}")
            
            # 检查几个chunk的结构一致性
            print(f"\n🔄 Checking structure consistency...")
            for i in range(min(3, len(metadata))):
                chunk = metadata[i]
                if isinstance(chunk, dict):
                    has_code = 'code' in chunk and chunk['code']
                    has_metadata = 'metadata' in chunk and isinstance(chunk['metadata'], dict)
                    has_file_path = has_metadata and 'file_path' in chunk['metadata']
                    print(f"   Chunk {i}: code={has_code}, metadata={has_metadata}, file_path={has_file_path}")
                    
                    if has_metadata and has_file_path:
                        print(f"     File: {chunk['metadata']['file_path']}")
                        print(f"     Start line: {chunk['metadata'].get('start_line', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to load metadata: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_search_result():
    """调试搜索结果格式"""
    print("\n🔍 Debugging search result format...")
    
    try:
        # 模拟搜索过程
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from config import get_config
        from index_manager import get_index_manager
        from model_manager import get_model_manager
        from embedding_generator import get_batch_generator, EmbeddingTask
        
        # 初始化组件
        config = get_config()
        index_manager = get_index_manager()
        model_manager = get_model_manager()
        batch_generator = get_batch_generator()
        
        # 加载索引
        index_manager.load_index()
        
        # 生成查询嵌入
        task = EmbeddingTask(id="debug", text="code chunking", metadata={'query': True})
        results = batch_generator.generate_embeddings([task])
        query_embedding = results[0].embedding.reshape(1, -1)
        
        # 执行搜索
        similarities, indices = index_manager.search(query_embedding, 5)
        
        print(f"✅ Search executed successfully")
        print(f"   Similarities: {similarities[0][:3]}")
        print(f"   Indices: {indices[0][:3]}")
        
        # 检查元数据访问
        metadata = index_manager.metadata
        print(f"   Metadata available: {metadata is not None}")
        print(f"   Metadata length: {len(metadata) if metadata else 0}")
        
        if metadata and len(indices[0]) > 0:
            idx = indices[0][0]
            if idx < len(metadata):
                chunk = metadata[idx]
                print(f"\n📄 First result chunk:")
                print(f"   Type: {type(chunk)}")
                print(f"   Keys: {list(chunk.keys()) if isinstance(chunk, dict) else 'Not a dict'}")
                
                if isinstance(chunk, dict):
                    if 'code' in chunk:
                        print(f"   Code length: {len(chunk['code'])}")
                        print(f"   Code preview: {repr(chunk['code'][:50])}...")
                    if 'metadata' in chunk:
                        meta = chunk['metadata']
                        print(f"   File path: {meta.get('file_path', 'N/A')}")
                        print(f"   Start line: {meta.get('start_line', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Search debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Starting metadata debugging...")
    print("=" * 60)
    
    # 调试元数据
    metadata_ok = debug_metadata()
    
    if metadata_ok:
        # 调试搜索结果
        search_ok = debug_search_result()
    
    print("=" * 60)
    print("🏁 Debugging completed")

if __name__ == "__main__":
    main()
