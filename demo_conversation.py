#!/usr/bin/env python3
"""
对话系统演示脚本
展示新的多轮对话功能
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def show_conversation_features():
    """展示对话系统功能特性"""
    print("🎯 UniXcoder RAG 对话系统功能特性")
    print("=" * 50)
    
    features = [
        ("💬 多轮对话", "支持连续对话，自动维护上下文"),
        ("🔄 流式回答", "实时显示AI生成过程，支持中断"),
        ("📚 智能引用", "可折叠的代码片段展示和过滤"),
        ("💾 对话管理", "历史记录、新建对话、统计信息"),
        ("🎨 响应式布局", "优化的双栏布局设计"),
        ("🔍 高级过滤", "按相似度、文件、方法过滤引用"),
        ("📊 可视化分析", "引用统计和分布图表"),
        ("⚙️ 个性化设置", "可配置上下文长度和显示选项")
    ]
    
    for feature, description in features:
        print(f"  {feature}: {description}")
    
    print()

def show_usage_guide():
    """显示使用指南"""
    print("📖 使用指南")
    print("-" * 30)
    
    steps = [
        "1. 🚀 点击'初始化系统'按钮",
        "2. 💭 在输入框中输入问题",
        "3. 🚀 点击'发送'开始对话",
        "4. 👀 观察流式回答过程",
        "5. 📚 查看右侧代码引用",
        "6. 🔄 继续提问进行多轮对话",
        "7. ⚙️ 使用侧边栏管理对话"
    ]
    
    for step in steps:
        print(f"  {step}")
    
    print()

def show_example_conversations():
    """显示示例对话"""
    print("💡 示例对话流程")
    print("-" * 30)
    
    conversations = [
        {
            "title": "🔍 代码分析对话",
            "flow": [
                "用户: 代码分块算法是如何工作的？",
                "AI: 基于AST解析的代码分块...",
                "用户: 能否优化分块性能？",
                "AI: 可以通过缓存和并行处理..."
            ]
        },
        {
            "title": "🛠️ 技术实现对话",
            "flow": [
                "用户: 如何实现语义搜索？",
                "AI: 使用UniXcoder模型生成嵌入...",
                "用户: 混合检索是什么原理？",
                "AI: 结合语义和关键词搜索..."
            ]
        },
        {
            "title": "🚀 性能优化对话",
            "flow": [
                "用户: 系统性能如何优化？",
                "AI: 可以从索引、缓存等方面...",
                "用户: 具体的缓存策略有哪些？",
                "AI: LRU缓存、结果缓存等..."
            ]
        }
    ]
    
    for conv in conversations:
        print(f"  {conv['title']}:")
        for line in conv['flow']:
            print(f"    {line}")
        print()

def check_system_ready():
    """检查系统是否准备就绪"""
    print("🔍 检查系统状态...")
    
    # 检查索引文件
    index_files = ["faiss_index.bin", "metadata.pkl"]
    missing_files = []
    
    for file in index_files:
        if Path(file).exists():
            print(f"  ✅ {file}")
        else:
            missing_files.append(file)
            print(f"  ❌ {file}")
    
    # 检查前端文件
    frontend_files = [
        "frontend/app.py",
        "frontend/api_service.py",
        "frontend/reference_viewer.py"
    ]
    
    for file in frontend_files:
        if Path(file).exists():
            print(f"  ✅ {file}")
        else:
            missing_files.append(file)
            print(f"  ❌ {file}")
    
    if missing_files:
        print(f"\n⚠️  缺少文件: {missing_files}")
        return False
    
    print("\n✅ 系统文件检查通过")
    return True

def start_conversation_demo():
    """启动对话演示"""
    print("\n🚀 启动对话系统演示...")
    
    try:
        # 检查端口
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8501))
        sock.close()
        
        if result == 0:
            print("  ⚠️  端口8501已被占用")
            print("  请手动停止其他应用或使用不同端口")
            return False
        
        print("  📡 启动Streamlit服务器...")
        print("  🌐 URL: http://localhost:8501")
        print("  ⏹️  按 Ctrl+C 停止服务")
        print()
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:8501')
                print("  🌐 已自动打开浏览器")
            except:
                print("  ⚠️  无法自动打开浏览器")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run",
            "frontend/app.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ])
        
        return True
        
    except KeyboardInterrupt:
        print("\n  ⏹️  演示已停止")
        return True
    except Exception as e:
        print(f"  ❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 UniXcoder RAG 对话系统演示")
    print("=" * 50)
    
    # 显示功能特性
    show_conversation_features()
    
    # 显示使用指南
    show_usage_guide()
    
    # 显示示例对话
    show_example_conversations()
    
    # 检查系统状态
    if not check_system_ready():
        print("\n❌ 系统检查失败")
        print("请确保已运行: python build_index.py")
        return False
    
    # 询问是否启动演示
    print("❓ 是否启动对话系统演示？(y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes', '']:
            return start_conversation_demo()
        else:
            print("\n📝 手动启动命令:")
            print("   ./start_streamlit.sh")
            print("   或者: streamlit run frontend/app.py")
            
            print("\n💡 使用提示:")
            print("   1. 启动后访问 http://localhost:8501")
            print("   2. 点击'初始化系统'")
            print("   3. 开始多轮对话体验")
            return True
    except KeyboardInterrupt:
        print("\n⏹️  演示已取消")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  演示已停止")
        sys.exit(0)
