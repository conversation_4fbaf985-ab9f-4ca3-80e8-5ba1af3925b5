"""
索引管理器
支持多种FAISS索引类型和高效近似搜索
"""

import os
import pickle
import time
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from abc import ABC, abstractmethod
import faiss

from config import get_config
from logger import get_logger, performance_monitor

logger = get_logger(__name__)

class IndexStrategy(ABC):
    """索引策略抽象基类"""
    
    @abstractmethod
    def create_index(self, dimension: int, **kwargs) -> faiss.Index:
        """创建索引"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取策略名称"""
        pass
    
    @abstractmethod
    def get_search_params(self) -> Dict[str, Any]:
        """获取搜索参数"""
        pass

class FlatIPStrategy(IndexStrategy):
    """平坦内积索引策略（精确搜索）"""
    
    def create_index(self, dimension: int, **kwargs) -> faiss.Index:
        return faiss.IndexFlatIP(dimension)
    
    def get_name(self) -> str:
        return "FlatIP"
    
    def get_search_params(self) -> Dict[str, Any]:
        return {}

class IVFStrategy(IndexStrategy):
    """IVF索引策略（倒排文件索引）"""
    
    def __init__(self, nlist: int = 100, nprobe: int = 10):
        self.nlist = nlist
        self.nprobe = nprobe
    
    def create_index(self, dimension: int, **kwargs) -> faiss.Index:
        quantizer = faiss.IndexFlatIP(dimension)
        index = faiss.IndexIVFFlat(quantizer, dimension, self.nlist, faiss.METRIC_INNER_PRODUCT)
        return index
    
    def get_name(self) -> str:
        return f"IVF{self.nlist}"
    
    def get_search_params(self) -> Dict[str, Any]:
        return {"nprobe": self.nprobe}

class HNSWStrategy(IndexStrategy):
    """HNSW索引策略（分层导航小世界图）"""
    
    def __init__(self, M: int = 16, efConstruction: int = 200, efSearch: int = 50):
        self.M = M
        self.efConstruction = efConstruction
        self.efSearch = efSearch
    
    def create_index(self, dimension: int, **kwargs) -> faiss.Index:
        index = faiss.IndexHNSWFlat(dimension, self.M, faiss.METRIC_INNER_PRODUCT)
        index.hnsw.efConstruction = self.efConstruction
        index.hnsw.efSearch = self.efSearch
        return index
    
    def get_name(self) -> str:
        return f"HNSW_M{self.M}"
    
    def get_search_params(self) -> Dict[str, Any]:
        return {"efSearch": self.efSearch}

class PQStrategy(IndexStrategy):
    """乘积量化索引策略（压缩索引）"""
    
    def __init__(self, M: int = 8, nbits: int = 8):
        self.M = M
        self.nbits = nbits
    
    def create_index(self, dimension: int, **kwargs) -> faiss.Index:
        index = faiss.IndexPQ(dimension, self.M, self.nbits, faiss.METRIC_INNER_PRODUCT)
        return index
    
    def get_name(self) -> str:
        return f"PQ{self.M}x{self.nbits}"
    
    def get_search_params(self) -> Dict[str, Any]:
        return {}

class IVFPQStrategy(IndexStrategy):
    """IVF+PQ混合索引策略（高效压缩搜索）"""
    
    def __init__(self, nlist: int = 100, M: int = 8, nbits: int = 8, nprobe: int = 10):
        self.nlist = nlist
        self.M = M
        self.nbits = nbits
        self.nprobe = nprobe
    
    def create_index(self, dimension: int, **kwargs) -> faiss.Index:
        quantizer = faiss.IndexFlatIP(dimension)
        index = faiss.IndexIVFPQ(quantizer, dimension, self.nlist, self.M, self.nbits, faiss.METRIC_INNER_PRODUCT)
        return index
    
    def get_name(self) -> str:
        return f"IVFPQ{self.nlist}_{self.M}x{self.nbits}"
    
    def get_search_params(self) -> Dict[str, Any]:
        return {"nprobe": self.nprobe}

class IndexManager:
    """索引管理器"""
    
    def __init__(self):
        self.config = get_config()
        self.index = None
        self.metadata = None
        self.strategy = None
        self.is_trained = False
        
        # 初始化策略
        self._init_strategy()
        
        # 记录初始化信息
        if self.strategy:
            logger.info(f"Initialized IndexManager with strategy: {self.strategy.get_name()}")
        else:
            logger.info("Initialized IndexManager with auto-select strategy")
    
    def _init_strategy(self):
        """初始化索引策略"""
        if getattr(self.config.index, 'auto_select', False):
            # 延迟初始化策略，等到有实际数据时再决定
            self.strategy = None
            self.size_threshold = getattr(self.config.index, 'size_threshold', 1000)
            logger.info(f"Using auto-select strategy with threshold {self.size_threshold}")
            return

        # 如果没有启用自动选择，使用指定的策略
        strategy_config = self.config.index.strategies.get('large')  # 默认使用large策略
        if strategy_config:
            self._set_strategy(strategy_config.index_type, strategy_config)
        else:
            logger.warning("No valid strategy found, using FlatIP")
            self.strategy = FlatIPStrategy()

    def _set_strategy(self, index_type: str, strategy_config=None):
        """设置具体的索引策略"""
        try:
            if index_type == "flat":
                self.strategy = FlatIPStrategy()
            elif index_type == "ivf":
                self.strategy = IVFStrategy(
                    nlist=getattr(strategy_config, 'nlist', 100),
                    nprobe=getattr(strategy_config, 'nprobe', 10)
                )
            elif index_type == "hnsw":
                self.strategy = HNSWStrategy()
            elif index_type == "pq":
                self.strategy = PQStrategy()
            elif index_type == "ivfpq":
                self.strategy = IVFPQStrategy(
                    nlist=getattr(strategy_config, 'nlist', 100),
                    nprobe=getattr(strategy_config, 'nprobe', 10)
                )
            else:
                logger.warning(f"Unknown index type: {index_type}, using FlatIP")
                self.strategy = FlatIPStrategy()

            if self.strategy:
                logger.info(f"Selected index strategy: {self.strategy.get_name()}")
        except Exception as e:
            logger.error(f"Error setting strategy: {e}, using FlatIP as fallback")
            self.strategy = FlatIPStrategy()

    def _select_strategy(self, num_vectors: int):
        """根据向量数量选择合适的索引策略"""
        if not getattr(self.config.index, 'auto_select', False):
            return

        strategies = getattr(self.config.index, 'strategies', None)
        if not strategies:
            logger.warning("No strategies defined, using default FlatIP strategy")
            self.strategy = FlatIPStrategy()
            return

        if num_vectors < self.size_threshold:
            logger.info(f"Small dataset detected ({num_vectors} < {self.size_threshold}), using flat index")
            strategy_config = strategies.get('small')
            if strategy_config:
                self._set_strategy(strategy_config.index_type, strategy_config)
            else:
                logger.warning("Small dataset strategy not found, using default FlatIP strategy")
                self.strategy = FlatIPStrategy()
        else:
            logger.info(f"Large dataset detected ({num_vectors} >= {self.size_threshold}), using IVF index")
            strategy_config = strategies.get('large')
            if strategy_config:
                self._set_strategy(strategy_config.index_type, strategy_config)
            else:
                logger.warning("Large dataset strategy not found, using default IVF strategy")
                self.strategy = IVFStrategy(nlist=50, nprobe=10)

    @performance_monitor("index_creation")
    def create_index(self, embeddings: np.ndarray) -> faiss.Index:
        """创建并训练索引"""
        if embeddings.size == 0:
            raise ValueError("Cannot create index with empty embeddings")
        
        # 根据数据规模选择策略
        if self.strategy is None:
            self._select_strategy(len(embeddings))
        
        dimension = embeddings.shape[1]
        logger.info(f"Creating {self.strategy.get_name()} index with dimension {dimension}")
        
        # 创建索引
        index = self.strategy.create_index(dimension)
        
        # 训练索引（如果需要）
        if not index.is_trained:
            logger.info("Training index...")
            start_time = time.time()
            
            # 对于大型数据集，使用子采样进行训练
            training_data = self._prepare_training_data(embeddings)
            index.train(training_data)
            
            training_time = time.time() - start_time
            logger.info(f"Index training completed in {training_time:.2f}s")
        
        # 添加向量
        logger.info(f"Adding {len(embeddings)} vectors to index...")
        start_time = time.time()
        index.add(embeddings)
        
        add_time = time.time() - start_time
        logger.info(f"Vectors added in {add_time:.2f}s")
        
        # 移动到GPU（如果配置且可用）
        strategy_config = self.config.index.strategies.get('large')
        if strategy_config and getattr(strategy_config, 'use_gpu', False) and faiss.get_num_gpus() > 0:
            index = self._move_to_gpu(index)
        
        self.index = index
        self.is_trained = True
        
        return index
    
    def _prepare_training_data(self, embeddings: np.ndarray, max_training_size: int = 100000) -> np.ndarray:
        """准备训练数据"""
        if len(embeddings) <= max_training_size:
            return embeddings
        
        # 随机采样
        indices = np.random.choice(len(embeddings), max_training_size, replace=False)
        training_data = embeddings[indices]
        
        logger.info(f"Using {len(training_data)} samples for training (sampled from {len(embeddings)})")
        return training_data
    
    def _move_to_gpu(self, index: faiss.Index) -> faiss.Index:
        """将索引移动到GPU"""
        try:
            res = faiss.StandardGpuResources()
            gpu_index = faiss.index_cpu_to_gpu(res, 0, index)
            logger.info("Index moved to GPU")
            return gpu_index
        except Exception as e:
            logger.warning(f"Failed to move index to GPU: {e}")
            return index
    
    @performance_monitor("index_search")
    def search(self, query_embeddings: np.ndarray, k: int) -> Tuple[np.ndarray, np.ndarray]:
        """搜索相似向量"""
        if self.index is None:
            raise RuntimeError("Index not loaded. Call load_index() first.")
        
        # 设置搜索参数
        search_params = self.strategy.get_search_params()
        for param, value in search_params.items():
            if hasattr(self.index, param):
                setattr(self.index, param, value)
        
        # 执行搜索
        similarities, indices = self.index.search(query_embeddings, k)
        
        return similarities, indices
    
    @performance_monitor("index_save")
    def save_index(self, index_file: Optional[str] = None, metadata_file: Optional[str] = None):
        """保存索引和元数据"""
        if self.index is None:
            raise RuntimeError("No index to save")
        
        index_file = index_file or self.config.index.index_file
        metadata_file = metadata_file or self.config.index.metadata_file
        
        # 保存索引
        logger.info(f"Saving index to {index_file}")
        faiss.write_index(self.index, index_file)
        
        # 保存元数据
        if self.metadata is not None:
            logger.info(f"Saving metadata to {metadata_file}")
            with open(metadata_file, 'wb') as f:
                pickle.dump(self.metadata, f)
        
        logger.info("Index and metadata saved successfully")
    
    @performance_monitor("index_load")
    def load_index(self, index_file: Optional[str] = None, metadata_file: Optional[str] = None):
        """加载索引和元数据"""
        index_file = index_file or self.config.index.index_file
        metadata_file = metadata_file or self.config.index.metadata_file
        
        if not os.path.exists(index_file):
            raise FileNotFoundError(f"Index file not found: {index_file}")
        
        # 加载索引
        logger.info(f"Loading index from {index_file}")
        self.index = faiss.read_index(index_file)
        
        # 移动到GPU（如果配置且可用）
        if self.config.index.use_gpu and faiss.get_num_gpus() > 0:
            self.index = self._move_to_gpu(self.index)
        
        # 加载元数据
        if os.path.exists(metadata_file):
            logger.info(f"Loading metadata from {metadata_file}")
            with open(metadata_file, 'rb') as f:
                self.metadata = pickle.load(f)
        else:
            logger.warning(f"Metadata file not found: {metadata_file}")
            self.metadata = []
        
        self.is_trained = True
        logger.info(f"Index loaded successfully. Total vectors: {self.index.ntotal}")
        
        # 自动推断并设置 self.strategy
        if isinstance(self.index, faiss.IndexFlatIP):
            self.strategy = FlatIPStrategy()
        elif isinstance(self.index, faiss.IndexIVFFlat):
            nlist = getattr(self.index, 'nlist', 100)
            nprobe = getattr(self.index, 'nprobe', 10)
            self.strategy = IVFStrategy(nlist=nlist, nprobe=nprobe)
        elif hasattr(faiss, 'IndexHNSWFlat') and isinstance(self.index, faiss.IndexHNSWFlat):
            self.strategy = HNSWStrategy()
        elif hasattr(faiss, 'IndexPQ') and isinstance(self.index, faiss.IndexPQ):
            self.strategy = PQStrategy()
        elif hasattr(faiss, 'IndexIVFPQ') and isinstance(self.index, faiss.IndexIVFPQ):
            nlist = getattr(self.index, 'nlist', 100)
            nprobe = getattr(self.index, 'nprobe', 10)
            M = getattr(self.index, 'M', 8)
            nbits = getattr(self.index, 'nbits', 8)
            self.strategy = IVFPQStrategy(nlist=nlist, M=M, nbits=nbits, nprobe=nprobe)
        else:
            self.strategy = FlatIPStrategy()
    
    def add_vectors(self, embeddings: np.ndarray, metadata: List[Dict[str, Any]]):
        """向现有索引添加向量"""
        if self.index is None:
            raise RuntimeError("Index not loaded")
        
        logger.info(f"Adding {len(embeddings)} new vectors to index")
        
        # 添加嵌入
        self.index.add(embeddings)
        
        # 添加元数据
        if self.metadata is None:
            self.metadata = []
        self.metadata.extend(metadata)
        
        logger.info(f"Total vectors in index: {self.index.ntotal}")
    
    def remove_vectors(self, ids: List[int]):
        """从索引中移除向量（仅部分索引类型支持）"""
        if hasattr(self.index, 'remove_ids'):
            self.index.remove_ids(np.array(ids, dtype=np.int64))
            logger.info(f"Removed {len(ids)} vectors from index")
        else:
            logger.warning("Current index type does not support vector removal")
    
    def optimize_index(self):
        """优化索引性能"""
        if self.index is None:
            return
        
        logger.info("Optimizing index...")
        
        # 对于IVF索引，调整nprobe参数
        if hasattr(self.index, 'nprobe'):
            # 根据数据量动态调整nprobe
            total_vectors = self.index.ntotal
            optimal_nprobe = min(max(int(np.sqrt(total_vectors / 1000)), 1), 100)
            self.index.nprobe = optimal_nprobe
            logger.info(f"Adjusted nprobe to {optimal_nprobe}")
        
        # 对于HNSW索引，调整efSearch参数
        if hasattr(self.index, 'hnsw'):
            total_vectors = self.index.ntotal
            optimal_ef = min(max(int(np.log2(total_vectors)) * 10, 16), 400)
            self.index.hnsw.efSearch = optimal_ef
            logger.info(f"Adjusted efSearch to {optimal_ef}")
    
    def get_index_info(self) -> Dict[str, Any]:
        """获取索引信息"""
        if self.index is None:
            return {"status": "not_loaded"}
        
        info = {
            "status": "loaded",
            "strategy": self.strategy.get_name() if self.strategy else "auto_select",
            "total_vectors": self.index.ntotal,
            "dimension": self.index.d,
            "is_trained": self.index.is_trained,
            "metric_type": "inner_product"
        }
        
        # 添加策略特定信息
        if hasattr(self.index, 'nlist'):
            info["nlist"] = self.index.nlist
        if hasattr(self.index, 'nprobe'):
            info["nprobe"] = self.index.nprobe
        if hasattr(self.index, 'hnsw'):
            info["hnsw_M"] = self.index.hnsw.M
            info["hnsw_efSearch"] = self.index.hnsw.efSearch
        
        return info
    
    def benchmark_search(self, query_embeddings: np.ndarray, k_values: List[int] = None) -> Dict[str, Any]:
        """基准测试搜索性能"""
        if self.index is None:
            raise RuntimeError("Index not loaded")
        
        k_values = k_values or [1, 5, 10, 25, 50]
        results = {}
        
        logger.info("Running search benchmark...")
        
        for k in k_values:
            start_time = time.time()
            similarities, indices = self.search(query_embeddings, k)
            elapsed_time = time.time() - start_time
            
            results[f"k={k}"] = {
                "time_per_query": elapsed_time / len(query_embeddings),
                "total_time": elapsed_time,
                "queries_per_second": len(query_embeddings) / elapsed_time
            }
        
        return results

# 全局索引管理器实例
_index_manager = None

def get_index_manager() -> IndexManager:
    """获取全局索引管理器"""
    global _index_manager
    if _index_manager is None:
        _index_manager = IndexManager()
    return _index_manager