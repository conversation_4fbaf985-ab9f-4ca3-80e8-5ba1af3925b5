
Search files...
本项目为 UniXcoder RAG 智能代码检索与问答系统，支持代码库的语义检索、问答和性能评测。以下为详细的运行与评估方法：

---

## 一、如何运行本项目

### 1. 环境准备
- **Python 版本**：需要 Python 3.10 及以上。
- **依赖安装**：
  - 推荐使用 `uv`（如已安装）：  
    ```bash
    uv sync
    ```
  - 或直接用 pip（首次运行会自动安装依赖）：  
    ```bash
    pip install -e . --upgrade
    ```

### 2. 配置环境变量
- 在 `.env` 文件或环境变量中设置：
  ```
  DEEPSEEK_API_KEY=你的API密钥
  ```

### 3. 构建索引
**首次使用必须构建代码索引：**

```bash
# 方式一：快速构建（推荐）
python quick_build_index.py

# 方式二：标准构建
python build_index.py
```

**注意：** 索引构建可能需要几分钟时间，请耐心等待。构建完成后会生成 `faiss_index.bin` 和 `metadata.pkl` 文件。

### 4. 启动应用

#### 方式一：Streamlit Web界面（推荐）
- **快速演示**（推荐新用户）：
  ```bash
  python demo_streamlit.py
  ```
- 直接启动应用：
  ```bash
  ./start_streamlit.sh
  ```
  或者：
  ```bash
  streamlit run frontend/app.py
  ```
- 在浏览器中访问：`http://localhost:8501`
- 功能特性：
  - 🔍 **智能代码检索** - 支持语义搜索和混合检索
  - 💬 **气泡对话界面** - 类似微信的气泡样式对话体验
  - 📝 **Markdown渲染** - AI回答支持完整的Markdown格式显示
  - 🔄 **流式AI回答** - 实时显示AI生成过程，支持中断控制
  - 📚 **侧边栏引用** - 代码引用移至侧边栏，支持收起展开
  - 📊 **可视化分析** - 相似度分数图表和检索方法分析
  - 🎯 **精确定位** - 显示文件路径、行号和上下文信息
  - 💾 **对话管理** - 历史记录、新建对话、上下文控制
  - 🎨 **响应式设计** - 美观的渐变色彩和动画效果

#### 方式二：命令行交互模式
- 启动交互模式：
  ```bash
  python ask.py
  ```

  ```bash
  uv run python ask.py "当前项目的unixcoder模型，接口设计如何调用的，功能有哪些"
  ```

- 单次提问模式：
  ```bash
  python ask.py "你的问题"
  ```
- 常用命令（在交互模式下输入）：
  - `exit` 退出
  - `stats` 查看性能统计
  - `help` 查看帮助

---

## 二、如何对生成内容进行全面评估

### 1. 检索与问答评测
- 运行自动化评测脚本，测试检索准确率与问答效果：
  ```bash
  python tests/test_evaluation.py
  ```
  该脚本会输出检索准确率等评估指标。

### 2. 性能基准测试
- 运行基准测试，全面评估系统性能（速度、准确率等）：
  ```bash
  python ask.py --benchmark
  ```
  或直接运行
  ```bash
  python tests/benchmark.py
  ```
  测试结果会自动生成详细的 JSON 报告（如 `benchmark_report_*.json`），包括：
  - 检索/问答的成功率
  - 响应时间统计
  - 系统硬件与配置快照

### 3. 优化效果演示
- 运行优化演示脚本，展示系统各项优化能力：
  ```bash
  python tests/optimization_demo.py
  ```

### 4. 集成测试
- 运行集成测试验证系统核心功能：
  ```bash
  python tests/test_integration.py
  ```

---

## 三、常见问题与文档

- **详细参数与用法**：  
  查看 `README.md` 或在命令行运行  
  ```bash
  python ask.py --help
  ```
- **分块策略与检索机制**：  
  详见 `docs/unixcoder分块策略.md` 和 CLAUDE.md。
- **配置说明**：  
  配置集中在 `config.json`，可通过环境变量或命令行参数覆盖。

---

如需进一步自定义评测、扩展评估指标，可直接修改 `tests/test_evaluation.py` 和 `tests/benchmark.py`，两者均支持详细的评测与报告输出。

## 四、项目结构

```
Unixcoder/
├── README.md                    # 项目说明文档
├── pyproject.toml              # 项目配置文件
├── config.json                 # 系统配置
├── .gitignore                  # Git忽略文件
├── uv.lock                     # 依赖锁定文件
├── start_streamlit.sh          # Streamlit前端启动脚本
├── test_frontend.py            # 前端功能测试脚本
├── test_basic_frontend.py      # 基础前端测试脚本
├── test_conversation_system.py # 对话系统测试脚本
├── demo_streamlit.py           # 前端演示脚本
├── demo_conversation.py        # 对话系统演示脚本
├── demo_bubble_chat.py         # 气泡对话界面演示脚本
├── quick_build_index.py        # 快速索引构建脚本
├──
├── # 核心模块
├── ask.py                      # 交互式问答主程序
├── build_index.py              # 索引构建脚本
├── unixcoder.py               # UniXcoder模型封装
├── code_chunker.py            # 代码分块逻辑
├── embedding_generator.py     # 嵌入生成器
├── index_manager.py           # 索引管理器
├── hybrid_retriever.py        # 混合检索器
├── advanced_prompt_builder.py # 高级提示构建器
├── dependency_analyzer.py     # 依赖分析器
├──
├── # 管理器模块
├── cache_manager.py           # 缓存管理器
├── config_manager.py          # 配置管理器
├── model_manager.py           # 模型管理器
├── performance_monitor.py     # 性能监控器
├── resource_manager.py        # 资源管理器
├──
├──
├── # Streamlit前端应用
├── frontend/
│   ├── app.py                 # 主Streamlit应用
│   ├── api_service.py         # API服务抽象层
│   ├── code_viewer.py         # 代码片段展示组件
│   └── reference_viewer.py    # 代码引用查看器
├── # 测试和评估
├── tests/
│   ├── README.md              # 测试说明文档
│   ├── test_evaluation.py     # 检索与问答评测
│   ├── test_integration.py    # 集成测试
│   ├── benchmark.py           # 性能基准测试
│   └── optimization_demo.py   # 优化效果演示
├──
├── # 文档和日志
├── docs/
│   ├── unixcoder分块策略.md   # 分块策略文档
│   └── FRONTEND_SUMMARY.md    # 前端应用说明
├── updatelogs/                # 更新日志
│   ├── 0.1.0.md
│   └── 0.1.1.md
├──
└── vendor/                    # 第三方依赖
    └── tree-sitter-python/
```

如有其他问题，欢迎继续提问！
