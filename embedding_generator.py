"""
批量嵌入生成器
优化的批量embedding生成，支持GPU加速和内存管理
"""

import time
import torch
import numpy as np
import hashlib
from typing import List, Tuple, Iterator, Optional, Dict, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading
from queue import Queue, Empty

from config import get_config
from model_manager import get_model_manager
from logger import get_logger, performance_monitor
from cache_manager import get_cache_manager, cached
from exceptions import EmbeddingError, ModelInferenceError, error_handler

logger = get_logger(__name__)

@dataclass
class EmbeddingTask:
    """嵌入生成任务"""
    id: str
    text: str
    metadata: Dict[str, Any]

@dataclass
class EmbeddingResult:
    """嵌入生成结果"""
    id: str
    embedding: np.ndarray
    metadata: Dict[str, Any]
    processing_time: float

class BatchEmbeddingGenerator:
    """批量嵌入生成器"""
    
    def __init__(self, batch_size: Optional[int] = None):
        self.config = get_config()
        self.model_manager = get_model_manager()
        self.batch_size = batch_size or self.config.system.batch_size
        self.device = self.model_manager.device
        
        # 使用新的缓存管理器
        self.cache_manager = get_cache_manager()
        self.cache = self.cache_manager.create_cache(
            name="embedding_cache",
            max_size=self.config.cache.embedding_max_size,
            max_memory_mb=self.config.cache.embedding_max_memory_mb,
            ttl=self.config.cache.embedding_ttl
        )
        
        logger.info(f"Initialized BatchEmbeddingGenerator with batch_size={self.batch_size}, device={self.device}")
    
    @performance_monitor("batch_embedding_generation")
    def generate_embeddings(self, tasks: List[EmbeddingTask]) -> List[EmbeddingResult]:
        """批量生成嵌入"""
        if not tasks:
            return []
        
        logger.info(f"Starting batch embedding generation for {len(tasks)} tasks")
        start_time = time.time()
        
        # 检查缓存
        cached_results, uncached_tasks = self._check_cache(tasks)
        
        if uncached_tasks:
            # 生成未缓存的嵌入
            new_results = self._generate_uncached_embeddings(uncached_tasks)
            # 更新缓存
            self._update_cache(new_results)
        else:
            new_results = []
        
        # 合并结果
        all_results = cached_results + new_results
        
        # 按原始顺序排序
        task_id_to_index = {task.id: i for i, task in enumerate(tasks)}
        all_results.sort(key=lambda r: task_id_to_index[r.id])
        
        total_time = time.time() - start_time
        logger.info(f"Batch embedding generation completed in {total_time:.2f}s "
                   f"(cached: {len(cached_results)}, generated: {len(new_results)})")
        
        return all_results
    
    def _check_cache(self, tasks: List[EmbeddingTask]) -> Tuple[List[EmbeddingResult], List[EmbeddingTask]]:
        """检查缓存，返回缓存的结果和需要生成的任务"""
        if not self.config.system.cache_embeddings:
            return [], tasks
        
        cached_results = []
        uncached_tasks = []
        
        for task in tasks:
            cache_key = self._get_cache_key(task.text)
            cached_embedding = self.cache.get(cache_key)
            
            if cached_embedding is not None:
                result = EmbeddingResult(
                    id=task.id,
                    embedding=cached_embedding,
                    metadata=task.metadata,
                    processing_time=0.0
                )
                cached_results.append(result)
            else:
                uncached_tasks.append(task)
        
        return cached_results, uncached_tasks
    
    def _generate_uncached_embeddings(self, tasks: List[EmbeddingTask]) -> List[EmbeddingResult]:
        """生成未缓存的嵌入"""
        model = self.model_manager.get_unixcoder()
        results = []
        
        # 分批处理
        for batch_start in range(0, len(tasks), self.batch_size):
            batch_end = min(batch_start + self.batch_size, len(tasks))
            batch_tasks = tasks[batch_start:batch_end]
            
            batch_results = self._process_batch(model, batch_tasks)
            results.extend(batch_results)
        
        return results
    
    @performance_monitor("single_batch_processing")
    def _process_batch(self, model, batch_tasks: List[EmbeddingTask]) -> List[EmbeddingResult]:
        """处理单个批次"""
        start_time = time.time()
        
        # 准备输入文本
        texts = [task.text for task in batch_tasks]
        
        # 批量分词
        with torch.no_grad():
            try:
                # 使用批量分词
                tokens_ids = model.tokenize(
                    texts, 
                    max_length=self.config.model.max_length, 
                    mode="<encoder-only>"
                )
                
                # 转换为张量并移动到设备
                batch_size = len(tokens_ids)
                max_len = max(len(tokens) for tokens in tokens_ids)
                
                # 创建批量张量
                source_ids = torch.zeros(batch_size, max_len, dtype=torch.long, device=self.device)
                for i, tokens in enumerate(tokens_ids):
                    source_ids[i, :len(tokens)] = torch.tensor(tokens, dtype=torch.long)
                
                # 批量生成嵌入
                _, embeddings = model(source_ids)
                
                # 归一化嵌入
                normalized_embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
                embeddings_numpy = normalized_embeddings.cpu().numpy()
                
            except Exception as e:
                logger.error(f"Error in batch processing: {e}")
                # 抛出更具体的异常
                raise ModelInferenceError(
                    f"Failed to generate embeddings for batch of {len(batch_tasks)} items",
                    model_name="unixcoder",
                    cause=e
                )
        
        # 创建结果
        processing_time = time.time() - start_time
        per_item_time = processing_time / len(batch_tasks)
        
        results = []
        for i, task in enumerate(batch_tasks):
            result = EmbeddingResult(
                id=task.id,
                embedding=embeddings_numpy[i],
                metadata=task.metadata,
                processing_time=per_item_time
            )
            results.append(result)
        
        logger.debug(f"Processed batch of {len(batch_tasks)} items in {processing_time:.3f}s")
        return results
    
    def _process_batch_fallback(self, model, batch_tasks: List[EmbeddingTask]) -> List[EmbeddingResult]:
        """批处理失败时的降级处理"""
        logger.warning("Falling back to individual processing")
        results = []
        
        for task in batch_tasks:
            try:
                start_time = time.time()
                
                tokens_ids = model.tokenize(
                    [task.text], 
                    max_length=self.config.model.max_length, 
                    mode="<encoder-only>"
                )
                source_ids = torch.tensor(tokens_ids, device=self.device)
                
                with torch.no_grad():
                    _, embedding = model(source_ids)
                    normalized = torch.nn.functional.normalize(embedding, p=2, dim=1)
                    embedding_numpy = normalized.cpu().numpy()[0]
                
                processing_time = time.time() - start_time
                
                result = EmbeddingResult(
                    id=task.id,
                    embedding=embedding_numpy,
                    metadata=task.metadata,
                    processing_time=processing_time
                )
                results.append(result)
                
            except Exception as e:
                logger.error(f"Failed to process task {task.id}: {e}")
                # 创建零嵌入作为降级
                zero_embedding = np.zeros(self.config.index.dimension, dtype=np.float32)
                result = EmbeddingResult(
                    id=task.id,
                    embedding=zero_embedding,
                    metadata=task.metadata,
                    processing_time=0.0
                )
                results.append(result)
        
        return results
    
    def _update_cache(self, results: List[EmbeddingResult]):
        """更新嵌入缓存"""
        if not self.config.system.cache_embeddings:
            return
        
        for result in results:
            # 从metadata中重建原始文本（如果可能）
            if 'original_text' in result.metadata:
                text = result.metadata['original_text']
                cache_key = self._get_cache_key(text)
                self.cache.put(cache_key, result.embedding)
    
    def _get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def clear_cache(self):
        """清空嵌入缓存"""
        self.cache.clear()
        logger.info("Embedding cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        cache_stats = self.cache.get_stats()
        return {
            'cache_size': cache_stats.size,
            'cache_hits': cache_stats.hits,
            'cache_misses': cache_stats.misses,
            'hit_rate': cache_stats.hit_rate,
            'memory_usage_mb': cache_stats.memory_usage / 1024 / 1024,
            'cache_enabled': self.config.system.cache_embeddings
        }

class StreamingEmbeddingGenerator:
    """流式嵌入生成器，支持实时处理大量任务"""
    
    def __init__(self, batch_size: Optional[int] = None, max_queue_size: int = 1000):
        self.batch_generator = BatchEmbeddingGenerator(batch_size)
        self.task_queue = Queue(maxsize=max_queue_size)
        self.result_queue = Queue()
        self.stop_event = threading.Event()
        self.worker_thread = None
        
    def start(self):
        """启动流式处理"""
        if self.worker_thread is not None:
            return
        
        self.stop_event.clear()
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("Streaming embedding generator started")
    
    def stop(self):
        """停止流式处理"""
        if self.worker_thread is None:
            return
        
        self.stop_event.set()
        self.worker_thread.join()
        self.worker_thread = None
        logger.info("Streaming embedding generator stopped")
    
    def submit_task(self, task: EmbeddingTask) -> bool:
        """提交任务"""
        try:
            self.task_queue.put(task, timeout=1.0)
            return True
        except:
            return False
    
    def get_result(self, timeout: float = 1.0) -> Optional[EmbeddingResult]:
        """获取结果"""
        try:
            return self.result_queue.get(timeout=timeout)
        except Empty:
            return None
    
    def _worker_loop(self):
        """工作线程循环"""
        batch_tasks = []
        batch_timeout = 0.5  # 批处理超时时间
        last_batch_time = time.time()
        
        while not self.stop_event.is_set():
            try:
                # 尝试获取任务
                task = self.task_queue.get(timeout=0.1)
                batch_tasks.append(task)
                
                # 检查是否应该处理批次
                should_process = (
                    len(batch_tasks) >= self.batch_generator.batch_size or
                    (batch_tasks and time.time() - last_batch_time > batch_timeout)
                )
                
                if should_process:
                    self._process_and_send_batch(batch_tasks)
                    batch_tasks = []
                    last_batch_time = time.time()
                    
            except Empty:
                # 超时，检查是否有待处理的任务
                if batch_tasks and time.time() - last_batch_time > batch_timeout:
                    self._process_and_send_batch(batch_tasks)
                    batch_tasks = []
                    last_batch_time = time.time()
        
        # 处理剩余任务
        if batch_tasks:
            self._process_and_send_batch(batch_tasks)
    
    def _process_and_send_batch(self, batch_tasks: List[EmbeddingTask]):
        """处理批次并发送结果"""
        try:
            results = self.batch_generator.generate_embeddings(batch_tasks)
            for result in results:
                self.result_queue.put(result)
        except Exception as e:
            logger.error(f"Error processing batch: {e}")

# 全局实例
_batch_generator = None
_streaming_generator = None

def get_batch_generator() -> BatchEmbeddingGenerator:
    """获取批量嵌入生成器"""
    global _batch_generator
    if _batch_generator is None:
        _batch_generator = BatchEmbeddingGenerator()
    return _batch_generator

def get_streaming_generator() -> StreamingEmbeddingGenerator:
    """获取流式嵌入生成器"""
    global _streaming_generator
    if _streaming_generator is None:
        _streaming_generator = StreamingEmbeddingGenerator()
    return _streaming_generator