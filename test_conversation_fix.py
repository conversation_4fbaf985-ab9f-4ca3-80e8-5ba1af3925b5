#!/usr/bin/env python3
"""
测试对话功能修复效果
"""

import sys
import os
sys.path.append('.')

from frontend.api_service import CodeOptimizationAPI, AnalysisRequest
import time

def test_conversation_functionality():
    """测试对话功能"""
    print("🧪 测试对话功能修复效果...")
    
    try:
        # 初始化API服务
        api = CodeOptimizationAPI()
        api.initialize(".")
        
        # 模拟对话上下文
        context = [
            {"role": "user", "content": "什么是UniXcoder？"},
            {"role": "assistant", "content": "UniXcoder是一个基于Transformer的代码理解模型，专门用于代码分析和理解任务。"},
            {"role": "user", "content": "它有什么特点？"},
            {"role": "assistant", "content": "UniXcoder的主要特点包括：1. 支持多种编程语言 2. 能够理解代码语义 3. 适用于代码搜索和分析任务"}
        ]
        
        # 创建测试请求
        request = AnalysisRequest(
            query="请详细解释UniXcoder的工作原理",
            analysis_type="代码理解",
            project_path="."
        )
        
        print("\n📊 开始带上下文的流式分析...")
        print(f"📝 上下文长度: {len(context)} 条消息")
        
        # 统计信息
        status_count = 0
        search_results_count = 0
        llm_chunk_count = 0
        llm_content = ""
        complete_count = 0
        
        # 执行流式分析
        for chunk in api.analyze_code_streaming_with_context(request, context):
            chunk_type = chunk.get("type")
            
            if chunk_type == "status":
                status_count += 1
                print(f"📢 状态: {chunk.get('message', '')}")
                
            elif chunk_type == "search_results":
                search_results_count += 1
                results = chunk.get("results", [])
                count = chunk.get("count", 0)
                print(f"🔍 搜索结果: 找到 {count} 个相关代码片段")
                
            elif chunk_type == "llm_chunk":
                llm_chunk_count += 1
                content = chunk.get("content", "")
                llm_content += content
                
                # 每20个chunk显示一次进度
                if llm_chunk_count % 20 == 0:
                    print(f"🤖 LLM进度: {llm_chunk_count} chunks, 内容长度: {len(llm_content)}")
                    
            elif chunk_type == "complete":
                complete_count += 1
                execution_time = chunk.get("execution_time", 0)
                success = chunk.get("success", False)
                total_chunks = chunk.get("total_chunks", 0)
                
                print(f"✅ 完成: 成功={success}, 执行时间={execution_time:.2f}秒")
                print(f"📊 统计: 总chunk数={total_chunks}")
                break
                
            elif chunk_type == "error":
                print(f"❌ 错误: {chunk.get('error', '未知错误')}")
                break
        
        # 输出测试结果
        print("\n📈 测试结果统计:")
        print(f"  - 状态消息数量: {status_count}")
        print(f"  - 搜索结果数量: {search_results_count}")
        print(f"  - LLM chunk数量: {llm_chunk_count}")
        print(f"  - 完成消息数量: {complete_count}")
        print(f"  - LLM内容长度: {len(llm_content)}")
        
        # 验证对话功能
        print("\n🔍 对话功能验证:")
        
        # 检查是否正确处理上下文
        if "UniXcoder" in llm_content and len(llm_content) > 100:
            print("✅ 上下文正确传递，AI能够基于历史对话回答")
        else:
            print("⚠️  上下文传递可能有问题")
            
        # 检查内容是否保留
        if len(llm_content) > 0:
            print("✅ LLM内容正确保留")
            print(f"   内容预览: {llm_content[:150]}...")
        else:
            print("❌ LLM内容丢失")
            
        # 检查是否正确完成
        if complete_count == 1:
            print("✅ 正确完成，支持继续对话")
        else:
            print(f"⚠️  完成信息异常: {complete_count} 条")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_new_conversation():
    """测试新对话功能"""
    print("\n🆕 测试新对话功能...")
    
    # 模拟新对话（无上下文）
    try:
        api = CodeOptimizationAPI()
        api.initialize(".")
        
        request = AnalysisRequest(
            query="什么是Python？",
            analysis_type="代码理解",
            project_path="."
        )
        
        print("📊 开始无上下文的流式分析...")
        
        llm_content = ""
        for chunk in api.analyze_code_streaming_with_context(request, []):
            if chunk.get("type") == "llm_chunk":
                llm_content += chunk.get("content", "")
            elif chunk.get("type") == "complete":
                break
        
        if len(llm_content) > 0:
            print("✅ 新对话功能正常")
            print(f"   内容预览: {llm_content[:100]}...")
        else:
            print("❌ 新对话功能异常")
            
        return True
        
    except Exception as e:
        print(f"❌ 新对话测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_conversation_functionality()
    success2 = test_new_conversation()
    
    overall_success = success1 and success2
    print(f"\n🎯 总体测试结果: {'成功' if overall_success else '失败'}")
    sys.exit(0 if overall_success else 1)
