#!/usr/bin/env python3
"""
测试流式输出修复效果
"""

import sys
import os
sys.path.append('.')

from frontend.api_service import CodeOptimizationAPI, AnalysisRequest
import time

def test_streaming_output():
    """测试流式输出功能"""
    print("🧪 测试流式输出修复效果...")
    
    try:
        # 初始化API服务
        api = CodeOptimizationAPI()
        api.initialize(".")
        
        # 创建测试请求
        request = AnalysisRequest(
            query="代码理解",
            analysis_type="代码理解",
            project_path="."
        )
        
        print("\n📊 开始流式分析...")
        
        # 统计信息
        status_count = 0
        search_results_count = 0
        llm_chunk_count = 0
        llm_content = ""
        complete_count = 0
        
        # 执行流式分析
        for chunk in api.analyze_code_streaming(request):
            chunk_type = chunk.get("type")
            
            if chunk_type == "status":
                status_count += 1
                print(f"📢 状态: {chunk.get('message', '')}")
                
            elif chunk_type == "search_results":
                search_results_count += 1
                results = chunk.get("results", [])
                count = chunk.get("count", 0)
                print(f"🔍 搜索结果: 找到 {count} 个相关代码片段")
                
            elif chunk_type == "llm_chunk":
                llm_chunk_count += 1
                content = chunk.get("content", "")
                llm_content += content
                
                # 每10个chunk显示一次进度
                if llm_chunk_count % 10 == 0:
                    print(f"🤖 LLM进度: {llm_chunk_count} chunks, 内容长度: {len(llm_content)}")
                    
            elif chunk_type == "complete":
                complete_count += 1
                execution_time = chunk.get("execution_time", 0)
                success = chunk.get("success", False)
                total_chunks = chunk.get("total_chunks", 0)
                
                print(f"✅ 完成: 成功={success}, 执行时间={execution_time:.2f}秒")
                print(f"📊 统计: 总chunk数={total_chunks}")
                break
                
            elif chunk_type == "error":
                print(f"❌ 错误: {chunk.get('error', '未知错误')}")
                break
        
        # 输出测试结果
        print("\n📈 测试结果统计:")
        print(f"  - 状态消息数量: {status_count}")
        print(f"  - 搜索结果数量: {search_results_count}")
        print(f"  - LLM chunk数量: {llm_chunk_count}")
        print(f"  - 完成消息数量: {complete_count}")
        print(f"  - LLM内容长度: {len(llm_content)}")
        
        # 验证修复效果
        print("\n🔍 修复效果验证:")
        
        # 检查是否有冗余状态信息
        if status_count <= 2:
            print("✅ 状态信息简洁，无冗余")
        else:
            print(f"⚠️  状态信息可能冗余: {status_count} 条")
            
        # 检查内容是否保留
        if len(llm_content) > 0:
            print("✅ LLM内容正确保留")
            print(f"   内容预览: {llm_content[:100]}...")
        else:
            print("❌ LLM内容丢失")
            
        # 检查是否正确完成
        if complete_count == 1:
            print("✅ 正确完成，无重复完成信息")
        else:
            print(f"⚠️  完成信息异常: {complete_count} 条")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_streaming_output()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
