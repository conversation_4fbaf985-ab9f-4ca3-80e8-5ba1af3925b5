"""
配置管理模块
统一管理系统的所有配置参数，支持环境变量覆盖和配置文件加载
"""

import os
import json
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict, field
from pathlib import Path

@dataclass
class ModelConfig:
    """模型相关配置"""
    name: str = "microsoft/unixcoder-base"
    max_length: int = 512
    device: str = "auto"  # auto, cuda, cpu
    cache_dir: Optional[str] = None
    load_in_8bit: bool = False
    offline_mode: bool = False
    local_files_only: bool = False

@dataclass
class CacheConfig:
    """缓存相关配置"""
    enabled: bool = True
    embedding_max_size: int = 10000  # 最大缓存的embedding数量
    embedding_max_memory_mb: int = 1024  # 最大内存使用量（MB）
    embedding_ttl: int = 7200  # embedding缓存过期时间（秒）
    cache_dir: str = ".cache"
    cache_ttl: int = 3600  # 通用缓存过期时间（秒）
    use_disk: bool = True  # 是否使用磁盘缓存

@dataclass
class IndexStrategyConfig:
    """索引策略配置"""
    index_type: str
    dimension: int = 768
    nlist: Optional[int] = None
    nprobe: Optional[int] = None
    use_gpu: bool = False

@dataclass
class IndexConfig:
    """索引相关配置"""
    index_file: str = "faiss_index.bin"
    metadata_file: str = "metadata.pkl"
    auto_select: bool = True
    size_threshold: int = 1000
    use_gpu: bool = False
    strategies: Dict[str, IndexStrategyConfig] = None
    
    def __post_init__(self):
        if self.strategies is None:
            self.strategies = {
                "small": IndexStrategyConfig(
                    index_type="flat",
                    dimension=768
                ),
                "large": IndexStrategyConfig(
                    index_type="ivf",
                    dimension=768,
                    nlist=50,
                    nprobe=10,
                    use_gpu=False
                )
            }

@dataclass
class ChunkingConfig:
    """代码分块配置"""
    max_tokens: int = 900
    overlap_ratio: float = 0.1
    min_chunk_size: int = 50
    semantic_splitting: bool = True
    preserve_structure: bool = True

@dataclass
class RetrievalConfig:
    """检索相关配置"""
    initial_k: int = 25
    similarity_threshold: float = 0.34
    score_drop_off_threshold: float = 0.05
    max_results: int = 7
    reranker_model: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"
    reranker_max_length: int = 512
    use_reranking: bool = True

@dataclass
class LLMConfig:
    """LLM相关配置"""
    api_key_env: str = "DEEPSEEK_API_KEY"
    api_base: str = "https://api.deepseek.com/v1"
    model_name: str = "deepseek-chat"
    max_tokens: int = 4000
    temperature: float = 0.5
    stream: bool = True

@dataclass
class ProjectConfig:
    """项目目录配置"""
    root_dir: str = "."
    target_directories: list = None
    exclude_patterns: list = None
    file_extensions: list = None

    def __post_init__(self):
        # 从环境变量读取配置
        import os

        # 项目根目录
        self.root_dir = os.getenv('PROJECT_ROOT_DIR', self.root_dir)

        # 目标目录列表
        if self.target_directories is None:
            target_dirs_str = os.getenv('TARGET_DIRECTORIES', './,./tests,./docs')
            self.target_directories = [d.strip() for d in target_dirs_str.split(',') if d.strip()]

        # 排除模式
        if self.exclude_patterns is None:
            exclude_str = os.getenv('EXCLUDE_PATTERNS', '__pycache__,*.pyc,*.pyo,*.egg-info,.git,.venv,node_modules,build,dist')
            self.exclude_patterns = [p.strip() for p in exclude_str.split(',') if p.strip()]

        # 文件扩展名
        if self.file_extensions is None:
            extensions_str = os.getenv('FILE_EXTENSIONS', '.py,.md,.txt,.json,.toml,.yaml,.yml')
            self.file_extensions = [e.strip() for e in extensions_str.split(',') if e.strip()]

@dataclass
class SystemConfig:
    """系统级配置"""
    codebase_path: str = "."
    supported_extensions: list = None
    ignore_patterns: list = None
    log_level: str = "INFO"
    log_file: Optional[str] = None
    performance_monitoring: bool = True
    cache_embeddings: bool = True
    batch_size: int = 32

    def __post_init__(self):
        if self.supported_extensions is None:
            self.supported_extensions = ['.py', '.js', '.ts', '.java', '.cpp', '.c', '.go', '.rs']
        if self.ignore_patterns is None:
            self.ignore_patterns = ['.venv', '.git', 'node_modules', '__pycache__', 'vendor']

@dataclass
class Config:
    """主配置类"""
    model: ModelConfig = None
    cache: CacheConfig = None
    index: IndexConfig = None
    chunking: ChunkingConfig = None
    retrieval: RetrievalConfig = None
    llm: LLMConfig = None
    system: SystemConfig = None
    project: ProjectConfig = None

    def __post_init__(self):
        if self.model is None:
            self.model = ModelConfig()
        if self.cache is None:
            self.cache = CacheConfig()
        if self.index is None:
            self.index = IndexConfig()
        if self.chunking is None:
            self.chunking = ChunkingConfig()
        if self.retrieval is None:
            self.retrieval = RetrievalConfig()
        if self.llm is None:
            self.llm = LLMConfig()
        if self.system is None:
            self.system = SystemConfig()
        if self.project is None:
            self.project = ProjectConfig()

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.json"
        self._config = None
    
    def load_config(self) -> Config:
        """加载配置"""
        if self._config is not None:
            return self._config
            
        # 1. 从默认配置开始
        config_dict = {}
        
        # 2. 从配置文件加载
        if Path(self.config_file).exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                config_dict = self._merge_dict(config_dict, file_config)
            except Exception as e:
                print(f"Warning: Failed to load config file {self.config_file}: {e}")
        
        # 3. 从环境变量覆盖
        env_config = self._load_from_env()
        config_dict = self._merge_dict(config_dict, env_config)
        
        # 4. 创建配置对象
        self._config = self._dict_to_config(config_dict)
        return self._config
    
    def save_config(self, config: Config):
        """保存配置到文件"""
        config_dict = asdict(config)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def _load_from_env(self) -> Dict[str, Any]:
        """从环境变量加载配置"""
        env_config = {}
        
        # 模型配置
        if os.getenv('UNIXCODER_MODEL_NAME'):
            env_config.setdefault('model', {})['name'] = os.getenv('UNIXCODER_MODEL_NAME')
        if os.getenv('UNIXCODER_DEVICE'):
            env_config.setdefault('model', {})['device'] = os.getenv('UNIXCODER_DEVICE')
        
        # 索引配置
        if os.getenv('FAISS_INDEX_TYPE'):
            env_config.setdefault('index', {})['index_type'] = os.getenv('FAISS_INDEX_TYPE')
        if os.getenv('FAISS_USE_GPU'):
            env_config.setdefault('index', {})['use_gpu'] = os.getenv('FAISS_USE_GPU').lower() == 'true'
        
        # 检索配置
        if os.getenv('INITIAL_K'):
            env_config.setdefault('retrieval', {})['initial_k'] = int(os.getenv('INITIAL_K'))
        if os.getenv('SIMILARITY_THRESHOLD'):
            env_config.setdefault('retrieval', {})['similarity_threshold'] = float(os.getenv('SIMILARITY_THRESHOLD'))
        
        # LLM配置
        if os.getenv('DEEPSEEK_API_KEY'):
            env_config.setdefault('llm', {})['api_key_env'] = 'DEEPSEEK_API_KEY'
        if os.getenv('LLM_API_BASE'):
            env_config.setdefault('llm', {})['api_base'] = os.getenv('LLM_API_BASE')
        
        # 系统配置
        if os.getenv('CODEBASE_PATH'):
            env_config.setdefault('system', {})['codebase_path'] = os.getenv('CODEBASE_PATH')
        if os.getenv('LOG_LEVEL'):
            env_config.setdefault('system', {})['log_level'] = os.getenv('LOG_LEVEL')
        if os.getenv('BATCH_SIZE'):
            env_config.setdefault('system', {})['batch_size'] = int(os.getenv('BATCH_SIZE'))
        
        return env_config
    
    def _merge_dict(self, base: Dict, override: Dict) -> Dict:
        """递归合并字典"""
        result = base.copy()
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_dict(result[key], value)
            else:
                result[key] = value
        return result
    
    def _dict_to_config(self, config_dict: Dict) -> Config:
        """将字典转换为配置对象"""
        model_config = ModelConfig(**config_dict.get('model', {}))
        cache_config = CacheConfig(**config_dict.get('cache', {}))
        
        # 处理索引配置
        index_dict = config_dict.get('index', {})
        strategies_dict = index_dict.pop('strategies', {})
        strategies = {}
        for key, strategy in strategies_dict.items():
            strategies[key] = IndexStrategyConfig(**strategy)
        index_config = IndexConfig(**index_dict, strategies=strategies)
        
        chunking_config = ChunkingConfig(**config_dict.get('chunking', {}))
        retrieval_config = RetrievalConfig(**config_dict.get('retrieval', {}))
        llm_config = LLMConfig(**config_dict.get('llm', {}))
        system_config = SystemConfig(**config_dict.get('system', {}))
        
        return Config(
            model=model_config,
            cache=cache_config,
            index=index_config,
            chunking=chunking_config,
            retrieval=retrieval_config,
            llm=llm_config,
            system=system_config
        )

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config() -> Config:
    """获取全局配置"""
    return config_manager.load_config()

def reload_config(config_file: Optional[str] = None):
    """重新加载配置"""
    global config_manager
    config_manager = ConfigManager(config_file)
    return config_manager.load_config()