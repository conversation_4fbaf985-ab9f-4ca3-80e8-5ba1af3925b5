{"model": {"name": "microsoft/unixcoder-base", "max_length": 512, "device": "auto", "cache_dir": "./models", "load_in_8bit": false, "offline_mode": true, "local_files_only": true}, "cache": {"enabled": true, "embedding_max_size": 10000, "embedding_max_memory_mb": 1024, "embedding_ttl": 7200, "cache_dir": ".cache", "cache_ttl": 3600, "use_disk": true}, "index": {"index_file": "faiss_index.bin", "metadata_file": "metadata.pkl", "auto_select": true, "size_threshold": 1000, "use_gpu": false, "strategies": {"small": {"index_type": "flat", "dimension": 768}, "large": {"index_type": "ivf", "dimension": 768, "nlist": 50, "nprobe": 10, "use_gpu": false}}}, "chunking": {"max_tokens": 900, "overlap_ratio": 0.1, "min_chunk_size": 50, "semantic_splitting": true, "preserve_structure": true}, "retrieval": {"initial_k": 25, "similarity_threshold": 0.34, "score_drop_off_threshold": 0.05, "max_results": 7, "reranker_model": "cross-encoder/ms-marco-MiniLM-L-6-v2", "reranker_max_length": 512, "use_reranking": true}, "llm": {"api_key_env": "DEEPSEEK_API_KEY", "api_base": "https://api.deepseek.com/v1", "model_name": "deepseek-chat", "max_tokens": 4000, "temperature": 0.7, "stream": true}, "system": {"codebase_path": ".", "supported_extensions": [".py", ".js", ".ts", ".java", ".cpp", ".c", ".go", ".rs"], "ignore_patterns": [".venv", ".git", "node_modules", "__pycache__", "vendor"], "log_level": "INFO", "log_file": null, "performance_monitoring": true, "cache_embeddings": true, "batch_size": 32}, "embedding": {"batch_size": 32, "cache_size": 10000}, "search": {"top_k": 5, "min_score": 0.5}}