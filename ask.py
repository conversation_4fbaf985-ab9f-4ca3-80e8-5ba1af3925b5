import argparse
import os
import sys
import openai
import numpy as np
from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import get_config
from logger import get_logger, performance_monitor
from model_manager import get_model_manager
from index_manager import get_index_manager
from embedding_generator import get_batch_generator, EmbeddingTask
from hybrid_retriever import get_hybrid_retriever
from advanced_prompt_builder import get_prompt_builder
from dependency_analyzer import get_dependency_analyzer

logger = get_logger(__name__)

# 加载环境变量
load_dotenv()

class CodeAssistant:
    def __init__(self):
        """初始化代码助手"""
        self.config = get_config()
        
        logger.info("Initializing CodeAssistant...")
        
        # 初始化组件
        self.model_manager = get_model_manager()
        self.index_manager = get_index_manager()
        self.batch_generator = get_batch_generator()
        
        # 预加载模型
        logger.info("Preloading models...")
        self.model_manager.preload_models()
        
        # 加载索引
        logger.info("Loading index...")
        try:
            self.index_manager.load_index()
            logger.info(f"Index loaded: {self.index_manager.get_index_info()}")
        except Exception as e:
            logger.error(f"Failed to load index: {e}")
            raise
        
        # 初始化 LLM 客户端
        self._init_llm_client()
        
        # 初始化混合检索器
        logger.info("Initializing hybrid retriever...")
        self.hybrid_retriever = get_hybrid_retriever(self, self.model_manager.get_reranker())
        self.hybrid_retriever.build_index(self.index_manager.metadata, None)
        
        # 初始化高级Prompt构建器
        self.prompt_builder = get_prompt_builder()
        
        # 初始化依赖分析器
        self.dependency_analyzer = get_dependency_analyzer()
        
        logger.info("CodeAssistant initialized successfully")
    
    def _init_llm_client(self):
        """初始化LLM客户端"""
        self.llm_client = None
        api_key = os.environ.get(self.config.llm.api_key_env)
        
        if not api_key:
            logger.warning(f"LLM API key not found in environment variable {self.config.llm.api_key_env}")
            return
        
        try:
            # 临时清除代理设置，避免SOCKS代理问题
            old_http_proxy = os.environ.get('HTTP_PROXY')
            old_https_proxy = os.environ.get('HTTPS_PROXY')
            old_all_proxy = os.environ.get('ALL_PROXY')

            # 清除代理环境变量
            for proxy_var in ['HTTP_PROXY', 'HTTPS_PROXY', 'ALL_PROXY', 'http_proxy', 'https_proxy', 'all_proxy']:
                if proxy_var in os.environ:
                    del os.environ[proxy_var]

            try:
                self.llm_client = openai.OpenAI(
                    api_key=api_key,
                    base_url=self.config.llm.api_base
                )
                # 测试连接
                self.llm_client.models.list()
                logger.info("LLM client initialized successfully")
            finally:
                # 恢复代理设置
                if old_http_proxy:
                    os.environ['HTTP_PROXY'] = old_http_proxy
                if old_https_proxy:
                    os.environ['HTTPS_PROXY'] = old_https_proxy
                if old_all_proxy:
                    os.environ['ALL_PROXY'] = old_all_proxy

        except Exception as e:
            logger.error(f"Failed to initialize LLM client: {e}")
            self.llm_client = None

    @performance_monitor("query_embedding")
    def get_query_embedding(self, query_text: str) -> np.ndarray:
        """为查询生成嵌入"""
        task = EmbeddingTask(
            id="query",
            text=query_text,
            metadata={'query': True}
        )
        
        results = self.batch_generator.generate_embeddings([task])
        if not results:
            raise RuntimeError("Failed to generate query embedding")
        
        return results[0].embedding.reshape(1, -1)

    @performance_monitor("faiss_search")
    def search_once(self, query_embedding: np.ndarray) -> list:
        """执行一次FAISS搜索"""
        similarities, indices = self.index_manager.search(
            query_embedding, 
            self.config.retrieval.initial_k
        )
        
        results = []
        metadata = self.index_manager.metadata
        
        for i, idx in enumerate(indices[0]):
            if idx < len(metadata):
                results.append({
                    "similarity": similarities[0][i],
                    "chunk": metadata[idx]
                })
        return results

    @performance_monitor("dynamic_search")
    def dynamic_search(self, query_text: str) -> list:
        """执行混合搜索：语义+BM25+结构化检索"""
        logger.debug(f"Starting hybrid search for: {query_text[:50]}...")
        
        try:
            # 使用混合检索器
            results = self.hybrid_retriever.search(query_text)
            
            if not results:
                logger.warning("No results found from hybrid retriever")
                return []
            
            logger.debug(f"Hybrid retriever returned {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Hybrid search failed, falling back to semantic search: {e}")
            
            # 回退到原始语义搜索
            initial_embedding = self.get_query_embedding(query_text)
            potential_results = self.search_once(initial_embedding)
            
            if not potential_results:
                logger.warning("No fallback results found")
                return []
            
            # 重排序（如果启用）
            if self.config.retrieval.use_reranking and len(potential_results) > 1:
                reranked_results = self._rerank_results(query_text, potential_results)
            else:
                reranked_results = potential_results
            
            return reranked_results[:self.config.retrieval.max_results]
    
    @performance_monitor("reranking")
    def _rerank_results(self, query_text: str, results: list) -> list:
        """使用CrossEncoder重排结果"""
        logger.debug("Starting reranking...")
        
        reranker = self.model_manager.get_reranker()
        
        # 准备重排输入对
        rerank_pairs = []
        for res in results:
            chunk = res['chunk']
            input_text = self._build_chunk_text(chunk)
            rerank_pairs.append([query_text, input_text])
        
        # 执行重排
        rerank_scores = reranker.predict(rerank_pairs, show_progress_bar=False)
        
        # 更新分数
        for i, score in enumerate(rerank_scores):
            results[i]['similarity'] = score
        
        # 按新分数排序
        reranked = sorted(results, key=lambda x: x['similarity'], reverse=True)
        
        logger.debug(f"Reranking complete, top score: {reranked[0]['similarity']:.4f}")
        return reranked
    
    def _build_chunk_text(self, chunk: dict) -> str:
        """构建代码块的富文本描述"""
        metadata = chunk['metadata']
        input_text = f"File: {metadata.get('file_path', '')}\n"
        
        if metadata.get('parent_class'):
            input_text += f"Class: {metadata['parent_class']}\n"
        if metadata.get('name'):
            input_text += f"Name: {metadata['name']}\n"
        if metadata.get('signature'):
            input_text += f"Signature: {metadata['signature']}\n"
        if metadata.get('docstring'):
            input_text += f"Docstring: {metadata['docstring']}\n"
        
        input_text += f"Code:\n{chunk['code']}"
        return input_text

    @performance_monitor("llm_generation")
    def generate_answer_with_llm(self, prompt: str):
        """使用LLM生成答案"""
        if not self.llm_client:
            print("\n--- AI Assistant Answer ---")
            print("Cannot generate answer because LLM client is not configured.")
            return
        
        try:
            print(f"\n--- AI Assistant Answer ({self.config.llm.model_name}) ---")
            
            response = self.llm_client.chat.completions.create(
                model=self.config.llm.model_name,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                stream=self.config.llm.stream,
                temperature=self.config.llm.temperature,
                max_tokens=self.config.llm.max_tokens
            )
            
            if self.config.llm.stream:
                for chunk in response:
                    content = chunk.choices[0].delta.content
                    if content:
                        print(content, end='', flush=True)
                print()  # 换行
            else:
                answer = response.choices[0].message.content
                print(answer)
                
        except Exception as e:
            logger.error(f"LLM generation failed: {e}")
            print(f"\nError generating answer: {e}")

    def filter_results_dynamically(self, potential_results):
        """对一组搜索结果应用动态阈值和断崖检测。
        注意：此函数现在与Cross-Encoder的逻辑不兼容，暂时不使用。
        """
        if not potential_results:
            return []

        # 3. 应用动态阈值和断崖检测
        final_results = []
        # 3.1 首先加入最相关的结果
        if potential_results[0]['similarity'] >= SIMILARITY_THRESHOLD:
            final_results.append(potential_results[0])
        else:
            # 如果最相关的结果都不满足最低门槛，则直接返回空
            return []

        # 3.2 遍历剩余结果
        for i in range(1, len(potential_results)):
            # 检查是否已达到最大数量
            if len(final_results) >= MAX_RESULTS:
                break
            
            # 检查是否低于绝对阈值
            if potential_results[i]['similarity'] < SIMILARITY_THRESHOLD:
                break
                
            # 检查相似度"断崖"
            score_diff = final_results[-1]['similarity'] - potential_results[i]['similarity']
            if score_diff > SCORE_DROP_OFF_THRESHOLD:
                break
                
            final_results.append(potential_results[i])
            
        return final_results

    def entity_match(self, meta, expected_entity):
        expected = expected_entity.lower()
        for key in ['name', 'signature', 'docstring']:
            value = meta.get(key, '')
            if expected in value.lower():
                return True
        return False

    @performance_monitor("prompt_generation")
    def generate_llm_prompt(self, query: str, results: list) -> str:
        """生成高级LLM提示词"""
        try:
            # 检测查询类型
            query_type = self.detect_query_type(query)
            
            # 转换结果格式以匹配高级Prompt构建器
            chunks = []
            for res in results:
                chunk = res.get('chunk', res)  # 兼容不同格式
                chunks.append(chunk)
            
            # 使用高级Prompt构建器
            advanced_prompt = self.prompt_builder.build_prompt(query, chunks, query_type)
            
            logger.debug(f"Generated advanced prompt with query type: {query_type}")
            return advanced_prompt
            
        except Exception as e:
            logger.error(f"Advanced prompt generation failed, falling back to basic prompt: {e}")
            
            # 回退到基础Prompt生成
            return self._generate_basic_prompt(query, results)

    def generate_llm_prompt_with_context(self, query: str, results: list, context: list = None) -> str:
        """生成高级LLM提示词 - 支持对话上下文"""
        try:
            # 检测查询类型
            query_type = self.detect_query_type(query)

            # 转换结果格式以匹配高级Prompt构建器
            chunks = []
            for res in results:
                chunk = res.get('chunk', res)  # 兼容不同格式
                chunks.append(chunk)

            # 使用高级Prompt构建器，包含上下文
            if context and len(context) > 0:
                # 构建包含对话历史的提示
                context_info = "\n\n## 对话历史:\n"
                for i, msg in enumerate(context[-6:]):  # 只包含最近3轮对话
                    if msg["role"] == "user":
                        context_info += f"用户: {msg['content']}\n"
                    elif msg["role"] == "assistant":
                        context_info += f"助手: {msg['content'][:200]}...\n"  # 截断长回答
                context_info += "\n请基于以上对话历史和新的代码片段回答用户的问题。\n"

                # 将上下文添加到查询中
                enhanced_query = context_info + "\n当前问题: " + query
                advanced_prompt = self.prompt_builder.build_prompt(enhanced_query, chunks, query_type)
            else:
                advanced_prompt = self.prompt_builder.build_prompt(query, chunks, query_type)

            logger.debug(f"Generated advanced prompt with query type: {query_type}, context: {len(context) if context else 0} messages")
            return advanced_prompt

        except Exception as e:
            logger.error(f"Advanced prompt generation with context failed, falling back to basic prompt: {e}")

            # 回退到基础Prompt生成
            return self._generate_basic_prompt(query, results)

    def detect_query_type(self, query: str) -> str:
        """检测查询类型"""
        query_lower = query.lower()
        
        # 实现类查询
        if any(word in query_lower for word in ['implement', '实现', 'create', '创建', 'build', '构建', 'write', '写', 'add', '添加']):
            return 'implementation'
        
        # 解释类查询
        elif any(word in query_lower for word in ['explain', '解释', 'understand', '理解', 'what', '什么', 'how', '如何', 'why', '为什么']):
            return 'explanation'
        
        # 调试类查询
        elif any(word in query_lower for word in ['debug', '调试', 'fix', '修复', 'error', '错误', 'bug', '问题', 'issue']):
            return 'debugging'
        
        # 默认分析类查询
        else:
            return 'analysis'
    
    def _generate_basic_prompt(self, query: str, results: list) -> str:
        """生成基础提示词（回退方案）"""
        # 获取分词器
        tokenizer = self.model_manager.get_tokenizer()
        max_tokens = self.config.llm.max_tokens
        
        if tokenizer is None:
            def count_tokens(text):
                return len(text.split())
        else:
            def count_tokens(text):
                return len(tokenizer.encode(text, add_special_tokens=False))
        
        # 构建上下文
        context_str = ""
        total_tokens = 0
        
        for res in results:
            chunk_info = res.get('chunk', res)
            meta = chunk_info.get('metadata', {})
            
            snippet = f"--- Code Snippet from {meta.get('file_path', 'N/A')} ---\n"
            if meta.get('parent_class'):
                snippet += f"Class: {meta['parent_class']}\n"
            if meta.get('signature'):
                snippet += f"Signature: {meta['signature']}\n"
            if meta.get('complexity_score'):
                snippet += f"Complexity: {meta['complexity_score']:.2f}\n"
            
            snippet += chunk_info.get('code', '') + "\n\n"
            
            snippet_tokens = count_tokens(snippet)
            if total_tokens + snippet_tokens > max_tokens * 0.8:
                break
                
            context_str += snippet
            total_tokens += snippet_tokens
        
        prompt = f"""
You are an expert AI programming assistant.
Your task is to answer the user's question based *only* on the provided code snippets.
Synthesize the information from all snippets to provide a comprehensive answer.
If the provided context is insufficient to answer the question, state that you cannot answer based on the given information.

--- CONTEXT ---
{context_str.strip()}
--- END CONTEXT ---

USER'S QUESTION:
"{query}"

YOUR ANSWER:
"""
        
        logger.debug(f"Generated basic prompt with {total_tokens} context tokens")
        return prompt

# --- 用户交互函数 (保持独立，因为它们不依赖于助手实例的状态) ---

def display_results(results):
    """格式化并打印搜索结果。"""
    print(f"\nFound {len(results)} relevant code snippets:\n")
    for i, res in enumerate(results):
        # 处理混合检索器的双重嵌套结构
        if 'chunk' in res and isinstance(res['chunk'], dict):
            # 混合检索器格式：result['chunk']['chunk'] 才是真正的代码数据
            outer_chunk = res['chunk']
            if 'chunk' in outer_chunk and isinstance(outer_chunk['chunk'], dict):
                # 双重嵌套：取内层的chunk
                chunk_info = outer_chunk['chunk']
            else:
                # 单层嵌套：直接使用
                chunk_info = outer_chunk
            similarity = res.get('score', res.get('similarity', 0.0))
            retrieval_method = res.get('retrieval_method', 'unknown')
        else:
            # 旧格式：直接是chunk数据
            chunk_info = res
            similarity = res.get('similarity', 0.0)
            retrieval_method = 'semantic'

        # 获取元数据
        meta = chunk_info.get('metadata', {}) if isinstance(chunk_info, dict) else {}

        print(f"--- Result {i+1} | Similarity: {similarity:.4f} | Method: {retrieval_method} ---")
        print(f"File: {meta.get('file_path', 'N/A')}:{meta.get('start_line', '?')}")

        if 'parent_class' in meta:
            print(f"Context: Method in class '{meta['parent_class']}'")
        if 'signature' in meta:
            print(f"Signature: {meta['signature']}")

        # 显示检索方法信息（如果有）
        if 'retrieval_methods' in res:
            methods = res['retrieval_methods']
            method_info = []
            for method, score in methods.items():
                if score > 0:
                    method_info.append(f"{method}:{score:.3f}")
            if method_info:
                print(f"Methods: {', '.join(method_info)}")

        print("-" * 20)
        # 打印代码，并高亮显示行号
        code = chunk_info.get('code', '') if isinstance(chunk_info, dict) else ''
        if code:
            code_lines = code.split('\n')
            for line_num, line in enumerate(code_lines[:15], meta.get('start_line', 1)): # 最多显示15行
                print(f"{line_num:4d} | {line}")
            if len(code_lines) > 15:
                print("     | ... (code truncated)")
        else:
            print("     | (No code content available)")
        print("\n")

@performance_monitor("full_query")
def main():
    parser = argparse.ArgumentParser(description="AI Code Assistant: Ask questions about your codebase.")
    parser.add_argument("query", type=str, nargs='?', default="", help="Your question about the codebase.")
    parser.add_argument("--config", type=str, help="Path to config file")
    parser.add_argument("--benchmark", action="store_true", help="Run performance benchmark")
    parser.add_argument("--stats", action="store_true", help="Show performance statistics")
    args = parser.parse_args()
    
    # 加载配置
    if args.config:
        from config import reload_config
        reload_config(args.config)
    
    # 运行基准测试
    if args.benchmark:
        from benchmark import run_benchmark
        logger.info("Running performance benchmark...")
        run_benchmark()
        return
    
    try:
        # 初始化助手
        logger.info("Initializing CodeAssistant...")
        assistant = CodeAssistant()
        
        if args.query:
            # 单次查询模式
            results = assistant.dynamic_search(args.query)
            display_results(results)
            
            if results:
                final_prompt = assistant.generate_llm_prompt(args.query, results)
                assistant.generate_answer_with_llm(final_prompt)
            else:
                print("\nNo relevant code found for your question.")
        else:
            # 交互模式
            run_interactive_mode(assistant)
        
        # 显示统计信息
        if args.stats:
            show_performance_stats()
            
    except Exception as e:
        logger.error(f"Application failed: {e}")
        raise

def run_interactive_mode(assistant: CodeAssistant):
    """运行交互模式"""
    print("\nEntering interactive mode. Type your questions or 'exit' to quit.")
    print("Commands: 'exit', 'stats', 'help'")
    
    while True:
        try:
            query = input("\nAsk a question > ").strip()
            if not query:
                continue
                
            if query.lower() == 'exit':
                break
            elif query.lower() == 'stats':
                show_performance_stats()
                continue
            elif query.lower() == 'help':
                show_help()
                continue
            
            # 执行查询
            results = assistant.dynamic_search(query)
            
            if not results:
                print("\nSorry, I couldn't find any relevant code for your question.")
                continue
            
            display_results(results)
            
            final_prompt = assistant.generate_llm_prompt(query, results)
            assistant.generate_answer_with_llm(final_prompt)
            
        except (KeyboardInterrupt, EOFError):
            break
        except Exception as e:
            logger.error(f"Query failed: {e}")
            print(f"\nError processing query: {e}")
    
    print("\nExiting interactive mode.")

def show_performance_stats():
    """显示性能统计"""
    from logger import get_performance_stats
    
    stats = get_performance_stats()
    if not stats:
        print("\nNo performance statistics available.")
        return
    
    print("\n=== Performance Statistics ===")
    for logger_name, operations in stats.items():
        print(f"\n{logger_name}:")
        for op_name, op_stats in operations.items():
            print(f"  {op_name}:")
            print(f"    Count: {op_stats['count']}")
            print(f"    Total time: {op_stats['total']:.3f}s")
            print(f"    Average time: {op_stats['avg']:.3f}s")
            print(f"    Min time: {op_stats['min']:.3f}s")
            print(f"    Max time: {op_stats['max']:.3f}s")

def show_help():
    """显示帮助信息"""
    print("""
=== Help ===
Available commands:
  exit   - Exit the application
  stats  - Show performance statistics
  help   - Show this help message
  
To ask a question, simply type your question and press Enter.

Examples:
  > how does the chunking algorithm work?
  > implement a search function
  > what is the main class in this codebase?
""")

if __name__ == "__main__":
    main() 