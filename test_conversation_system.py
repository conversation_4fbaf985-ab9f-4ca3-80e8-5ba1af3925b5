#!/usr/bin/env python3
"""
测试对话系统功能
验证多轮对话、历史管理、引用显示等功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_conversation_components():
    """测试对话组件"""
    print("🧪 测试对话组件...")
    
    try:
        # 测试前端组件导入
        sys.path.insert(0, str(project_root / "frontend"))
        
        from api_service import SearchResult, QueryRequest
        from reference_viewer import (
            display_reference_panel, 
            display_reference_summary, 
            display_reference_filters,
            get_similarity_class,
            format_code_preview
        )
        
        print("✅ 引用查看器组件导入成功")
        
        # 测试工具函数
        assert get_similarity_class(0.9) == "similarity-high"
        assert get_similarity_class(0.7) == "similarity-medium"
        assert get_similarity_class(0.4) == "similarity-low"
        print("✅ 相似度分类函数测试通过")
        
        # 测试代码预览格式化
        test_code = "def test():\n    return True\n    # more code\n    pass\n    end"
        preview = format_code_preview(test_code, max_lines=3)
        assert "..." in preview
        print("✅ 代码预览格式化测试通过")
        
        return True
        
    except ImportError as e:
        print(f"❌ 组件导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        return False

def test_session_state_structure():
    """测试会话状态结构"""
    print("\n🧪 测试会话状态结构...")
    
    try:
        # 模拟Streamlit会话状态
        class MockSessionState:
            def __init__(self):
                self.conversation_history = []
                self.current_results = []
                self.current_query = ""
                self.is_generating = False
                self.conversation_id = 1
                self.show_references = True
                self.current_response = ""
                self.context_length = 10
        
        session_state = MockSessionState()
        
        # 测试添加对话消息
        session_state.conversation_history.append({
            "role": "user",
            "content": "测试问题",
            "timestamp": "12:00:00"
        })
        
        session_state.conversation_history.append({
            "role": "assistant",
            "content": "测试回答",
            "timestamp": "12:00:05",
            "results_count": 3
        })
        
        assert len(session_state.conversation_history) == 2
        assert session_state.conversation_history[0]["role"] == "user"
        assert session_state.conversation_history[1]["role"] == "assistant"
        print("✅ 会话状态结构测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 会话状态测试失败: {e}")
        return False

def test_search_result_structure():
    """测试搜索结果结构"""
    print("\n🧪 测试搜索结果结构...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import SearchResult
        
        # 创建测试搜索结果
        test_result = SearchResult(
            similarity=0.85,
            file_path="test/example.py",
            start_line=10,
            end_line=20,
            code="def test_function():\n    return True",
            signature="test_function()",
            parent_class="TestClass",
            retrieval_method="semantic",
            retrieval_methods={"semantic": 0.85, "keyword": 0.3}
        )
        
        assert test_result.similarity == 0.85
        assert test_result.file_path == "test/example.py"
        assert test_result.retrieval_method == "semantic"
        assert "semantic" in test_result.retrieval_methods
        print("✅ 搜索结果结构测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索结果测试失败: {e}")
        return False

def test_conversation_context():
    """测试对话上下文处理"""
    print("\n🧪 测试对话上下文处理...")
    
    try:
        # 模拟对话历史
        conversation_history = [
            {"role": "user", "content": "什么是代码分块？"},
            {"role": "assistant", "content": "代码分块是将大型代码文件分割成小块的技术..."},
            {"role": "user", "content": "如何实现代码分块？"},
            {"role": "assistant", "content": "可以使用AST解析器来实现代码分块..."},
            {"role": "user", "content": "有什么优化方法？"}
        ]
        
        # 测试上下文提取（最近4条消息）
        context_length = 4
        recent_context = conversation_history[-context_length:]
        
        assert len(recent_context) == 4
        assert recent_context[0]["role"] == "assistant"  # 第二个回答
        assert recent_context[-1]["role"] == "user"     # 最新问题
        print("✅ 对话上下文提取测试通过")
        
        # 测试上下文格式化
        formatted_context = []
        for msg in recent_context:
            formatted_context.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        assert len(formatted_context) == 4
        assert all("role" in msg and "content" in msg for msg in formatted_context)
        print("✅ 对话上下文格式化测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话上下文测试失败: {e}")
        return False

def test_reference_filtering():
    """测试引用过滤功能"""
    print("\n🧪 测试引用过滤功能...")
    
    try:
        sys.path.insert(0, str(project_root / "frontend"))
        from api_service import SearchResult
        
        # 创建测试数据
        test_results = [
            SearchResult(0.9, "file1.py", 1, 10, "code1", retrieval_method="semantic"),
            SearchResult(0.7, "file2.py", 11, 20, "code2", retrieval_method="keyword"),
            SearchResult(0.5, "file1.py", 21, 30, "code3", retrieval_method="semantic"),
            SearchResult(0.3, "file3.py", 31, 40, "code4", retrieval_method="hybrid")
        ]
        
        # 测试相似度过滤
        high_similarity = [r for r in test_results if r.similarity > 0.6]
        assert len(high_similarity) == 2
        print("✅ 相似度过滤测试通过")
        
        # 测试文件过滤
        file1_results = [r for r in test_results if "file1.py" in r.file_path]
        assert len(file1_results) == 2
        print("✅ 文件过滤测试通过")
        
        # 测试方法过滤
        semantic_results = [r for r in test_results if r.retrieval_method == "semantic"]
        assert len(semantic_results) == 2
        print("✅ 检索方法过滤测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 引用过滤测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n🧪 测试UI组件...")
    
    try:
        # 测试CSS类生成
        sys.path.insert(0, str(project_root / "frontend"))
        from reference_viewer import get_similarity_class
        
        # 测试不同相似度的CSS类
        test_cases = [
            (0.95, "similarity-high"),
            (0.75, "similarity-medium"),
            (0.45, "similarity-low"),
            (0.85, "similarity-high"),
            (0.65, "similarity-medium")
        ]
        
        for similarity, expected_class in test_cases:
            actual_class = get_similarity_class(similarity)
            assert actual_class == expected_class, f"相似度 {similarity} 应该返回 {expected_class}，但返回了 {actual_class}"
        
        print("✅ CSS类生成测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 对话系统功能测试")
    print("=" * 50)
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    # 运行测试
    tests = [
        ("对话组件", test_conversation_components),
        ("会话状态结构", test_session_state_structure),
        ("搜索结果结构", test_search_result_structure),
        ("对话上下文处理", test_conversation_context),
        ("引用过滤功能", test_reference_filtering),
        ("UI组件", test_ui_components)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 对话系统功能测试全部通过！")
        print("\n新功能特性:")
        print("  ✅ 多轮对话支持")
        print("  ✅ 对话历史管理")
        print("  ✅ 上下文感知")
        print("  ✅ 流式输出显示")
        print("  ✅ 代码引用面板")
        print("  ✅ 引用过滤和排序")
        print("  ✅ 对话中断控制")
        print("  ✅ 响应式布局")
        
        print("\n启动命令:")
        print("   python demo_streamlit.py")
        print("   或者: ./start_streamlit.sh")
    else:
        print("⚠️  部分测试失败，请检查实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
