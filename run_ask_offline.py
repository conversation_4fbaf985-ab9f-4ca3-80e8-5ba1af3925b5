#!/usr/bin/env python3
"""
离线模式运行ask.py
设置所有必要的环境变量来强制离线模式
"""

import os
import sys
from dotenv import load_dotenv

# 首先加载.env文件
load_dotenv()

def set_offline_environment():
    """设置离线环境变量"""
    offline_env = {
        # Transformers离线模式
        'TRANSFORMERS_OFFLINE': '1',
        'HF_HUB_OFFLINE': '1',
        'HF_HUB_DISABLE_TELEMETRY': '1',
        
        # 禁用网络请求
        'CURL_CA_BUNDLE': '',
        'REQUESTS_CA_BUNDLE': '',
        
        # 强制使用本地文件
        'HF_DATASETS_OFFLINE': '1',
        'TOKENIZERS_PARALLELISM': 'false',
        
        # 禁用自动更新
        'HF_HUB_DISABLE_PROGRESS_BARS': '1',
        'HF_HUB_DISABLE_SYMLINKS_WARNING': '1',
        'HF_HUB_DISABLE_EXPERIMENTAL_WARNING': '1',
    }
    
    # 更新环境变量
    for key, value in offline_env.items():
        os.environ[key] = value

def main():
    """主函数"""
    print("🚀 Setting up offline environment for ask.py...")
    
    # 设置离线环境
    set_offline_environment()
    
    # 获取命令行参数
    query = " ".join(sys.argv[1:]) if len(sys.argv) > 1 else ""
    
    if not query:
        print("Please provide a query. Example:")
        print("python run_ask_offline.py 'how does the code chunking work?'")
        return False
    
    print(f"Query: {query}")
    print("-" * 40)
    
    # 运行ask.py
    try:
        # 直接导入并运行，而不是subprocess
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # 模拟命令行参数
        sys.argv = ['ask.py', query]
        
        # 导入并运行ask
        import ask
        ask.main()
        
        return True
        
    except Exception as e:
        print(f"\n❌ Ask failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
