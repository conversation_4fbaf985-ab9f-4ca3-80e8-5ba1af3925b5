"""
代码依赖关系分析器
分析代码间的导入、调用、继承关系，构建依赖图以改善召回
"""

import os
import ast
import re
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict, deque
from dataclasses import dataclass
import networkx as nx
from tree_sitter import Language, Parser
import tree_sitter_python

from config import get_config
from logger import get_logger, performance_monitor

logger = get_logger(__name__)

@dataclass
class CodeEntity:
    """代码实体"""
    name: str
    type: str  # 'function', 'class', 'variable', 'import'
    file_path: str
    start_line: int
    end_line: int
    signature: Optional[str] = None
    docstring: Optional[str] = None
    parent: Optional[str] = None

@dataclass
class Dependency:
    """依赖关系"""
    source: str  # 源实体
    target: str  # 目标实体
    type: str    # 'import', 'call', 'inherit', 'reference'
    file_path: str
    line_number: int
    context: Optional[str] = None

class CodeDependencyAnalyzer:
    """代码依赖关系分析器"""
    
    def __init__(self):
        self.config = get_config()
        
        # 初始化解析器
        self.parser = Parser()
        py_language = Language(tree_sitter_python.language())
        self.parser.language = py_language
        
        # 存储分析结果
        self.entities: Dict[str, CodeEntity] = {}
        self.dependencies: List[Dependency] = []
        self.file_entities: Dict[str, List[str]] = defaultdict(list)
        
        # 依赖图
        self.dependency_graph = nx.DiGraph()
        self.call_graph = nx.DiGraph()
        self.import_graph = nx.DiGraph()
        
        # 缓存
        self.function_signatures = {}
        self.class_methods = defaultdict(list)
        self.module_exports = defaultdict(list)
    
    @performance_monitor("analyze_codebase")
    def analyze_codebase(self, codebase_path: str) -> Dict[str, Any]:
        """分析整个代码库的依赖关系"""
        logger.info(f"Analyzing codebase dependencies: {codebase_path}")
        
        # 1. 收集所有Python文件
        python_files = self._find_python_files(codebase_path)
        logger.info(f"Found {len(python_files)} Python files")
        
        # 2. 解析每个文件
        for file_path in python_files:
            try:
                self._analyze_file(file_path)
            except Exception as e:
                logger.warning(f"Failed to analyze {file_path}: {e}")
        
        # 3. 构建依赖图
        self._build_dependency_graphs()
        
        # 4. 分析依赖关系
        analysis_result = self._analyze_dependencies()
        
        logger.info(f"Dependency analysis complete: {len(self.entities)} entities, {len(self.dependencies)} dependencies")
        return analysis_result
    
    def _find_python_files(self, codebase_path: str) -> List[str]:
        """查找Python文件"""
        python_files = []
        ignore_patterns = self.config.system.ignore_patterns
        
        for root, dirs, files in os.walk(codebase_path):
            # 过滤忽略的目录
            dirs[:] = [d for d in dirs if not any(pattern in d for pattern in ignore_patterns)]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    python_files.append(file_path)
        
        return python_files
    
    @performance_monitor("analyze_file")
    def _analyze_file(self, file_path: str):
        """分析单个文件"""
        try:
            with open(file_path, 'rb') as f:
                code_bytes = f.read()
            
            # 使用tree-sitter解析
            tree = self.parser.parse(code_bytes)
            code_text = code_bytes.decode('utf-8', errors='ignore')
            
            # 分析不同类型的代码元素
            self._extract_entities(file_path, tree.root_node, code_text)
            self._extract_dependencies(file_path, tree.root_node, code_text)
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
    
    def _extract_entities(self, file_path: str, root_node, code_text: str):
        """提取代码实体"""
        lines = code_text.split('\n')
        
        def visit_node(node, parent_class=None):
            entity_id = None
            
            if node.type == 'function_definition':
                entity_id = self._extract_function(file_path, node, lines, parent_class)
            elif node.type == 'class_definition':
                entity_id = self._extract_class(file_path, node, lines)
                parent_class = entity_id  # 更新父类上下文
            elif node.type == 'import_statement' or node.type == 'import_from_statement':
                self._extract_import(file_path, node, lines)
            
            # 递归处理子节点
            for child in node.children:
                visit_node(child, parent_class)
        
        visit_node(root_node)
    
    def _extract_function(self, file_path: str, node, lines: List[str], parent_class: Optional[str] = None) -> str:
        """提取函数信息"""
        name_node = node.child_by_field_name('name')
        if not name_node:
            return None
        
        func_name = name_node.text.decode('utf-8')
        start_line = node.start_point[0] + 1
        end_line = node.end_point[0] + 1
        
        # 构建唯一ID
        if parent_class:
            entity_id = f"{parent_class}.{func_name}"
        else:
            entity_id = f"{file_path}:{func_name}"
        
        # 提取签名
        signature = self._extract_signature(node, lines)
        
        # 提取文档字符串
        docstring = self._extract_docstring(node)
        
        entity = CodeEntity(
            name=func_name,
            type='function',
            file_path=file_path,
            start_line=start_line,
            end_line=end_line,
            signature=signature,
            docstring=docstring,
            parent=parent_class
        )
        
        self.entities[entity_id] = entity
        self.file_entities[file_path].append(entity_id)
        
        # 缓存函数签名
        self.function_signatures[func_name] = entity_id
        
        if parent_class:
            self.class_methods[parent_class].append(entity_id)
        
        return entity_id
    
    def _extract_class(self, file_path: str, node, lines: List[str]) -> str:
        """提取类信息"""
        name_node = node.child_by_field_name('name')
        if not name_node:
            return None
        
        class_name = name_node.text.decode('utf-8')
        start_line = node.start_point[0] + 1
        end_line = node.end_point[0] + 1
        
        entity_id = f"{file_path}:{class_name}"
        
        # 提取类签名（包括继承）
        signature = self._extract_signature(node, lines)
        
        # 提取文档字符串
        docstring = self._extract_docstring(node)
        
        entity = CodeEntity(
            name=class_name,
            type='class',
            file_path=file_path,
            start_line=start_line,
            end_line=end_line,
            signature=signature,
            docstring=docstring
        )
        
        self.entities[entity_id] = entity
        self.file_entities[file_path].append(entity_id)
        
        return entity_id
    
    def _extract_import(self, file_path: str, node, lines: List[str]):
        """提取导入信息"""
        start_line = node.start_point[0] + 1
        import_text = lines[start_line - 1].strip()
        
        # 解析导入语句
        imported_modules = self._parse_import_statement(import_text)
        
        for module in imported_modules:
            dependency = Dependency(
                source=file_path,
                target=module,
                type='import',
                file_path=file_path,
                line_number=start_line,
                context=import_text
            )
            self.dependencies.append(dependency)
    
    def _extract_dependencies(self, file_path: str, root_node, code_text: str):
        """提取依赖关系"""
        lines = code_text.split('\n')
        
        # 查找函数调用
        self._extract_function_calls(file_path, root_node, lines)
        
        # 查找变量引用
        self._extract_variable_references(file_path, root_node, lines)
        
        # 查找类继承
        self._extract_class_inheritance(file_path, root_node, lines)
    
    def _extract_function_calls(self, file_path: str, node, lines: List[str], current_function: Optional[str] = None):
        """提取函数调用关系"""
        if node.type == 'function_definition':
            name_node = node.child_by_field_name('name')
            if name_node:
                current_function = name_node.text.decode('utf-8')
        
        elif node.type == 'call':
            function_node = node.child_by_field_name('function')
            if function_node:
                called_function = function_node.text.decode('utf-8')
                line_number = node.start_point[0] + 1
                
                # 记录函数调用依赖
                if current_function:
                    dependency = Dependency(
                        source=f"{file_path}:{current_function}",
                        target=called_function,
                        type='call',
                        file_path=file_path,
                        line_number=line_number,
                        context=lines[line_number - 1].strip() if line_number <= len(lines) else ""
                    )
                    self.dependencies.append(dependency)
        
        # 递归处理子节点
        for child in node.children:
            self._extract_function_calls(file_path, child, lines, current_function)
    
    def _extract_variable_references(self, file_path: str, node, lines: List[str]):
        """提取变量引用关系"""
        # 查找变量赋值和引用
        if node.type == 'assignment':
            line_number = node.start_point[0] + 1
            # 这里可以添加更详细的变量依赖分析
        
        for child in node.children:
            self._extract_variable_references(file_path, child, lines)
    
    def _extract_class_inheritance(self, file_path: str, root_node, lines: List[str]):
        """提取类继承关系"""
        def visit_node(node):
            if node.type == 'class_definition':
                name_node = node.child_by_field_name('name')
                superclasses_node = node.child_by_field_name('superclasses')
                
                if name_node and superclasses_node:
                    class_name = name_node.text.decode('utf-8')
                    line_number = node.start_point[0] + 1
                    
                    # 解析父类
                    superclasses_text = superclasses_node.text.decode('utf-8')
                    parent_classes = re.findall(r'\w+', superclasses_text)
                    
                    for parent_class in parent_classes:
                        dependency = Dependency(
                            source=f"{file_path}:{class_name}",
                            target=parent_class,
                            type='inherit',
                            file_path=file_path,
                            line_number=line_number,
                            context=lines[line_number - 1].strip() if line_number <= len(lines) else ""
                        )
                        self.dependencies.append(dependency)
            
            for child in node.children:
                visit_node(child)
        
        visit_node(root_node)
    
    def _extract_signature(self, node, lines: List[str]) -> str:
        """提取函数或类的签名"""
        start_line = node.start_point[0]
        end_line = node.end_point[0]
        
        # 找到函数体开始位置
        for i, child in enumerate(node.children):
            if child.type == 'block':
                signature_end_line = child.start_point[0]
                break
        else:
            signature_end_line = end_line
        
        # 提取签名行
        signature_lines = lines[start_line:signature_end_line]
        signature = ''.join(signature_lines).strip()
        
        # 清理签名（移除多余空白）
        signature = re.sub(r'\s+', ' ', signature)
        if signature.endswith(':'):
            signature = signature[:-1]
        
        return signature
    
    def _extract_docstring(self, node) -> Optional[str]:
        """提取文档字符串"""
        # 查找函数或类体中的第一个字符串字面量
        for child in node.children:
            if child.type == 'block':
                for grandchild in child.children:
                    if grandchild.type == 'expression_statement':
                        for ggchild in grandchild.children:
                            if ggchild.type == 'string':
                                docstring = ggchild.text.decode('utf-8')
                                # 清理文档字符串
                                docstring = docstring.strip('"""').strip("'''").strip('"').strip("'")
                                return docstring.strip()
        return None
    
    def _parse_import_statement(self, import_text: str) -> List[str]:
        """解析导入语句"""
        modules = []
        
        # 处理不同类型的导入
        if import_text.startswith('from '):
            # from module import item
            match = re.match(r'from\\s+(\\S+)\\s+import\\s+(.+)', import_text)
            if match:
                module = match.group(1)
                imports = match.group(2)
                modules.append(module)
                
                # 解析具体导入的项目
                items = [item.strip() for item in imports.split(',')]
                for item in items:
                    if ' as ' in item:
                        item = item.split(' as ')[0].strip()
                    modules.append(f"{module}.{item}")
        
        elif import_text.startswith('import '):
            # import module
            match = re.match(r'import\\s+(.+)', import_text)
            if match:
                imports = match.group(1)
                items = [item.strip() for item in imports.split(',')]
                for item in items:
                    if ' as ' in item:
                        item = item.split(' as ')[0].strip()
                    modules.append(item)
        
        return modules
    
    def _build_dependency_graphs(self):
        """构建依赖图"""
        logger.info("Building dependency graphs...")
        
        # 构建完整依赖图
        for dep in self.dependencies:
            self.dependency_graph.add_edge(dep.source, dep.target, 
                                         type=dep.type, 
                                         file_path=dep.file_path,
                                         line_number=dep.line_number)
            
            # 构建专门的调用图
            if dep.type == 'call':
                self.call_graph.add_edge(dep.source, dep.target)
            
            # 构建导入图
            elif dep.type == 'import':
                self.import_graph.add_edge(dep.source, dep.target)
    
    def _analyze_dependencies(self) -> Dict[str, Any]:
        """分析依赖关系"""
        analysis = {
            'entities_count': len(self.entities),
            'dependencies_count': len(self.dependencies),
            'files_count': len(self.file_entities),
            'dependency_types': defaultdict(int),
            'most_dependent_entities': [],
            'most_depended_entities': [],
            'circular_dependencies': [],
            'orphan_entities': []
        }
        
        # 统计依赖类型
        for dep in self.dependencies:
            analysis['dependency_types'][dep.type] += 1
        
        # 分析最依赖其他实体的实体
        out_degrees = dict(self.dependency_graph.out_degree())
        analysis['most_dependent_entities'] = sorted(
            out_degrees.items(), key=lambda x: x[1], reverse=True
        )[:10]
        
        # 分析被最多依赖的实体
        in_degrees = dict(self.dependency_graph.in_degree())
        analysis['most_depended_entities'] = sorted(
            in_degrees.items(), key=lambda x: x[1], reverse=True
        )[:10]
        
        # 检测循环依赖
        try:
            cycles = list(nx.simple_cycles(self.dependency_graph))
            analysis['circular_dependencies'] = cycles[:5]  # 只返回前5个
        except Exception:
            analysis['circular_dependencies'] = []
        
        # 查找孤立实体
        isolated_nodes = list(nx.isolates(self.dependency_graph))
        analysis['orphan_entities'] = isolated_nodes[:10]
        
        return analysis
    
    @performance_monitor("get_related_entities")
    def get_related_entities(self, entity_id: str, max_depth: int = 2, 
                           relation_types: Optional[Set[str]] = None) -> List[str]:
        """获取与指定实体相关的实体"""
        if entity_id not in self.dependency_graph:
            return []
        
        if relation_types is None:
            relation_types = {'call', 'import', 'inherit', 'reference'}
        
        related_entities = set()
        queue = deque([(entity_id, 0)])
        visited = {entity_id}
        
        while queue:
            current_entity, depth = queue.popleft()
            
            if depth >= max_depth:
                continue
            
            # 获取出边（当前实体依赖的实体）
            for target in self.dependency_graph.successors(current_entity):
                edge_data = self.dependency_graph.get_edge_data(current_entity, target)
                if edge_data and edge_data.get('type') in relation_types:
                    if target not in visited:
                        related_entities.add(target)
                        visited.add(target)
                        queue.append((target, depth + 1))
            
            # 获取入边（依赖当前实体的实体）
            for source in self.dependency_graph.predecessors(current_entity):
                edge_data = self.dependency_graph.get_edge_data(source, current_entity)
                if edge_data and edge_data.get('type') in relation_types:
                    if source not in visited:
                        related_entities.add(source)
                        visited.add(source)
                        queue.append((source, depth + 1))
        
        return list(related_entities)
    
    def get_entity_by_name(self, name: str) -> List[CodeEntity]:
        """根据名称查找实体"""
        results = []
        for entity in self.entities.values():
            if entity.name == name or name in entity.name:
                results.append(entity)
        return results
    
    def get_file_entities(self, file_path: str) -> List[CodeEntity]:
        """获取文件中的所有实体"""
        entity_ids = self.file_entities.get(file_path, [])
        return [self.entities[eid] for eid in entity_ids if eid in self.entities]
    
    def export_dependency_graph(self, output_file: str = "dependency_graph.json"):
        """导出依赖图"""
        import json
        
        graph_data = {
            'entities': {eid: {
                'name': entity.name,
                'type': entity.type,
                'file_path': entity.file_path,
                'start_line': entity.start_line,
                'end_line': entity.end_line
            } for eid, entity in self.entities.items()},
            'dependencies': [{
                'source': dep.source,
                'target': dep.target,
                'type': dep.type,
                'file_path': dep.file_path,
                'line_number': dep.line_number
            } for dep in self.dependencies]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Dependency graph exported to {output_file}")

# 全局实例
_dependency_analyzer = None

def get_dependency_analyzer() -> CodeDependencyAnalyzer:
    """获取依赖分析器"""
    global _dependency_analyzer
    if _dependency_analyzer is None:
        _dependency_analyzer = CodeDependencyAnalyzer()
    return _dependency_analyzer