#!/usr/bin/env python3
"""
Test script to verify that the model loading fix works correctly.
"""

import os
import sys
sys.path.append('.')

from model_manager import ModelManager
from config import get_config

def test_model_loading():
    """Test if models can be loaded in offline mode"""
    print("Testing model loading with offline mode...")
    
    # Load configuration
    config = get_config()
    
    print(f"Configuration loaded:")
    print(f"  - Model name: {config.model.name}")
    print(f"  - Cache dir: {config.model.cache_dir}")
    print(f"  - Local files only: {getattr(config.model, 'local_files_only', 'NOT SET')}")
    print(f"  - Offline mode: {getattr(config.model, 'offline_mode', 'NOT SET')}")

    # Debug: print all attributes of model config
    print(f"  - All model config attributes: {dir(config.model)}")
    
    # Initialize model manager
    model_manager = ModelManager()
    
    try:
        print("\nTesting UniXcoder model loading...")
        unixcoder = model_manager.get_unixcoder()
        print("✅ UniXcoder model loaded successfully!")
        
        print("\nTesting reranker model loading...")
        if config.retrieval.use_reranking:
            reranker = model_manager.get_reranker()
            print("✅ Reranker model loaded successfully!")
        else:
            print("⚠️  Reranking is disabled in config")
            
        print("\n🎉 All models loaded successfully in offline mode!")
        return True
        
    except Exception as e:
        print(f"❌ Error loading models: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading()
    sys.exit(0 if success else 1)
