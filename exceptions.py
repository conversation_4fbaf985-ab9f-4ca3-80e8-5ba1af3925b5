"""
统一异常处理体系

提供分层的异常类和上下文管理器，用于统一处理系统中的各种错误情况。
"""

import logging
import traceback
from contextlib import contextmanager
from typing import Any, Dict, Optional, Type, Union
from enum import Enum


class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误
    UNKNOWN_ERROR = "E000"
    CONFIGURATION_ERROR = "E001"
    RESOURCE_ERROR = "E002"
    
    # 模型相关错误
    MODEL_LOAD_ERROR = "E100"
    MODEL_INFERENCE_ERROR = "E101"
    MODEL_MEMORY_ERROR = "E102"
    
    # 索引相关错误
    INDEX_BUILD_ERROR = "E200"
    INDEX_LOAD_ERROR = "E201"
    INDEX_SEARCH_ERROR = "E202"
    
    # 数据处理错误
    DATA_PARSE_ERROR = "E300"
    DATA_CHUNK_ERROR = "E301"
    DATA_EMBEDDING_ERROR = "E302"
    
    # 检索相关错误
    RETRIEVAL_ERROR = "E400"
    RANKING_ERROR = "E401"
    FUSION_ERROR = "E402"


class BaseServiceError(Exception):
    """服务基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code.value,
            "message": self.message,
            "details": self.details,
            "cause": str(self.cause) if self.cause else None
        }
    
    def __str__(self) -> str:
        cause_str = f" (caused by: {self.cause})" if self.cause else ""
        return f"[{self.error_code.value}] {self.message}{cause_str}"


class ConfigurationError(BaseServiceError):
    """配置相关异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            error_code=ErrorCode.CONFIGURATION_ERROR,
            details={"config_key": config_key},
            **kwargs
        )


class ModelError(BaseServiceError):
    """模型相关异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            details={"model_name": model_name},
            **kwargs
        )


class ModelLoadError(ModelError):
    """模型加载异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            model_name=model_name,
            error_code=ErrorCode.MODEL_LOAD_ERROR,
            **kwargs
        )


class ModelInferenceError(ModelError):
    """模型推理异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            model_name=model_name,
            error_code=ErrorCode.MODEL_INFERENCE_ERROR,
            **kwargs
        )


class ModelMemoryError(ModelError):
    """模型内存异常"""
    
    def __init__(self, message: str, model_name: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            model_name=model_name,
            error_code=ErrorCode.MODEL_MEMORY_ERROR,
            **kwargs
        )


class IndexError(BaseServiceError):
    """索引相关异常"""
    
    def __init__(self, message: str, index_path: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            details={"index_path": index_path},
            **kwargs
        )


class IndexBuildError(IndexError):
    """索引构建异常"""
    
    def __init__(self, message: str, index_path: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            index_path=index_path,
            error_code=ErrorCode.INDEX_BUILD_ERROR,
            **kwargs
        )


class IndexLoadError(IndexError):
    """索引加载异常"""
    
    def __init__(self, message: str, index_path: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            index_path=index_path,
            error_code=ErrorCode.INDEX_LOAD_ERROR,
            **kwargs
        )


class IndexSearchError(IndexError):
    """索引搜索异常"""
    
    def __init__(self, message: str, query: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            error_code=ErrorCode.INDEX_SEARCH_ERROR,
            details={"query": query},
            **kwargs
        )


class DataProcessingError(BaseServiceError):
    """数据处理异常"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            details={"file_path": file_path},
            **kwargs
        )


class DataParseError(DataProcessingError):
    """数据解析异常"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            file_path=file_path,
            error_code=ErrorCode.DATA_PARSE_ERROR,
            **kwargs
        )


class DataChunkError(DataProcessingError):
    """数据分块异常"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            file_path=file_path,
            error_code=ErrorCode.DATA_CHUNK_ERROR,
            **kwargs
        )


class EmbeddingError(DataProcessingError):
    """嵌入生成异常"""
    
    def __init__(self, message: str, text: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            error_code=ErrorCode.DATA_EMBEDDING_ERROR,
            details={"text": text[:100] + "..." if text and len(text) > 100 else text},
            **kwargs
        )


class RetrievalError(BaseServiceError):
    """检索相关异常"""
    
    def __init__(self, message: str, query: Optional[str] = None, **kwargs):
        super().__init__(
            message,
            error_code=ErrorCode.RETRIEVAL_ERROR,
            details={"query": query},
            **kwargs
        )


class RankingError(RetrievalError):
    """排序异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            error_code=ErrorCode.RANKING_ERROR,
            **kwargs
        )


class FusionError(RetrievalError):
    """结果融合异常"""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            error_code=ErrorCode.FUSION_ERROR,
            **kwargs
        )


@contextmanager
def error_handler(
    logger: logging.Logger,
    operation: str,
    raise_on_error: bool = True,
    default_return: Any = None
):
    """
    统一错误处理上下文管理器
    
    Args:
        logger: 日志记录器
        operation: 操作描述
        raise_on_error: 是否在错误时抛出异常
        default_return: 错误时的默认返回值
    """
    try:
        yield
    except BaseServiceError as e:
        logger.error(f"Operation '{operation}' failed: {e}")
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"Error details: {e.to_dict()}")
        if raise_on_error:
            raise
        return default_return
    except Exception as e:
        logger.error(f"Unexpected error in operation '{operation}': {e}")
        logger.debug(f"Traceback: {traceback.format_exc()}")
        
        # 包装为基础服务异常
        service_error = BaseServiceError(
            f"Unexpected error in {operation}: {str(e)}",
            cause=e
        )
        
        if raise_on_error:
            raise service_error from e
        return default_return


@contextmanager
def resource_manager(*resources, cleanup_func=None):
    """
    资源管理上下文管理器
    
    Args:
        *resources: 需要管理的资源
        cleanup_func: 自定义清理函数
    """
    try:
        yield resources if len(resources) > 1 else (resources[0] if resources else None)
    finally:
        if cleanup_func:
            try:
                cleanup_func(*resources)
            except Exception as e:
                logging.getLogger(__name__).warning(f"Resource cleanup failed: {e}")
        
        # 默认清理逻辑
        for resource in resources:
            try:
                if hasattr(resource, 'close'):
                    resource.close()
                elif hasattr(resource, 'cleanup'):
                    resource.cleanup()
                elif hasattr(resource, '__exit__'):
                    resource.__exit__(None, None, None)
            except Exception as e:
                logging.getLogger(__name__).warning(f"Failed to cleanup resource {resource}: {e}")


def handle_errors(
    *exception_types: Type[Exception],
    logger: Optional[logging.Logger] = None,
    default_return: Any = None,
    reraise_as: Optional[Type[BaseServiceError]] = None
):
    """
    错误处理装饰器
    
    Args:
        *exception_types: 要捕获的异常类型
        logger: 日志记录器
        default_return: 错误时的默认返回值
        reraise_as: 重新抛出的异常类型
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exception_types as e:
                if logger:
                    logger.error(f"Error in {func.__name__}: {e}")
                
                if reraise_as:
                    raise reraise_as(f"Error in {func.__name__}: {str(e)}", cause=e) from e
                
                return default_return
            except Exception as e:
                if logger:
                    logger.error(f"Unexpected error in {func.__name__}: {e}")
                    logger.debug(f"Traceback: {traceback.format_exc()}")
                
                if reraise_as:
                    raise reraise_as(f"Unexpected error in {func.__name__}: {str(e)}", cause=e) from e
                
                raise
        
        return wrapper
    return decorator