"""
缓存管理系统

提供多层次、高效的缓存管理，支持LRU、TTL等多种缓存策略。
"""

import time
import threading
import pickle
import hashlib
from abc import ABC, abstractmethod
from collections import OrderedDict
from contextlib import contextmanager
from typing import Any, Dict, Optional, Union, Callable, Tuple
from dataclasses import dataclass
import weakref
import psutil

from logger import get_logger
from exceptions import BaseServiceError, ErrorCode

logger = get_logger(__name__)


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size: int = 0
    memory_usage: int = 0
    
    @property
    def hit_rate(self) -> float:
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def reset(self):
        """重置统计信息"""
        self.hits = 0
        self.misses = 0
        self.evictions = 0


class CacheItem:
    """缓存项"""
    
    def __init__(self, key: str, value: Any, ttl: Optional[float] = None):
        self.key = key
        self.value = value
        self.created_at = time.time()
        self.last_accessed = self.created_at
        self.access_count = 0
        self.ttl = ttl
        self.size = self._calculate_size(value)
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小（字节）"""
        try:
            return len(pickle.dumps(value))
        except Exception:
            # 如果无法序列化，使用近似估算
            if hasattr(value, '__sizeof__'):
                return value.__sizeof__()
            return 1024  # 默认1KB
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def access(self):
        """记录访问"""
        self.last_accessed = time.time()
        self.access_count += 1


class BaseCache(ABC):
    """缓存基类"""
    
    def __init__(self, name: str = "cache"):
        self.name = name
        self.stats = CacheStats()
        self._lock = threading.RLock()
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """存储缓存值"""
        pass
    
    @abstractmethod
    def remove(self, key: str) -> bool:
        """删除缓存项"""
        pass
    
    @abstractmethod
    def clear(self):
        """清空缓存"""
        pass
    
    @abstractmethod
    def size(self) -> int:
        """获取缓存大小"""
        pass
    
    def contains(self, key: str) -> bool:
        """检查是否包含key"""
        return self.get(key) is not None
    
    def get_stats(self) -> CacheStats:
        """获取统计信息"""
        with self._lock:
            return CacheStats(
                hits=self.stats.hits,
                misses=self.stats.misses,
                evictions=self.stats.evictions,
                size=self.size(),
                memory_usage=self._get_memory_usage()
            )
    
    def _get_memory_usage(self) -> int:
        """获取内存使用量"""
        return 0  # 子类实现


class LRUCache(BaseCache):
    """LRU缓存实现"""
    
    def __init__(
        self,
        max_size: int = 1000,
        max_memory_mb: int = 512,
        ttl: Optional[float] = None,
        name: str = "lru_cache"
    ):
        super().__init__(name)
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.default_ttl = ttl
        self._cache: OrderedDict[str, CacheItem] = OrderedDict()
        self._memory_usage = 0
    
    def get(self, key: str) -> Optional[Any]:
        with self._lock:
            item = self._cache.get(key)
            
            if item is None:
                self.stats.misses += 1
                return None
            
            # 检查是否过期
            if item.is_expired():
                self._remove_item(key)
                self.stats.misses += 1
                return None
            
            # 更新访问信息并移动到末尾（最近使用）
            item.access()
            self._cache.move_to_end(key)
            self.stats.hits += 1
            
            return item.value
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        with self._lock:
            # 使用默认TTL如果未指定
            if ttl is None:
                ttl = self.default_ttl
            
            # 创建新的缓存项
            item = CacheItem(key, value, ttl)
            
            # 检查是否已存在
            if key in self._cache:
                old_item = self._cache[key]
                self._memory_usage -= old_item.size
            
            # 检查内存限制
            if self._memory_usage + item.size > self.max_memory_bytes:
                self._evict_for_memory(item.size)
            
            # 检查大小限制
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            # 添加新项
            self._cache[key] = item
            self._memory_usage += item.size
            
            return True
    
    def remove(self, key: str) -> bool:
        with self._lock:
            return self._remove_item(key)
    
    def _remove_item(self, key: str) -> bool:
        """内部删除方法"""
        item = self._cache.pop(key, None)
        if item:
            self._memory_usage -= item.size
            return True
        return False
    
    def clear(self):
        with self._lock:
            self._cache.clear()
            self._memory_usage = 0
            self.stats.reset()
    
    def size(self) -> int:
        return len(self._cache)
    
    def _get_memory_usage(self) -> int:
        return self._memory_usage
    
    def _evict_lru(self):
        """移除最少使用的项"""
        if self._cache:
            key, item = self._cache.popitem(last=False)
            self._memory_usage -= item.size
            self.stats.evictions += 1
            logger.debug(f"Evicted LRU item: {key}")
    
    def _evict_for_memory(self, needed_size: int):
        """为新项腾出内存空间"""
        target_memory = self.max_memory_bytes - needed_size
        
        while self._memory_usage > target_memory and self._cache:
            key, item = self._cache.popitem(last=False)
            self._memory_usage -= item.size
            self.stats.evictions += 1
            logger.debug(f"Evicted for memory: {key}")
    
    def cleanup_expired(self):
        """清理过期项"""
        with self._lock:
            expired_keys = [
                key for key, item in self._cache.items()
                if item.is_expired()
            ]
            
            for key in expired_keys:
                self._remove_item(key)
                self.stats.evictions += 1
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired items")


class TwoLevelCache(BaseCache):
    """两级缓存：内存 + 磁盘"""
    
    def __init__(
        self,
        l1_cache: BaseCache,
        l2_cache: Optional[BaseCache] = None,
        name: str = "two_level_cache"
    ):
        super().__init__(name)
        self.l1_cache = l1_cache
        self.l2_cache = l2_cache
    
    def get(self, key: str) -> Optional[Any]:
        # 先从L1缓存获取
        value = self.l1_cache.get(key)
        if value is not None:
            self.stats.hits += 1
            return value
        
        # 从L2缓存获取
        if self.l2_cache:
            value = self.l2_cache.get(key)
            if value is not None:
                # 提升到L1缓存
                self.l1_cache.put(key, value)
                self.stats.hits += 1
                return value
        
        self.stats.misses += 1
        return None
    
    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        # 存储到L1缓存
        success = self.l1_cache.put(key, value, ttl)
        
        # 可选：同时存储到L2缓存
        if self.l2_cache:
            self.l2_cache.put(key, value, ttl)
        
        return success
    
    def remove(self, key: str) -> bool:
        success1 = self.l1_cache.remove(key)
        success2 = self.l2_cache.remove(key) if self.l2_cache else True
        return success1 or success2
    
    def clear(self):
        self.l1_cache.clear()
        if self.l2_cache:
            self.l2_cache.clear()
        self.stats.reset()
    
    def size(self) -> int:
        return self.l1_cache.size()


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self._caches: Dict[str, BaseCache] = {}
        self._lock = threading.RLock()
        self._cleanup_thread = None
        self._running = False
    
    def create_cache(
        self,
        name: str,
        cache_type: str = "lru",
        **kwargs
    ) -> BaseCache:
        """创建缓存实例"""
        with self._lock:
            if name in self._caches:
                logger.warning(f"Cache '{name}' already exists")
                return self._caches[name]
            
            if cache_type == "lru":
                cache = LRUCache(name=name, **kwargs)
            else:
                raise ValueError(f"Unsupported cache type: {cache_type}")
            
            self._caches[name] = cache
            logger.info(f"Created cache '{name}' of type '{cache_type}'")
            
            # 启动清理线程
            self._start_cleanup_thread()
            
            return cache
    
    def get_cache(self, name: str) -> Optional[BaseCache]:
        """获取缓存实例"""
        return self._caches.get(name)
    
    def remove_cache(self, name: str) -> bool:
        """删除缓存实例"""
        with self._lock:
            cache = self._caches.pop(name, None)
            if cache:
                cache.clear()
                logger.info(f"Removed cache '{name}'")
                return True
            return False
    
    def get_all_stats(self) -> Dict[str, CacheStats]:
        """获取所有缓存的统计信息"""
        return {name: cache.get_stats() for name, cache in self._caches.items()}
    
    def cleanup_all(self):
        """清理所有缓存的过期项"""
        for cache in self._caches.values():
            if hasattr(cache, 'cleanup_expired'):
                cache.cleanup_expired()
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        if not self._running:
            self._running = True
            self._cleanup_thread = threading.Thread(
                target=self._cleanup_worker,
                daemon=True
            )
            self._cleanup_thread.start()
    
    def _cleanup_worker(self):
        """清理工作线程"""
        while self._running:
            try:
                time.sleep(300)  # 每5分钟清理一次
                self.cleanup_all()
            except Exception as e:
                logger.error(f"Cache cleanup error: {e}")
    
    def shutdown(self):
        """关闭管理器"""
        self._running = False
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=1)
        
        for cache in self._caches.values():
            cache.clear()
        self._caches.clear()


# 全局缓存管理器实例
_cache_manager = None
_cache_manager_lock = threading.Lock()


def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器"""
    global _cache_manager
    
    if _cache_manager is None:
        with _cache_manager_lock:
            if _cache_manager is None:
                _cache_manager = CacheManager()
    
    return _cache_manager


def cached(
    cache_name: str = "default",
    ttl: Optional[float] = None,
    key_func: Optional[Callable] = None,
    max_size: int = 1000,
    max_memory_mb: int = 512
):
    """
    缓存装饰器
    
    Args:
        cache_name: 缓存名称
        ttl: 生存时间（秒）
        key_func: 自定义key生成函数
        max_size: 最大缓存项数
        max_memory_mb: 最大内存使用（MB）
    """
    def decorator(func):
        # 获取或创建缓存
        cache_manager = get_cache_manager()
        cache = cache_manager.get_cache(cache_name)
        
        if cache is None:
            cache = cache_manager.create_cache(
                cache_name,
                max_size=max_size,
                max_memory_mb=max_memory_mb,
                ttl=ttl
            )
        
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认键生成
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = hashlib.md5("|".join(key_parts).encode()).hexdigest()
            
            # 尝试从缓存获取
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.put(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


@contextmanager
def memory_limit_context(limit_mb: int):
    """内存限制上下文管理器"""
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024
    
    try:
        yield
    finally:
        current_memory = process.memory_info().rss / 1024 / 1024
        memory_used = current_memory - initial_memory
        
        if memory_used > limit_mb:
            logger.warning(
                f"Memory usage ({memory_used:.1f}MB) exceeded limit ({limit_mb}MB)"
            )
            
            # 触发缓存清理
            cache_manager = get_cache_manager()
            cache_manager.cleanup_all()